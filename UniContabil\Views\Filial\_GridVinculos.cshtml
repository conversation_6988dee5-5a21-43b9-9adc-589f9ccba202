﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model List<OrganizacaoUsuarioDetails>

@{
  ViewData["Title"] = "Usuario";
}
<style>
  .selects {
    margin-left: 5pt;
    margin-top: 5pt;
    height: 13px !important;
  }

  .divtable {
    overflow-y: auto;
    max-height: 250px;
    margin-bottom: 10px;
  }

    .divtable::-webkit-scrollbar {
      width: 2px;
    }

    .divtable::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    }

    .divtable::-webkit-scrollbar-thumb {
      background-color: #afafaf;
      outline: 1px solid #adabab;
      border-radius: 10px;
    }

  .table-group .table-group-content table th {
    font-size: 15pt !important;
    color: black !important;
  }
</style>
@if (Model.Count > 0)
{

  <div class="table-group card">
    <div class="table-group-content">
      <div class="table-responsive">
        <table id="filiais-table">
          <thead>
            <tr>
              <th>
              </th>
            </tr>
            <tr>
              <th>
                Nome
              </th>
              <th>
                CPF
              </th>
              <th>
                E-mail
              </th>
              <th></th>
            </tr>

          </thead>

          <tbody>
            @for (int i = 0; i < Model.Count; i++)
            @*@foreach (var item in Model)*@
            {
              <tr data-trfilial="@Model[i].Id">
                @Html.HiddenFor(a => a[i].Id)
                @Html.HiddenFor(a => a[i].Nome)
                <td>
                  @*@Html.CheckBoxFor(a => a[i].Check, new { @style = "margin-left: 5pt;margin - top: 5pt; height: 13px !important;", @class = "selects", @id = string.Format("selectfilial_{0}", i) })*@
                  <span> @Model[i].Nome </span>
                </td>
                <td> <span> @Model[i].CPF </span></td>
                <td> <span> @Model[i].Email </span></td>
                <td>
                  @if (ContextoUsuario.UserLogged.Contabilidade == null)
                  {
                    <button class="btn btn-outline-danger btn-sm" onclick="Filial.DeleteAssociado('@Model[i].IdUsuario')" id="" style="height: 28px;margin-left: 2px;">
                      Remover
                    </button>
                  }
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
    </div>
  </div>
}

<script src="~/Views/Usuarios/GridFiliais.js?ds=bh"></script>
