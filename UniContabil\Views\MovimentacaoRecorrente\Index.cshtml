﻿@using UniContabilEntidades.Models.DataBase
@model List<E_MovimentacaoRecorrente>
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;
@{
  ViewBag.Title = "Movimentações";
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();

}
<script src="~/Views/MovimentacaoRecorrente/MovimentacaoRecorrente.js?123=123"></script>
<div class="table-group card">
  <div class="table-group card-header" id="CabecalhoPedidoCompra">
    <div class="row col-md-12">
      <div class="form-group col-md-6">
        <label>Pesquisar:</label>
        @using (Html.BeginForm("Index", "MovimentacaoRecorrente", FormMethod.Get))
        {

          <input type="text" class="form-control" placeholder="Aperte enter para pesquisar" name="search" value="@ViewBag.Search" />
        }
      </div>
      <div class="form-group col-md-6" style=" display: flex; justify-content: flex-end;">
        <a class="btn btn-outline-success" href="@Url.Action("create")" style=" margin-top: 27px;">Novo</a>
      </div>
    </div>

    <div class="card-options">
      @*@if (ContextoUsuario.HasPermission("PedidoCompra", TipoFuncao.Create))
        {
          <button type="button" class="btn btn-success" onclick="location.href='@Url.Action("Create", "NotaFiscalFat")'">Adicionar Nota</button>
        }*@
    </div>
  </div>
  <div class="table-group-content" style="margin-top: 30px;">
    <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
      <thead>
        <tr>
          <th>
            Descrição
          </th>
          <th>
            Data Inicial
          </th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        @foreach (E_MovimentacaoRecorrente Movimentacao in Model)
        {
          <tr>
            <td>
              @Movimentacao.MR_Descricao
            </td>
            <td>
              @Movimentacao.MR_DataInicial.ToString("dd/MM/yyyy")
            </td>
            <td>
              <a class="btn btn-outline-info" href="@Url.Action("Edit", new { id = Movimentacao.MR_Id })">Editar</a>
              <button class="link link-danger" onclick="MovimentacaoRecorrente.Delete(@("'" + Movimentacao.MR_Id + "'"))">Apagar</button>
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
  <div class="card-footer">
  </div>
</div>