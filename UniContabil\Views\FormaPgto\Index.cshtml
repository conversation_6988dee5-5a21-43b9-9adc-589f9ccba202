﻿@model UniContabilEntidades.Models.FormaPgtoModel
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;


@{
  ViewData["Title"] = "Index";
  Layout = "~/Views/Shared/_Layout.cshtml";
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();
}
<h1>@Html.DisplayNameFor(model => model)</h1>
@if (ContextoUsuario.HasPermission(controllerName, TipoFuncao.Create))
{
  <p>
    <a asp-action="Create">+ Novo</a>
  </p>
}
<table class="table">
  <thead>
    <tr>
      @*<th>
        @Html.DisplayNameFor(model => model.NomeOrganizacao)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.NomeFilial)
      </th>*@
      <th>
        @Html.DisplayNameFor(model => model.Descricao)
      </th>
      <th></th>
    </tr>
  </thead>
  <tbody>
    @foreach (UniContabilEntidades.Models.FormaPgtoModel item in ViewBag.PageItems)
    {
    <tr>
      @*<td>
      @Html.DisplayFor(modelItem => item.NomeOrganizacao)
    </td>
    <td>
      @Html.DisplayFor(modelItem => item.NomeFilial)
    </td>*@
      <td>
        @Html.DisplayFor(modelItem => item.Descricao)
      </td>
      <td>
        @if (ContextoUsuario.HasPermission(controllerName, TipoFuncao.Edit))
        {
          <a class="btn btn-secondary" asp-action="Edit" asp-route-id="@item.Id">Editar</a>
        }
        @if (ContextoUsuario.HasPermission(controllerName, TipoFuncao.Delete))
        {
          <button class="btn btn-danger" onclick="DeleteItem('@this.ViewContext.RouteData.Values["controller"].ToString()', @item.Id)">Apagar</button>
        }
      </td>
    </tr>
    }
  </tbody>
</table>
@Html.PagedListPager((IPagedList)ViewBag.PageItems, page => Url.Action("Index", new { page }),
  new PagedListRenderOptions
  {
    LiElementClasses = new string[] { "page-item" },
    PageClasses = new string[] { "page-link" },
    Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
    MaximumPageNumbersToDisplay = 5
  })

