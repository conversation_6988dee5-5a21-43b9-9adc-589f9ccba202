﻿@model List<UniContabilEntidades.Models.TipoDocumentoFilialListModel>

@{
  ViewData["Title"] = "Documentos";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

<script src="~/Views/DocumentoFilial/DocumentoFilial.js?as=dga"></script>
<link href="~/Views/DocumentoFilial/DocumentoFilial.css?as=doa" rel="stylesheet" />

<div class="col-md-12">
  <div class="row" style="justify-content:space-between">

    <div class="table-group col-md-3 card" style="max-width: 24% !important; height: calc(100vh - 80px) !important; max-height: calc(100vh - 80px); overflow: auto">
      <div class="row" style="justify-content:space-between">
        <a class="btn btn-success" id="openModalCreatePasta" style="margin-bottom: 10px;"><i class="mw-drip-plus"></i><i class="mw-drip-folder"></i></a>
        <a class="btn btn-success" id="openModalCreateDoc" style=" height: 38px !important;" hidden="hidden"><i class="mw-drip-document-new"></i></a>
      </div>
      <table class="table table-borderless scroll-smooth">
        <tbody>
          @foreach (UniContabilEntidades.Models.TipoDocumentoFilialListModel document in Model.OrderBy(a => a.Descricao))
          {
            <tr class="DocumentoSelecionado" data-idtipo="@document.IdDocumento">
              <td><i class="mw-drip-folder"></i> @Html.DisplayFor(modelItem => document.Descricao)</td>
            </tr>
          }
        </tbody>
      </table>
    </div>

    <div class="col-md-9 card" style="width: 100%; height: calc(100vh - 80px) !important; max-height: calc(100vh - 80px);">
      <div class="row">
        <input type="hidden" id="idtipo" />
        <div class="col-md-12" id="Divsearch" style="margin-bottom: 10px;" hidden="hidden">
          <div class="row" style="align-items: center;">
            <div class="col-md-3">
              <div class="form-group">
                <label>Data</label>
                <input type="date" class="form-control" id="dataSearch" />
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label>Descrição</label>
                <input type="text" class="form-control" id="descSearch" />
              </div>
            </div>
            <div class="col-md-2" style="margin-top: 25px;">
              <button class="btn btn-info" id="searchDocs">Pesquisar</button>
            </div>
          </div>
        </div>

      </div>
      <div class="row">
        <div class="col-md-12" id="gridDocumentos" style="max-height: calc(100vh - 80px); overflow: auto">
        </div>
      </div>
    </div>
  </div>
</div>
@Html.Partial("_Modal")

<style>
  table {
    font-size: 8pt !important;
  }

  table th, table td {
    padding: 0 0 0 5px !important;
  }
</style>
