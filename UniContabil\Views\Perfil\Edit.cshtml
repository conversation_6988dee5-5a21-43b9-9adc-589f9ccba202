﻿@model UniContabilEntidades.Models.UsuarioEditModel
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Editar " + @Html.DisplayNameFor(model => model);
  Layout = "~/Views/Shared/_Layout.cshtml";
}
<script src="~/Views/Perfil/Perfil.js?ac=a"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>

<div class="card" hidden="hidden">
  @using (Html.BeginForm("Edit", "Perfil", FormMethod.Post))
  {
    <div class="card-body">
      @Html.HiddenFor(a => a.Id)

      <div class="form-group">
        @Html.LabelFor(a => a.Nome)
        @Html.TextBoxFor(a => a.Nome, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.Nome)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.CPF)
        @Html.TextBoxFor(a => a.CPF, new { @class = "form-control CPF" })
        @Html.ValidationMessageFor(a => a.CPF)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.Email)
        @Html.TextBoxFor(a => a.Email, new { @class = "form-control EMAIL" })
        @Html.ValidationMessageFor(a => a.Email)
      </div>

      <div class="form-group">
        @Html.LabelFor(a => a.TelefoneCelular)
        @Html.TextBoxFor(a => a.TelefoneCelular, new { @class = "form-control telefone" })
        @Html.ValidationMessageFor(a => a.TelefoneCelular)
      </div>

      <div class="form-group">
        @Html.LabelFor(a => a.TelefoneFixo)
        @Html.TextBoxFor(a => a.TelefoneFixo, new { @class = "form-control telefone2" })
        @Html.ValidationMessageFor(a => a.TelefoneFixo)
      </div>
    </div>
    <div class="card-footer">
      <a asp-action="Index" class="link link-info">Voltar</a>
      <button type="submit" class="btn btn-success">Salvar</button>
    </div>
  }
</div>

<div class="card">
  <ul class="nav nav-tabs" id="myTab" role="tablist">
    <li class="nav-item" role="presentation">
      <a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab" aria-controls="home" aria-selected="true">Dependentes</a>
    </li>
  </ul>
  <div class="tab-content" id="myTabContent">
    <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
      <div class="card">
        <div class="row">
          <a class="btn btn-success" id="createDependente" style=" margin-left: 10px; margin-top:10px">+ Novo</a>
        </div>
        <div id="DependentesPartial">
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="modalDependentesCreate" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Criar Dependente </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        @await Html.PartialAsync("_partialCreateDependentes", new DependentesModel())
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
        <button type="button" class="btn btn-primary" id="enviarDependente">Criar</button>
      </div>
    </div>
  </div>
</div>

<style>
  /* .modal {
    padding: 0 !important;
  }

    .modal .modal-dialog {
      width: 100%;
      max-width: none;
      height: 100%;
      margin: 0;
    }

    .modal .modal-content {
      height: 97%;
      border: 0;
      border-radius: 0;
    }

    .modal .modal-body {
      overflow-y: auto;
    }*/
  .modal .modal-dialog {
    width: 60%;
  }
</style>
<script>
  $(document).ready(function () {
    
  });
  $("#CPF").attr("readonly", true);
  $("#Nome").attr("readonly", true);
</script>