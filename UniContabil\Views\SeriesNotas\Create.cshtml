﻿@model UniContabilEntidades.Models.SeriesNotasModel
@using UniContabil.Infrastructure.Controls

    @{
    ViewData["Title"] = "Novo " + @Html.DisplayNameFor(model => model);
    }
  <div class="card">
    <form asp-action="Create">
      <div class="card-header">
        <div class="card-title">
          Novo @Html.DisplayNameFor(model => model)
        </div>
        <div class="card-options">
        </div>
      </div>
      <div class="card-body">
        @Html.AntiForgeryToken()
        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
              <div class="form-group">
                <label asp-for="Serie" class="control-label"></label>
                <input asp-for="Serie" class="form-control" />
                <span asp-validation-for="Serie" class="text-danger"></span>
              </div>
      </div>
      <div class="card-footer">
        <a asp-action="Index" class="link link-info">Voltar</a>
        <input type="submit" value="Adicionar" class="btn btn-primary" />
      </div>
    </form>
  </div>

