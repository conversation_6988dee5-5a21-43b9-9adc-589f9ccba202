﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure.Controls
@model List<NotaFiscalFatIndexModel>
@{
  ViewBag.Title = "Nota Fiscal Faturamento";
  //Layout = "~/Views/Shared/_LayoutAccount.cshtml";
}

<script src="~/Views/NotaFiscalFat/NotaFiscalFat.js?ds=sd"></script>


<div class="table-group  card" id="CabecalhoPedidoCompra">
  @using (Html.BeginForm("Index", "NotaFiscalFat"))
  {
    <div class="card-header">
      <div class="card-title">@Html.DisplayNameFor(model => model)</div>
      <div class="card-options">
        
      </div>
    </div>

    <div class="table-group-content">

      <div id="TableTela">
        <table >
          <thead>
            <tr>
              <th>Número Nota</th>
              <th>Serie </th>
              <th>
                Organizacao
              </th>
              <th>
                Cliente
              </th>
              <th>
                Valor Total
              <th>
                Desconto Total
              </th>
              <th>
                Data de Emissão
              </th>
              <th>
              </th>
            </tr>
          </thead>
          <tbody>
            @foreach (NotaFiscalFatIndexModel NotaFiscal in Model)
            {
            <tr>
              <td>@NotaFiscal.NumNotaEnvio</td>
              <td>
                @NotaFiscal.Serie
              </td>
              <td>
                @NotaFiscal.Organizacao
              </td>
              <td>
                @if (string.IsNullOrEmpty(NotaFiscal.Cliente))
                {
                  <text> - </text>
                }
                else
                {
                  <text> @NotaFiscal.Cliente </text>
                }
              </td>
              <td>
                @if (NotaFiscal.ValorTotal != 0)
                {
                  <text>R$ @NotaFiscal.ValorTotal</text>
                }
                else
                {
                  <text>R$ 0,00</text>
                }
              </td>
              <td>
                @if (!NotaFiscal.DescontoTotal.HasValue)
                {
                  <text> R$ 0,00 </text>
                }
                else
                {
                  <text>R$ @NotaFiscal.DescontoTotal </text>
                }
              </td>
              <td>@NotaFiscal.DtEmissao.ToString("dd/MM/yyyy")</td>
              <td style="width:100px">
                <a href="@Url.Action("Edit", "NotaFiscalFat", new { id = @NotaFiscal.Codigo})" class="btn btn-outline-warning btn-circle btn-sm" title="Editar">
                  Editar
                </a>
                @*<button type="button" class="btn btn-primary TransmitirNFSe" data-id="@NotaFiscal.Codigo">Transmitir NFSe</button>*@
              </td>
            </tr>
            }
          </tbody>
        </table>
      </div>

    </div>
    <div class=" footer">

    </div>
  }

</div>

@*<div class="container" id="CabecalhoPedidoCompra">
  @using (Html.BeginForm("Index", "NotaFiscalFat"))
  {*@
    @*<div class="row">
        <button onclick="location.href='@Url.Action("Create", "NotaFiscalFat")'" type="button" class="btn btn-primary" id="Adicionar">Criar Nota </button>
      </div>*@
    @*<div id="TableTela">
      <table class="table table-striped table-sm">
        <thead>
          <tr>
            <th>Código</th>
            <th>
              Organizacao
            </th>
            <th>
              Cliente
            </th>
            <th>
              Serie
            </th>
            <th>
              Valor Total
            <th>
              Desconto Total
            </th>
            <th>
              Data de Emissão
            </th>
            <th>
            </th>
          </tr>
        </thead>
        <tbody>
          @foreach (NotaFiscalFatIndexModel NotaFiscal in Model)
          {
            <tr>
              <td>@NotaFiscal.CodigoIdent</td>
              <td>
                @NotaFiscal.Organizacao
              </td>
              <td>
                @if (string.IsNullOrEmpty(NotaFiscal.Cliente))
                {
                  <text> - </text>
                }
                else
                {
                  <text> @NotaFiscal.Cliente </text>
                }
              </td>
              <td>
                @NotaFiscal.Serie
              </td>
              <td>
                @if (NotaFiscal.ValorTotal != 0)
                {
                  <text>R$ @NotaFiscal.ValorTotal</text>
                }
                else
                {
                  <text>R$ 0,00</text>
                }
              </td>
              <td>
                @if (!NotaFiscal.DescontoTotal.HasValue)
                {
                  <text> R$ 0,00 </text>
                }
                else
                {
                  <text>R$ @NotaFiscal.DescontoTotal </text>
                }
              </td>
              <td>@NotaFiscal.DtEmissao.ToString("dd/MM/yyyy")</td>
              <td style="width:100px">
                <a href="@Url.Action("Edit", "NotaFiscalFat", new { id = @NotaFiscal.Codigo})" class="btn btn-outline-warning btn-circle btn-sm" title="Editar">
                  Editar
                </a>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
</div>*@

