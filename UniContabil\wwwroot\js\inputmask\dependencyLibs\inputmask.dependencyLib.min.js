/*!
* dependencyLibs/inputmask.dependencyLib.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2017 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 3.3.11
*/

!function(e){"function"==typeof define&&define.amd?define(["../global/window","../global/document"],e):"object"==typeof exports?module.exports=e(require("../global/window"),require("../global/document")):window.dependencyLib=e(window,document)}(function(e,t){function n(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1}function i(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?s[s.toString.call(e)]||"object":typeof e}function o(e){return null!=e&&e===e.window}function r(e){var t="length"in e&&e.length,n=i(e);return"function"!==n&&!o(e)&&(!(1!==e.nodeType||!t)||("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e))}function a(e){return e instanceof Element}function l(n){return n instanceof l?n:this instanceof l?void(void 0!==n&&null!==n&&n!==e&&(this[0]=n.nodeName?n:void 0!==n[0]&&n[0].nodeName?n[0]:t.querySelector(n),void 0!==this[0]&&null!==this[0]&&(this[0].eventRegistry=this[0].eventRegistry||{}))):new l(n)}for(var s={},f="Boolean Number String Function Array Date RegExp Object Error".split(" "),c=0;c<f.length;c++)s["[object "+f[c]+"]"]=f[c].toLowerCase();return l.prototype={on:function(e,t){if(a(this[0]))for(var n=this[0].eventRegistry,i=this[0],o=e.split(" "),r=0;r<o.length;r++){var l=o[r].split(".");!function(e,o){i.addEventListener?i.addEventListener(e,t,!1):i.attachEvent&&i.attachEvent("on"+e,t),n[e]=n[e]||{},n[e][o]=n[e][o]||[],n[e][o].push(t)}(l[0],l[1]||"global")}return this},off:function(e,t){if(a(this[0]))for(var n=this[0].eventRegistry,i=this[0],o=e.split(" "),r=0;r<o.length;r++)for(var l=o[r].split("."),s=function(e,i){var o,r,a=[];if(e.length>0)if(void 0===t)for(o=0,r=n[e][i].length;o<r;o++)a.push({ev:e,namespace:i&&i.length>0?i:"global",handler:n[e][i][o]});else a.push({ev:e,namespace:i&&i.length>0?i:"global",handler:t});else if(i.length>0)for(var l in n)for(var s in n[l])if(s===i)if(void 0===t)for(o=0,r=n[l][s].length;o<r;o++)a.push({ev:l,namespace:s,handler:n[l][s][o]});else a.push({ev:l,namespace:s,handler:t});return a}(l[0],l[1]),f=0,c=s.length;f<c;f++)!function(e,t,o){if(e in n==1)if(i.removeEventListener?i.removeEventListener(e,o,!1):i.detachEvent&&i.detachEvent("on"+e,o),"global"===t)for(var r in n[e])n[e][r].splice(n[e][r].indexOf(o),1);else n[e][t].splice(n[e][t].indexOf(o),1)}(s[f].ev,s[f].namespace,s[f].handler);return this},trigger:function(e){if(a(this[0]))for(var n=this[0].eventRegistry,i=this[0],o="string"==typeof e?e.split(" "):[e.type],r=0;r<o.length;r++){var s=o[r].split("."),f=s[0],c=s[1]||"global";if(void 0!==t&&"global"===c){var u,v,p={bubbles:!0,cancelable:!0,detail:Array.prototype.slice.call(arguments,1)};if(t.createEvent){try{u=new CustomEvent(f,p)}catch(e){(u=t.createEvent("CustomEvent")).initCustomEvent(f,p.bubbles,p.cancelable,p.detail)}e.type&&l.extend(u,e),i.dispatchEvent(u)}else(u=t.createEventObject()).eventType=f,e.type&&l.extend(u,e),i.fireEvent("on"+u.eventType,u)}else if(void 0!==n[f])if(arguments[0]=arguments[0].type?arguments[0]:l.Event(arguments[0]),"global"===c)for(var d in n[f])for(v=0;v<n[f][d].length;v++)n[f][d][v].apply(i,arguments);else for(v=0;v<n[f][c].length;v++)n[f][c][v].apply(i,arguments)}return this}},l.isFunction=function(e){return"function"===i(e)},l.noop=function(){},l.isArray=Array.isArray,l.inArray=function(e,t,i){return null==t?-1:n(t,e)},l.valHooks=void 0,l.isPlainObject=function(e){return"object"===i(e)&&!e.nodeType&&!o(e)&&!(e.constructor&&!s.hasOwnProperty.call(e.constructor.prototype,"isPrototypeOf"))},l.extend=function(){var e,t,n,i,o,r,a=arguments[0]||{},s=1,f=arguments.length,c=!1;for("boolean"==typeof a&&(c=a,a=arguments[s]||{},s++),"object"==typeof a||l.isFunction(a)||(a={}),s===f&&(a=this,s--);s<f;s++)if(null!=(e=arguments[s]))for(t in e)n=a[t],a!==(i=e[t])&&(c&&i&&(l.isPlainObject(i)||(o=l.isArray(i)))?(o?(o=!1,r=n&&l.isArray(n)?n:[]):r=n&&l.isPlainObject(n)?n:{},a[t]=l.extend(c,r,i)):void 0!==i&&(a[t]=i));return a},l.each=function(e,t){var n=0;if(r(e))for(var i=e.length;n<i&&!1!==t.call(e[n],n,e[n]);n++);else for(n in e)if(!1===t.call(e[n],n,e[n]))break;return e},l.map=function(e,t){var n,i=0,o=e.length,a=[];if(r(e))for(;i<o;i++)null!=(n=t(e[i],i))&&a.push(n);else for(i in e)null!=(n=t(e[i],i))&&a.push(n);return[].concat(a)},l.data=function(e,t,n){if(void 0===n)return e.__data?e.__data[t]:null;e.__data=e.__data||{},e.__data[t]=n},"function"==typeof e.CustomEvent?l.Event=e.CustomEvent:(l.Event=function(e,n){n=n||{bubbles:!1,cancelable:!1,detail:void 0};var i=t.createEvent("CustomEvent");return i.initCustomEvent(e,n.bubbles,n.cancelable,n.detail),i},l.Event.prototype=e.Event.prototype),l});