﻿@font-face {
  font-family: Poppins-Bold;
  src: url("../fonts/poppins/Poppins-Bold.ttf");
}

@font-face {
  font-family: Poppins-Medium;
  src: url("../fonts/poppins/Poppins-Medium.ttf");
}

@font-face {
  font-family: Poppins-Regular;
  src: url("../fonts/poppins/Poppins-Regular.ttf");
}

@font-face {
  font-family: Poppins-Light;
  src: url("../fonts/poppins/Poppins-Light.ttf");
}

@font-face {
  font-family: Poppins-Italic;
  src: url("../fonts/poppins/Poppins-MediumItalic.ttf");
}

body * {
  font-family: Poppins-Regular;
}

.modal-backdrop {
  background-color: #00000091 !important;
}

body {
  background-color: #f7fbf7 !important;
}

.select2 {
  width: 100% !important;
}

.system-title {
  text-align: end;
  padding-right: 50px;
  font-size: 30pt;
  font-family: Poppins-Medium;
  color: #70a83b;
  background-color: #fff;
}

.footer {
  position: fixed;
  width: inherit;
  bottom: 0;
  padding-bottom: 20px;
  background-color: #fff;
  z-index: 2;
}

.list-group-item {
  cursor: pointer;
  font-size: 11pt;
}

.nav-link {
  color: #416d94 !important;
  cursor: pointer;
  font-size: 11.5pt;
  user-select: none;
}

.select2-container {
  height: 24px !important;
}

.btn {
  font-size: 10pt !important;
}

.btn-primary {
  background-color: #70a83b !important;
  border: #70a83b !important;
}

  .btn-primary:hover {
    background-color: #70a83b !important;
    border: #70a83b !important;
  }

.btn-outline-primary {
  color: #70a83b !important;
  border-color: #70a83b !important;
}

  .btn-outline-primary:hover {
    background-color: #70a83b !important;
    border-color: #70a83b !important;
    color: #fff !important;
  }

/*.page-link {
  color: #70a83b !important;
}

.page-item.active .page-link {
  color: #fff !important;
  background-color: #70a83b !important;
  border-color: #70a83b !important;
}*/

.layout-background {
  width: 95%;
  margin: auto;
  justify-content: space-evenly;
  display: flex;
}

.logo {
  width: 190px;
  margin: 20px auto;
  display: block;
  max-width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
}

.view-title {
  padding-left: 6%;
  font-size: 15pt;
  font-family: Poppins-Medium;
  margin: auto 0;
  color: #344050;
  overflow-wrap: break-word;
  width: auto;
  min-width: 400px;
}

.username {
  margin-bottom: 0;
  font-family: Poppins-Medium !important;
}

.userdata {
  font-size: 11pt;
  margin-bottom: 0;
  color: #797979;
}

.datavalue {
  font-family: Poppins-Medium !important;
  color: #344050;
}

.change-data {
  margin-left: 10px;
}

.content {
  width: 80%;
  padding-top: 15px;
}

.sidebar-group-title {
  display: flex;
  justify-content: space-evenly;
}

  .sidebar-group-title > p {
    margin: 0 7.5px;
    color: black !important;
    font-size: 10pt;
  }

  .sidebar-group-title > div {
    width: 100%;
    margin: 0;
    height: 10px;
    border-bottom: 1px solid #bdc1bd;
  }

.sidebar-group {
  padding: 7.5px 0;
}

.sidebar-menu > a {
  font-size: 10.5pt;
  font-family: Poppins-Medium;
  color: #496b4c;
  text-decoration: none !important;
}

  .sidebar-menu > a:hover {
    color: #28a745;
  }

.sidebar-menu > i {
  transform: translateY(4px);
  user-select: none;
  color: #546b4c;
}

.sidebar-menu-icon {
  float: left;
}

.sidebar-menu-arrow {
  float: right;
}

.sidebar-item {
  padding-left: 20px
}

  .sidebar-item > a {
    font-size: 10pt;
    font-family: Poppins-Medium;
    color: #496b4c;
    display: block;
    margin: 5px 0;
    text-decoration: none !important;
  }

    .sidebar-item > a:hover {
      color: #28a745;
    }

    .sidebar-item > a:last-child {
      margin-bottom: 5px;
    }

.page-content {
  padding: 20px 10px;
  overflow-y: auto;
  max-height: calc(100vh - 85px);
}

.card {
  border-radius: 10px !important;
  background-color: #fff;
  box-shadow: 5px -2px 16px 0 #4145581a, 0 3px 6px 0 #00000033 !important;
  border: 0 !important;
  display: block !important;
  flex-direction: unset !important;
  height: fit-content !important;
  margin-top: 20px !important;
}

.card-header {
  display: flex !important;
  justify-content: space-between !important;
  background-color: #f9fafd !important;
  padding: 5px 20px !important;
  border-radius: 0 !important;
  border-top-left-radius: 10px !important;
  border-bottom: 0 !important;
  border-top-right-radius: 10px !important;
  height: 45px !important;
}

.card-title {
  /*color: #344050 !important;*/
  margin: auto 0 !important;
  font-family: Poppins-Medium !important;
  /*font-size: 11pt !important;*/
}

.card-options {
  width: 45%;
  justify-content: flex-end;
  display: flex;
}

  .card-options button,
  .card-options a,
  .card-options input {
    margin-left: 5px;
  }

.card > .card-body {
  padding: 15px !important;
  flex: 0 !important;
  max-height: calc(100vh - 200px) !important;
  overflow-y: auto ;
}

.card > form > .card-body {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

  .card > form > .card-body > .form-group {
    width: 22.5%;
    margin: 0 10px 15px;
  }

.table-group .table-group-content table {
  width: 100%;
}

  .table-group .table-group-content table th, .table-group .table-group-content table td {
    padding: 3px 20px;
    font-size: 10pt;
    color: #344050;
  }

    .table-group .table-group-content table td > .link {
      margin: auto;
      display: inline-block;
    }

  .table-group .table-group-content table th {
    padding: 5px 20px;
    position: sticky;
    top: 0;
    background-color: #fff;
  }

  .table-group .table-group-content table > tbody > tr {
    border-bottom: 1px solid #f1f1f1;
  }

    .table-group .table-group-content table > tbody > tr:hover {
      background-color: #b6d9ff !important;
    }

    .table-group .table-group-content table > tbody > tr:nth-child(even) {
      background-color: #f9f9f9;
    }

.table-group-content {
/*  overflow-y: auto;
  overflow-x: auto;
  max-width: 100%;
  max-height: calc(100vh - 250px);
  white-space: nowrap;*/
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  background-color: #f9fafd !important;
  padding: 5px 20px !important;
  border-radius: 0 !important;
  border-top: 0 !important;
  border-bottom-left-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
}

  .card-footer button,
  .card-footer a,
  .card-footer input {
    margin-left: 5px;
  }

.link {
  border: 0;
  background: transparent;
  height: 25px;
  cursor: pointer !important;
  font-family: Poppins-Medium;
  text-decoration: none !important;
  min-width: 75px;
  outline: 0 !important;
  margin: 0 10px;
  text-align: center;
  font-size: 11pt;
}

.link-primary {
  color: #70a83b !important;
}

.link-secondary {
  color: #6c757d !important;
}

.link-success {
  color: #28a745 !important;
}

.link-warn {
  color: #ff8128 !important;
}

.link-info {
  color: #17a2b8 !important;
}

.link-danger {
  color: #c82333 !important;
}

label, p {
  font-size: 11pt;
}

.select2-selection__rendered, .select2-selection__placeholder, input, select, textarea, .select2-results__option {
  font-size: 10pt !important;
}

input, select {
  height: 28px !important;
  padding: 3px .75rem !important;
}

.select2-container--default .select2-selection--single, .select2-dropdown, .select2-container--default .select2-search--dropdown .select2-search__field {
  border: 1px solid #ced4da !important;
}

.select2-search__field {
  outline: 0 !important;
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-track {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: #afafaf;
  border-radius: 10px;
}

.pagination {
  margin-bottom: 0 !important;
}

i {
  cursor: pointer;
}

.input-group-prepend > .input-group-text {
  font-size: 10pt;
  height: 28px;
}

.jstree-default .jstree-anchor {
  font-size: 11pt;
}

.form-check-label {
  margin-top: 7.5px !important;
}

.field-validation-error {
  font-size: 10pt;
  font-family: 'Poppins-Medium';
  color: #c50000;
}

.row {
  margin: 0 !important;
  /*Não retire essa linha, a margem negativa da row gera problemas de layout*/
}

.loading-spinner {
  width: 25px;
  height: 25px;
  display: inline-block;
  box-sizing: border-box;
  position: relative;
  margin: 0 5px;
}

  .loading-spinner:before {
    border-radius: 50%;
    content: " ";
    width: 25px;
    height: 25px;
    display: inline-block;
    box-sizing: border-box;
    border-top: solid 4.5px #bababa;
    border-right: solid 4.5px #bababa;
    border-bottom: solid 4.5px #bababa;
    border-left: solid 4.5px #bababa;
    position: absolute;
    top: 0;
    left: 0
  }

  .loading-spinner:after {
    border-radius: 50%;
    content: " ";
    width: 25px;
    height: 25px;
    display: inline-block;
    box-sizing: border-box;
    border-top: solid 4.5px #70a83b;
    border-right: solid 4.5px transparent;
    border-bottom: solid 4.5px transparent;
    border-left: solid 4.5px transparent;
    position: absolute;
    top: 0;
    left: 0;
    animation: loading-spinner-animate 1s ease-in-out infinite
  }

@keyframes loading-spinner-animate {
  0% {
    transform: rotate(0)
  }

  100% {
    transform: rotate(360deg)
  }
}

.form-group > label {
  background-color: #ffffff;
  transform: translate(6.5px, 12.5px);
  padding: 0 2.5px;
  position: relative;
  z-index: 4;
  font-size: 10.5pt;
  color: #676767;
  display: table;
  width: fit-content;
}
.form-group {
  margin-bottom: 0px !important;
}