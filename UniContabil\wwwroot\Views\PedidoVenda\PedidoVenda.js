﻿var PedidoVenda = function () { };

PedidoVenda.init = function () {

  $(document).on("click", ".RemoverItemVenda", function () {
    var model = {
      CodItemPedidoVenda: $(this).data("codigo")
    };
    var idPedidoVenda = $('#Codigo').val();

    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/ItensPedidoVenda/Delete',
      dataType: 'json',
      data: model,
      success: function (data) {
        if (!data.erro) {
          PedidoVenda.GridItensVenda(idPedidoVenda);
          Alerta(data.titulo, data.mensagem, "green", 5000);
        }
        else {
          Alerta(data.titulo, data.mensagem, "red", 5000);
        }
      }
    });
  });

  $(document).on("click", "#AdicionarItemVenda, #EditarItemVenda", function () {

    if ($(this).val() == "0") {
      $('#CodigoModal').val("0");
      url = GetURLBaseComplete() + '/ItensPedidoVenda/Create';
    }
    else
      url = GetURLBaseComplete() + '/ItensPedidoVenda/Edit';

    var create = $(this).val();

    var model = {
      CodigoOrganizacaoModal: $('#CodigoOrganizacao').val(),
      CodFilialModal: $('#FilialSelect_id').val(),
      CodigoPedidoModal: $('#CodigoPedidoModal').val(),
      DescricaoProduto: $('#ProdutoSelect_text').val()
    };

    $('#CodigoOrganizacaoModal').val(model.CodigoOrganizacaoModal);
    $('#CodFilialModal').val(model.CodFilialModal);
    $('#CodigoPedidoModal').val(model.CodigoPedidoModal);
    $('#Descricao').val(model.DescricaoProduto);

    var valorUnitarioUnmask = $('#ValorUnit').inputmask('unmaskedvalue');
    var DescontoUnmask = $('#ValorUnit').inputmask('unmaskedvalue');
    var DescontoPorcentagemUnmask = $('#DescontoPerc').inputmask('unmaskedvalue');
    var ValorTotalUnmask = $('#ValorTotalItem').inputmask('unmaskedvalue');

    if (DescontoPorcentagemUnmask > 100) {
      DescontoPorcentagemUnmask = 100;
    }

    $('#ValorUnit').inputmask('remove');
    $('#Desconto').inputmask('remove');
    $('#DescontoPerc').inputmask('remove');
    $('#ValorTotalItem').inputmask('remove');

    $('#ValorUnit').val(valorUnitarioUnmask);
    $('#Desconto').val(DescontoUnmask);
    $('#DescontoPerc').val(DescontoPorcentagemUnmask);
    $('#ValorTotalItem').val(ValorTotalUnmask);

    var idPedidoVenda = $('#Codigo').val();

    if ($(this).val() == "0") {
      model = {
        Qtde: $("#Qtde").val(),
        Unid: $("#Unid").val(),
        SeqItem: 0,
        CodigoOrganizacaoModal: $('#CodigoOrganizacao').val(),
        CodFilialModal: $('#FilialSelect_id').val(),
        CodigoPedidoModal: $('#Codigo').val(),
        CodigoProdutoModal: $('#ProdutoSelect_id').val(),
        CodigoLocalEstoque: null,
        ValorUnit: $('#ValorUnit').val(),
        Desconto: $('#Desconto').val(),
        DescontoPerc: $('#DescontoPerc').val(),
        ValorTotalItem: $('#ValorTotalItem').val(),
        Descricao: $("#Descricao").val(),
        Observacoes: $("#Observacoes").val(),
        Lote: ""
      };;
    }
    else {
      var model = $('form').serialize();
    }

    $.ajax({
      type: 'POST',
      url: url,
      dataType: 'json',
      data: model,
      success: function (data) {
        if (!data.erro) {
          PedidoVenda.GridItensVenda(idPedidoVenda);
          MWERP.IniciaMascaras();
          Alerta(data.titulo, data.mensagem, "green", 5000);
          $('#AdicionarItemVenda').show();
          $('#ProdutoSelect_id').val("0");
          $('#ProdutoSelect_text').val("");
          $('#ProdutoSelectLookup').val(null);
          $('#ProdutoSelectLookup').trigger('change');
          $('#DivItensPedido input').val(null);
          $("#Observacoes").val(null);
        }
        else {
          if (create != "0") {
            $('#AdicionarItemVenda').hide();
            $('#EditarItemVenda').show();
          }
          MWERP.IniciaMascaras();
          Alerta(data.titulo, data.mensagem, "red", 5000);
        }
      }
    });
  });

  $(document).on("click", ".EditarItemVenda", function () {
    var model = {
      CodItemPedidoVenda: $(this).data("codigo")
    };

    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/ItensPedidoVenda/GetByCodigo',
      dataType: 'json',
      data: model,
      success: function (data) {
        if (!data.retorno.erro) {
          if (data.model.CodFilialModal == "" || data.model.CodFilialModal == "0")
            SweetAlert('Atenção', 'Gentileza selecionar uma filial antes de editar itens', 'warning', 'Fechar');
          else {
            $('#CodigoModal').val(data.model.codigoModal);
            $('#CodigoOrganizacaoModal').val(data.model.codigoOrganizacaoModal);
            $('#CodFilialModal').val(data.model.codFilialModal);
            $('#CodigoPedidoCompraModal').val(data.model.codigoPedidoCompraModal);

            $('#Unid').val(data.model.unid);
            $('#Qtde').val(data.model.qtde);
            $('#ValorUnit').val(data.model.valorUnit);
            $('#ValorTotalItem').val(data.model.valorTotalItem);
            $('#DescontoPerc').val(data.model.descontoPerc);
            $('#Desconto').val(data.model.desconto);
            $('#Descricao').val(data.model.descricao);
            $('#Observacoes').val(data.model.observacoes);
            $('#SeqItem').val(data.model.seqItem);

            $('#ProdutoSelect_id').val(data.model.produtoSelect.id);
            $('#ProdutoSelect_text').val(data.model.produtoSelect.text);
            $('#ProdutoSelectLookup').val(data.model.produtoSelect.id);
            $('#ProdutoSelectLookup').append(`<option value='${data.model.produtoSelect.id}'>${data.model.produtoSelect.text}</option>`)
            $('#ProdutoSelectLookup').trigger('change');

            $('#ProdutoSelectLookup').trigger('change');

            $('#AdicionarItemVenda').hide();
            $('#EditarItemVenda').show();
          }
        }
        else {
          Alerta(data.titulo, data.mensagem, "red", 5000);
        }
      }
    });
  });

  $(document).on("click", "#OpenModalSeries", function () {
    $('#_ModalSerie input').val(null);

    var CodigoPedido = $('#Codigo').val();
    $('#CodigoPedido').val(CodigoPedido);
    $('#TipoPedido').val("2");
    $('#_ModalSerie').modal('show');

  });

  $(document).on("change", "#DescontoPerc", function () {
    var desc = $(this).inputmask('unmaskedvalue');

    if (desc >= 100) {
      $(this).val(100);
    }
  });

  $(document).on("change", "#Desconto", function () {
    //let inputDesc = $(this).inputmask('unmaskedvalue');
    //let inputVlr = $("#ValorUnit").inputmask('unmaskedvalue');

    let desc = $(this).inputmask('unmaskedvalue');
    let vlrUnit = $("#ValorUnit").inputmask('unmaskedvalue');
    let qtd = $("#Qtde").inputmask('unmaskedvalue');
    let total = 0;

    if (vlrUnit && desc) {
      desc = parseInt(desc)
      vlrUnit = parseInt(vlrUnit)
      qtd = parseInt(qtd)

      if (qtd)
        total = (qtd * vlrUnit) - desc;
      else
        total = vlrUnit - desc;

      //$("#ValorTotalItem").val(total);
      
    }

  });

};

PedidoVenda.GridItensVenda = function (idPedidoVenda) {
  var url = GetURLBaseComplete() + '/PedidoVenda/GetGridItensPedido';
  var model = {
    CodPedidoVenda: idPedidoVenda
  }

  $.ajax({
    type: 'GET',
    cache: false,
    url: url,
    dataType: 'html',
    data: model,
    success: function (retorno) {
      if (retorno.Erro) {
        Alerta('Erro', retorno.Mensagem, 'red');
      }
      else {
        $('#DivGridItens').html("");
        $('#DivGridItens').html(retorno);
      }
    }
  });
};

$(document).ready(function () {
  PedidoVenda.init();
});