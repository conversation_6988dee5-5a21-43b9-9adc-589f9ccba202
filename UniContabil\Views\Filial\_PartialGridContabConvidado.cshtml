﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure.Controls
@model List<ContabConvidadoModel>

<div class="table-group card">
  <div class="table-group-content">
    <div class="table-responsive">
      <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
        <thead>
          <tr>
            @*<th>Status</th>*@
            <th>Nome</th>
            <th>CPF / CNPJ</th>
            <th>Email</th>
            <th>Data Convite</th>
            @*<th>Data Retorno</th>*@
          </tr>
        </thead>
        <tbody>
          @foreach (ContabConvidadoModel item in Model)
          {
            <tr>
              @*<td>
                @if (item.Status == 1)
                {
                  <text>
                    <i class="mw-circle-full" style="color:yellow"></i>
                  </text>
                }
                @if (item.Status == 3)
                {
                  <text>
                    <i class="mw-circle-full" style="color:red"></i>
                  </text>
                }
                @if (item.Status == 2)
                {
                  <text>
                    <i class="mw-circle-full" style="color:green"></i>
                  </text>
                }
              </td>*@
              <td>@item.Nome</td>
              <td>@item.CPFCNPJ</td>
              <td>@item.Email</td>
              <td>@item.DataConvite.ToString("dd/MM/yyyy")</td>
              @*<td>@(item.DataResposta.HasValue ? item.DataResposta.Value.ToString("dd/MM/yyyy") : "")</td>*@
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>
