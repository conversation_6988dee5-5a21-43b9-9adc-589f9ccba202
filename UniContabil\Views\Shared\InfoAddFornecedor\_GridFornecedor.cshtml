﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure
@model List<GridFornecedor>

<table class="table table-sm table-striped table-hover">
  <thead>
    <tr>
      <th scope="col">
        Nome
      </th>
      <th scope="col">
        CPF/CNPJ
      </th>
      <th scope="col">
        Email
      </th>
      <th scope="col">
        TelefoneFixo
      </th>
      <th scope="col">
        TelefoneCelular
      </th>
      <th>
      </th>
    </tr>
  </thead>
  <tbody>
    @foreach (GridFornecedor gridFornecedor in Model)
    {
      <tr>
        <td>
          @gridFornecedor.Nome
        </td>
        <td>
          @gridFornecedor.CPFCNPJ
        </td>
        <td>
          @gridFornecedor.Email
        </td>
        <td>
          @gridFornecedor.TelefoneFixo
        </td>
        <td>
          @gridFornecedor.TelefoneCelular
        </td>
        <td>
          <i class="mw-drip-information MaisInformacoesFornecedor" data-CodigoFornecedor="@gridFornecedor.CodigoFornecedor"></i>
        </td>
      </tr>
    }
  </tbody>
</table>

