﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="08/31/2021 17:41:15" ReportInfo.Modified="09/02/2021 12:58:57" ReportInfo.CreatorVersion="2021.3.0.0">
  <ScriptText>
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
  }
}
</ScriptText>
  <Dictionary/>
  <ReportPage Name="Page1" PaperWidth="80" PaperHeight="48" LeftMargin="5" TopMargin="5" RightMargin="5" BottomMargin="5" Watermark.Font="Arial, 60pt">
    <DataBand Name="Data1" Width="264.6" Height="135.27">
      <ShapeObject Name="Shape1" Left="7" Top="2.97" Width="245.7" Height="132.3" Border.Width="1.5"/>
      <TextObject Name="Text1" Left="16.45" Top="2.97" Width="226.8" Height="122.85" Text="&#13;&#10;&#13;&#10;&#13;&#10;Eu, &lt;b&gt;[Emissor.NomeRazao]&lt;/b&gt;, [Emissor.CPForCNPJ] &lt;b&gt;[Emissor.CPFCNPJ]&lt;/b&gt;.&#13;&#10;Declaro que recebi do Sr(a) &lt;b&gt;[Recetor.Nome]&lt;/b&gt;, [Recetor.CPF] O valor de [Recetor.Valor] ([Recetor.ValorPorExtenso]) Referente ao serviço de &lt;b&gt;[Recetor.Decricao]&lt;/b&gt;.&#13;&#10;&#13;&#10;Data: [Recetor.Data]" HorzAlign="Justify" Font="Arial, 6pt" TextRenderType="HtmlTags"/>
      <TextObject Name="Text2" Left="7" Top="106.92" Width="245.7" Height="27.02" Text="Assinatura" HorzAlign="Center" VertAlign="Bottom" Font="Arial, 6pt"/>
      <TextObject Name="Text3" Left="7" Top="2.97" Width="245.7" Height="18.9" Text="Recibo de Recebimento" HorzAlign="Center" VertAlign="Center" Font="Arial, 6pt, style=Bold"/>
      <LineObject Name="Line1" Left="16.46" Top="123.18" Width="226.8" Border.Width="0.5" Diagonal="true"/>
    </DataBand>
  </ReportPage>
</Report>
