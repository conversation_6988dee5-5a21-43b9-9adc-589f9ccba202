﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model List<TabelaTipoMovimentacaoModel>
@{
}

@*<script src="~/Views/TabelaTipoMovimentacao/TabelaTipoMovimentacao.js"></script>*@

<div class="table-group card">
  <div class="card-header">
    <div class="row">
      @if (ContextoUsuario.HasPermission("TabelaTipoMovimentacao", TipoFuncao.Create))
      {
        <button type="button" class="btn btn-success" onclick="location.href='@Url.Action("Create", "TabelaTipoMovimentacao")'">Adicionar</button>
      }
    </div>
  </div>
  <div class="table-group-content">
    <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
      <thead>
        <tr>
          <th scope="col">
            Descrição
          </th>
          <th scope="col">
            CFOP
          </th>
          <th scope="col">
            Valor ICMS
          </th>
          <th scope="col">
          </th>
        </tr>
      </thead>
      <tbody>
        @foreach (TabelaTipoMovimentacaoModel item in Model)
        {
        <tr>
          <td>
            @item.Descricao
          </td>
          <td>
            @if (item.ICMS != null)
            {
              @item.ICMS
            }
          </td>
          <td>
            @if (item.CFOPSelect != null)
            {
              @item.CFOPSelect.text
            }
          </td>
          <td>
            @if (ContextoUsuario.HasPermission("TabelaTipoMovimentacao", TipoFuncao.Edit))
            {
              <a class="link link-primary" href="@Url.Action("Edit", "TabelaTipoMovimentacao", new { id = item.Codigo })">Editar</a>
            }
          </td>
        </tr>
        }
      </tbody>
    </table>
  </div>
  <div class="card-footer">
  </div>
</div>
