﻿@model UniContabilEntidades.Models.FornecedorModel
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;


@{
  ViewData["Title"] = @Html.DisplayNameFor(model => model);
  Layout = "~/Views/Shared/_Layout.cshtml";
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();
}
<div class="table-group card">
  <div class="card-header">
    <div class="row">
      @if (ContextoUsuario.HasPermission(controllerName, TipoFuncao.Create))
      {
        <a class="btn btn-success" asp-action="Create"><i class="mw-drip-plus"></i></a>
      }
    </div>
  </div>  <div class="table-group-content">
    <div class="table-responsive">
      <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
        <thead>
          <tr>
            <th>
              Médico/Clinica
            </th>
            <th>
              Tipo
            </th>
            <th>
              @Html.DisplayNameFor(model => model.CPFCNPJ)
            </th>
            <th>
              @Html.DisplayNameFor(model => model.Nome)
            </th>
            <th>
              @Html.DisplayNameFor(model => model.CEP)
            </th>
            <th>
              @Html.DisplayNameFor(model => model.TelefoneFixo)
            </th>
            <th>
              @Html.DisplayNameFor(model => model.TelefoneCelular)
            </th>
            <th>
              @Html.DisplayNameFor(model => model.Email)
            </th>
            <th>
              Whatsapp?
            </th>
            <th>
              Ativo?
            </th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          @foreach (UniContabilEntidades.Models.FornecedorModel item in ViewBag.PageItems)
          {
            <tr>
              <td>
                @Html.DisplayFor(modelItem => item.NomeOrganizacao)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.TipoFornecedor)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.CPFCNPJ)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.Nome)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.TelefoneFixo)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.TelefoneCelular)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.Email)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.TelefoneWhatsApp)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.Ativo)
              </td>
              <td>
                @if (ContextoUsuario.HasPermission("Fornecedor", TipoFuncao.Edit))
                {
                  <a class="btn btn-secondary" asp-action="Edit" asp-route-id="@item.Id">Editar</a>
                }
                @if (ContextoUsuario.HasPermission("Fornecedor", TipoFuncao.Delete))
                {
                  <button class="btn btn-danger" onclick="DeleteItem('@this.ViewContext.RouteData.Values["controller"].ToString()', @item.Id)">Apagar</button>
                }
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
  <div class="card-footer">

    @Html.PagedListPager((IPagedList)ViewBag.PageItems, page => Url.Action("Index", new { page }),
      new PagedListRenderOptions
      {
        LiElementClasses = new string[] { "page-item" },
        PageClasses = new string[] { "page-link" },
        Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
        MaximumPageNumbersToDisplay = 5
      })
  </div>
</div>

