﻿var Shared = function () {
};

Shared.init = function () {

  $(document).on("change", "#OrganizacaoUserSelectLookup", function () {
    Shared.AlterarOrgAtiva($(this).val());
  });

  $(document).on("click", ".collapseDown", function () {
    $(this).removeClass('mw-drip-chevron-down collapseDown')
    $(this).addClass('mw-drip-chevron-up collapseUp')
  });

  $(document).on("click", ".collapseUp", function () {
    $(this).removeClass('mw-drip-chevron-up collapseUp')
    $(this).addClass('mw-drip-chevron-down collapseDown')
  });

  $(document).on("click", "#modalMudarOrg", function () {
    $.ajax({
      url: GetURLBaseComplete() + "/Organizacao/GetModalOrg",
      dataType: "html",
      type: "GET",
      success: function (data) {
        $('#modalOrg').modal('show');
        $('#modalOrg').show();
        $("#_PartialOrg").html("");
        $("#_PartialOrg").html(data);

      }
    });
  });

};

Shared.AlterarOrgAtiva = function (idFilial) {

  if (idFilial && (idFilial != "0")) {
    idFilial = parseInt(idFilial);

    if (idFilial < 1)
      idFilial = null;

    const obj = {
      idFilial: idFilial
    };

    $.post(GetURLBaseComplete() + "/Account/ChangeOrganizacao", obj, function (data) {
      data = JSON.parse(data);
      if (data.Sucesso)
        location.href = GetURLBaseComplete() + "/ClassificacaoNotasFiscais/Index";
      else if (!data.Sucesso && data.Reload)
        location.reload();
      else
        Alerta("Erro", data.Mensagem, "red", 5000);
    });
  }
  else {
    Alerta("Aviso", "Por favor selecione os campos de organização e filial.", "yellow", 5000);
  }
};

Shared.ShowModalResetPassword = function () {
  $('#ResetPassword').modal('show');
}

Shared.ResetPassword = function () {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/Perfil/ResetPassword',
    dataType: 'json',
    processData: false,
    data: $('#formResetPassword').serialize(),
    success: function (data) {
      if (!data.erro) {
        Alerta("Sucesso", data.mensagem, "green", 5000, "topRight");
        $('#ResetPassword').modal('hide');
      }
      else
        Alerta("Erro", data.mensagem, "red", 8000, "topRight");

    },
    err: function (erro) {
      Alerta("Erro", "Tivemos um problema ao realizar sua solicitação, verifique sua conexão com internet ou tente novamente mais tarde.", "red", 8000);
    }
  });
}
funcao = function () {
  $.ajax({
    url: "http://187.1.83.6/uauAPI/api/v1.0/Autenticador/AutenticarUsuario",
    headers: {
      'X-INTEGRATION-Autorization': 'eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2In0..c1_U-Ju9U7etQ1hHe31mYQ.SDv8vP1Ep-RWVtzOWPGQvNOVL975ALEM6HMkHm-B9KEykHtpEJWiAmpp9ww3fhr5yYL4nTf58IBTXaIbPK-LupdwYTO0N_ejK0QYra9ix7A427IGhlwHXJ2RU4TwswNXcu1_ZaxvoLhHeOsFAm0bAys5_CfkymNArvJn9OlLM2A.7tJAxvnSAjrLZZBFY35BCw',
      'Cache-Contro': 'no-cache',
      'Content-Type': 'application/json'
    },
    dataType: "json",
    type: "POST",
    data: {
      "login": ".root" //SUBSTITUIR VARIALVEIS DE ACORDO COM O NECESSARIO CASO QUEIRA PEGAR PELO ID ==> $("#IDDOCAMPO").val()
      , "senha": ".M@00103333"
    },
    success: function (RetornoRequest) {
      return RetornoRequest;
    },
    err: function (error) {
      return "Curl error #: " + error;
    }
  });
};

$(document).ready(function () {
  Shared.init();
});



