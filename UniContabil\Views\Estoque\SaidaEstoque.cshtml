﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model SaidaEstoqueModel
@{
  ViewBag.Title = "Saída Estoque";
}

<link rel="stylesheet" href="~/Views/Estoque/Estoque.css?difa=rldlr" />
<script src="~/Views/Estoque/Estoque.js"></script>

@using (Html.BeginForm("SaidaEstoque", "Estoque", FormMethod.Post))
{
  <div class="table-group card">
    <div class="card-header">
      <div class="card-title">
        Lista de Pedidos
      </div>
      <div class="card-options">
        @if (ContextoUsuario.HasPermission("Estoque", TipoFuncao.CriarSaidaEstoque))
        {
          <div style="display: flex">
            @Html.CheckBoxFor(a => a.CheckTodos)
            <label for="CheckTodos" style="margin: 2.5px 0 0 5px;"> Selecionar Todos</label>
          </div>
          <div style="min-width: 150px;margin-left: 10px">
            @Html.Select2("LocalEstoqueSelect", "GetLocalEstoqueSelect", "Selecione Local Estoque", "", "", Model.LocalEstoqueSelect == null ? "" : Model.LocalEstoqueSelect.id, Model.LocalEstoqueSelect == null ? "" : Model.LocalEstoqueSelect.text)
          </div>
          <button type="submit" class="btn btn-success" style="margin-left: 10px">Criar Movimento Estoque</button>
        }
      </div>
    </div>
    <div class="card-body">
      @if (Model.ListaPedidoVendaSaidaEstoqueIndex != null)
      {
        @for (int i = 0; i < Model.ListaPedidoVendaSaidaEstoqueIndex.Count; i++)
        {
          if (Model.ListaPedidoVendaSaidaEstoqueIndex[i].ListaItensPedidoVendaEstoqueIndex.Count() != 0)
          {
            <div style="margin-bottom: 20px">
              @Html.HiddenFor(a => Model.ListaPedidoVendaSaidaEstoqueIndex[i].Codigo)
              @Html.HiddenFor(a => Model.ListaPedidoVendaSaidaEstoqueIndex[i].CodigoFilial)
              @Html.HiddenFor(a => Model.ListaPedidoVendaSaidaEstoqueIndex[i].NomeCliente)
              @Html.HiddenFor(a => Model.ListaPedidoVendaSaidaEstoqueIndex[i].NomeFilial)
              @Html.HiddenFor(a => Model.ListaPedidoVendaSaidaEstoqueIndex[i].DataEmissao)
              <div style="display: flex">
                @if (ContextoUsuario.HasPermission("Estoque", TipoFuncao.CriarSaidaEstoque))
                {
                  @Html.CheckBoxFor(a => Model.ListaPedidoVendaSaidaEstoqueIndex[i].Checked)
                }
                <span style="margin: 2.5px 0 0 5px; font-size: 11pt; font-family: Poppins-Medium;">
                  @string.Format("Filial: {0} - Cliente: {1} - Data: {2}", Model.ListaPedidoVendaSaidaEstoqueIndex[i].NomeFilial, Model.ListaPedidoVendaSaidaEstoqueIndex[i].NomeCliente, Model.ListaPedidoVendaSaidaEstoqueIndex[i].DataEmissao.ToString("dd/MM/yyyy"))
                </span>
              </div>
              <div class="table-group-content">
                <table>
                  <thead>
                    <tr>
                      <th scope="col">
                        Produto
                      </th>
                      <th scope="col">
                        Quantidade
                      </th>
                      <th scope="col">
                        Valor Unitário
                      </th>
                      <th scope="col">
                        Valor Total
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @for (int z = 0; z < Model.ListaPedidoVendaSaidaEstoqueIndex[i].ListaItensPedidoVendaEstoqueIndex.Count(); z++)
                    {
                      <tr>
                        <td style="display:none;">
                          @Html.HiddenFor(a => Model.ListaPedidoVendaSaidaEstoqueIndex[i].ListaItensPedidoVendaEstoqueIndex[z].Codigo)
                          @Html.HiddenFor(a => Model.ListaPedidoVendaSaidaEstoqueIndex[i].ListaItensPedidoVendaEstoqueIndex[z].DescricaoProduto)
                          @Html.HiddenFor(a => Model.ListaPedidoVendaSaidaEstoqueIndex[i].ListaItensPedidoVendaEstoqueIndex[z].Quantidade)
                          @Html.HiddenFor(a => Model.ListaPedidoVendaSaidaEstoqueIndex[i].ListaItensPedidoVendaEstoqueIndex[z].ValorTotal)
                          @Html.HiddenFor(a => Model.ListaPedidoVendaSaidaEstoqueIndex[i].ListaItensPedidoVendaEstoqueIndex[z].ValorUnitario)
                        </td>
                        <td>
                          @Model.ListaPedidoVendaSaidaEstoqueIndex[i].ListaItensPedidoVendaEstoqueIndex[z].DescricaoProduto
                        </td>
                        <td>
                          @Model.ListaPedidoVendaSaidaEstoqueIndex[i].ListaItensPedidoVendaEstoqueIndex[z].Quantidade
                        </td>
                        <td>
                          @Model.ListaPedidoVendaSaidaEstoqueIndex[i].ListaItensPedidoVendaEstoqueIndex[z].ValorUnitario
                        </td>
                        <td>
                          @Model.ListaPedidoVendaSaidaEstoqueIndex[i].ListaItensPedidoVendaEstoqueIndex[z].ValorTotal
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          }
        }
      }
      else
      {

      }
    </div>
    <div class="card-footer">

    </div>
  </div>
}