﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model List<PedidoCompraModelIndex>
@{
  ViewBag.Title = "Pedido Compra";
}

<script src="~/Views/PedidoCompra/PedidoCompra.js"></script>

<div class="table-group card">
  <div class="card-header">
    <div class="card-title">Últimos Pedidos</div>
    <div class="card-options">
      @if (ContextoUsuario.HasPermission("PedidoCompra", TipoFuncao.Create))
      {
        <button type="button" class="btn btn-success" onclick="location.href='@Url.Action("CreateInicial", "PedidoCompra")'">Adicionar Pedido Compra</button>
      }
    </div>
  </div>
  <div class="table-group-content">
    <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
      <thead>
        <tr>
          <th scope="col">
            Código
          </th>
          <th scope="col">
            Filial
          </th>
          <th scope="col">
            Data Emissão
          </th>
          <th scope="col">
            Cliente
          </th>
          <th scope="col">
          </th>
        </tr>
      </thead>
      <tbody>
        @foreach (PedidoCompraModelIndex pedidoCompraModelIndex in Model)
        {
          <tr>
            <td>
              @pedidoCompraModelIndex.CodigoIdent
            </td>
            <td>
              @if (string.IsNullOrEmpty(pedidoCompraModelIndex.NomeFilial))
              {
                <text> - </text>
              }
              else
              {
                @pedidoCompraModelIndex.NomeFilial
              }
            </td>
            <td>
              @pedidoCompraModelIndex.DataEmissao
            </td>
            <td>
              @if (string.IsNullOrEmpty(pedidoCompraModelIndex.NomeCliente))
              {
                <text> - </text>
              }
              else
              {
                @pedidoCompraModelIndex.NomeCliente
              }
            </td>
            <td>
              @if (ContextoUsuario.HasPermission("PedidoCompra", TipoFuncao.Edit))
              {
                <a class="link link-primary" href="@Url.Action("Edit", "PedidoCompra", new { CodPedidoCompra = pedidoCompraModelIndex.Codigo })">Editar</a>
              }
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
  <div class="card-footer">
  </div>
</div>
