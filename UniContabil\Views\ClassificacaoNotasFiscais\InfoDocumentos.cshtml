﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure.Controls
@model LancamentosModel

@Html.HiddenFor(a => a.Id)
@Html.HiddenFor(a => a.IdClassificacao)
@Html.HiddenFor(a => a.DataCria<PERSON>o)
@Html.HiddenFor(a => a.<PERSON>)
@Html.HiddenFor(a => a.TipoPessoaDespesa)
@Html.HiddenFor(a => a.TipoEntrada)
@Html.HiddenFor(a => a.IdFormaPgto)
@Html.HiddenFor(a => a.IdCartaoCredito)
@Html.HiddenFor(a => a.IdBanco)

@if (Model != null)
{
  <div class="card-body">
    @*<div class="row" style="display:flex; justify-content:flex-end;">
          @if (Model.HasRecibo)
          {
          <button class="btn btn-outline-info" id="btnRecibo" style=" margin-top: 4px;" codigoLancamento="@Model.Id"><PERSON><PERSON><PERSON></button>
          }
      </div>*@
    <div class="row">
      <div class="col-md-12">
        <div class="form-group">
          @Html.LabelFor(a => a.Descricao)
          @Html.EditorFor(a => a.Descricao, new { htmlAttributes = new { @class = "form-control" } })
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          @Html.LabelFor(a => a.DataLancamento)
          <input name="DtaLancamentoFormat" class="form-control Data" id="DtaLancamentoFormat" value="@Model.DtaLancamentoFormat" />
          @*@Html.EditorFor(a => a.DataLancamento, new { htmlAttributes = new { @class = "form-control DatePick" } })*@

        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          @Html.LabelFor(a => a.Valor)
          <input name="ValorFormat" class="form-control moneyMask" id="ValorFormat" value="@Model.ValorFormat" />
          @*@Html.EditorFor(a => a.Valor, new { htmlAttributes = new { @class = "form-control moneyMask" } })*@
        </div>
      </div>
      <div class="col-md-3" id="formapgtoselect">
        <div class="form-group">
          <label asp-for="FormaPgtoDespesaSelect" class="control-label"></label>
          @Html.Select2("FormaPgtoDespesaSelect", "GetFormaPagamentoSelect", "Selecione a Forma de Pgto", "Select2", "", Model.IdFormaPgto == 0 ? "" : Model.FormaPgtoDespesaSelect.id, Model.FormaPgtoDespesaSelect == null ? "" : Model.FormaPgtoDespesaSelect.text)
        </div>
      </div>
      <div class="col-md-3" id="formaCredito" @if (!Model.IdCartaoCredito.HasValue || Model.IdCartaoCredito == 0) { <text> hidden="hidden" </text> }>
        <div class="form-group">
          <label @*asp-for="CartaoCreditoSelect"*@ class="control-label">Cartão de Crédito</label>
          @Html.Select2("CartaoCreditoSelect", "GetCartaoCreditoSelect", "Selecione o Cartão de Crédito", "Select2", "", Model.IdCartaoCredito == 0 ? "" : Model.CartaoCreditoSelect.id, Model.CartaoCreditoSelect == null ? "" : Model.CartaoCreditoSelect.text)
        </div>
      </div>
      <div class="col-md-3" id="formabanco"  @if (!Model.IdBanco.HasValue || Model.IdBanco == 0) { <text> hidden="hidden" </text> }>
        <div class="form-group">
          <label @*asp-for="FormaPgtoDespesaSelect"*@ class="control-label">Banco</label>
          @Html.Select2("BancoSelect", "GetBancoSelect", "Selecione o Banco", "Select2", "", Model.IdBanco == 0 ? "" : Model.BancoSelect.id, Model.BancoSelect == null ? "" : Model.BancoSelect.text)
        </div>
      </div>
    </div>
    <div class="row" style="align-items: flex-end;">
      <div class="col-md-3">
        <div class="form-group">
          <label>CPF/CNPJ</label>
          <input name="CPF" class="form-control CPFCNPJ" id="CPFCNPJ" value="@Model.CPF" />
        </div>
      </div>
      <div class="col-md-9">
        <div class="form-group">
          <label>Nome/Razão Social</label>
          <input name="NomePF" class="form-control" id="NomePF" value="@Model.NomePF" />
        </div>
      </div>
      @*<div class="col-md-3" id="divDtaNSC">
          <div class="form-group">
            @Html.LabelFor(a => a.DtaNascimento)
            <input name="DtaNascimentoFormat" class="form-control Data" id="DtaNascimentoFormat" value="@Model.DtaNascimentoFormat" />
          </div>
        </div>*@
      @if (Model.HasRecibo)
      {
        <div class="col-md-2">
          <div class="row" style="margin-top: 5px !important;">
            <input type="checkbox" name="geraRecibo" id="geraRecibo" />
            <label style="margin-top: 3px;margin-left: 5px;"> Gerar Recibo?</label>
          </div>
        </div>
        @Html.HiddenFor(a => a.NomeRecibo, new { @id = "nomerecibo" })
        @Html.HiddenFor(a => a.CPFRecibo, new { @id = "cpfrecibo" })
        <div class="col-md-12" id="dadosrecibo">
        </div>
      }

      <div class="col-md-12" style=" float: left !important; text-align: right; margin-top: 1vh;">
        <a class="btn btn-info" id="Edit"> Salvar</a>
      </div>
    </div>
  </div>

  <script>
    $(document).ready(function () {
      $("#dadosrecibo").hide();

      let cpfcnpj = $("#CPFCNPJ").val();

      if (cpfcnpj) {
        cpfcnpj = cpfcnpj.replaceAll(".", "");
        cpfcnpj = cpfcnpj.replaceAll("-", "");
        cpfcnpj = cpfcnpj.replaceAll("/", "");

        if (cpfcnpj.length == 11) {
          $("#divDtaNSC").show();
        }
        else {
          $("#divDtaNSC").hide();
        }
      }

      if ($("#TipoPessoaDespesa").val() == 1)
        $("#PessoaDespesa_PF").show();
      else {
        $("#PessoaDespesa_PF").hide();
      }
    });

    $(document).on("keyup", "#CPFCNPJ", function () {
      let cpfcnpj = $(this)[0].value.replaceAll(".", "");
      cpfcnpj = cpfcnpj.replaceAll("-", "")

      if (cpfcnpj.length == 11) {
        $("#divDtaNSC").show();
        $("#TipoPessoaDespesa").val("1");
      }
      else {
        $("#divDtaNSC").hide();
        $("#TipoPessoaDespesa").val("2");
      }
    });
  </script>
}
