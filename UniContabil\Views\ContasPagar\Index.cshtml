﻿@model UniContabilEntidades.Models.ContasPagarModel
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;


@{
  ViewData["Title"] = "Index";
}
<h1>@Html.DisplayNameFor(model => model)</h1>
@if (ContextoUsuario.HasPermission("ContasPagar", TipoFuncao.Create))
{
  <p>
    <a asp-action="Create">+ Novo</a>
  </p>
}
<table class="table">
  <thead>
    <tr>
      <th>
        @Html.DisplayNameFor(model => model.CodigoIdent)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.NomeFilial)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.NomeFornecedor)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DescricaoHistoricoContas)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DescricaoCentroCusto)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DescricaoContaContabel)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.TipoTitulo)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.NroDocumento)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.Serie)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DtEmissao)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.ValorBruto)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DtVencimento)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DtVencimentoReal)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.Parcelas)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.NroBaixa)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DtBaixa)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.ValorTotalPago)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.Juros)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.Descontos)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.JurosDia)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DescontoDia)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.MultaMes)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.JurosDiaMonetario)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DescontoDiaMonetario)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.MultaMesMonetario)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.ValorLiquido)
      </th>
      <th></th>
    </tr>
  </thead>
  <tbody>
    @foreach (UniContabilEntidades.Models.ContasPagarModel item in ViewBag.PageItems)
    {
      <tr>
        <td>
          @Html.DisplayFor(modelItem => item.CodigoIdent)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.NomeFilial)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.NomeFornecedor)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DescricaoHistoricoContas)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DescricaoCentroCusto)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DescricaoContaContabel)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.TipoTitulo)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.NroDocumento)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.Serie)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DtEmissao)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.ValorBruto)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DtVencimento)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DtVencimentoReal)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.Parcelas)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.NroBaixa)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DtBaixa)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.ValorTotalPago)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.Juros)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.Descontos)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.JurosDia)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DescontoDia)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.MultaMes)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.JurosDiaMonetario)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DescontoDiaMonetario)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.MultaMesMonetario)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.ValorLiquido)
        </td>
        <td>
          @if (ContextoUsuario.HasPermission("ContasPagar", TipoFuncao.Edit))
          {
            <a class="btn btn-secondary" asp-action="Edit" asp-route-id="@item.Id">Editar</a>
          }
          @if (ContextoUsuario.HasPermission("ContasPagar", TipoFuncao.Delete))
          {
            <button class="btn btn-danger" onclick="DeleteItem('@this.ViewContext.RouteData.Values["controller"].ToString()', @item.Id)">Apagar</button>
          }
        </td>
      </tr>
    }
  </tbody>
</table>
@Html.PagedListPager((IPagedList)ViewBag.PageItems, page => Url.Action("Index", new { page }),
  new PagedListRenderOptions
  {
    LiElementClasses = new string[] { "page-item" },
    PageClasses = new string[] { "page-link" },
    Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
    MaximumPageNumbersToDisplay = 5
  })

