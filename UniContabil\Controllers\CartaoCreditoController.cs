﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniContabilDomain.Services;
using UniContabilEntidades.Models;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;

namespace UniContabil.Controllers
{
  [Authorize]
  public class CartaoCreditoController : LibController
  {
    private CartaoDeCreditoServices _cartaoService;

    public CartaoDeCreditoServices CartaoDeCreditoServices
    {
      get
      {
        _cartaoService = new CartaoDeCreditoServices(ContextoUsuario.UserLogged);
        return _cartaoService;
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        IPagedList<GetListIndex> list = CartaoDeCreditoServices.GetPageList(page);
        return View(list);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        return View();
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Create()
    {
      return View(new CartaoDeCreditoCreateModel());
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Create(CartaoDeCreditoCreateModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          if (ContextoUsuario.UserLogged.IdOrganizacao == 0 && !ContextoUsuario.UserLogged.IdFilial.HasValue)
          {
            MessageAdd(new Message(MessageType.Warning, "É necessário selecionar a clínica."));
            return View(model);
          }

          CartaoDeCreditoServices.Create(model);
          return RedirectToAction(nameof(Index));
        }
        else
        {
          return View(model);
        }
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Edit(int id)
    {
      try
      {
        CartaoDeCreditoEditModel model = CartaoDeCreditoServices.GetById(id);

        if (model == null)
        {
          return NotFound();
        }

        return View(model);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        return View();
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Edit(CartaoDeCreditoEditModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          CartaoDeCreditoServices.Edit(model);
          return RedirectToAction(nameof(Index));
        }
        return View(model);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        return View();
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public string Delete(CartaoDeCreditoDeleteModel model)
    {
      try
      {
        CartaoDeCreditoServices.Delete(model);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", ex.HResult) });
      }
    }
  }
}
