﻿@model UniContabilEntidades.Models.VincularCreditoDebitoModel
@using UniContabil.Infrastructure.Controls
<form id="formAdc">
  @Html.HiddenFor(x => x.IdClassificacao)
  @Html.HiddenFor(x => x.IdClassificacaoContaPlano)

  <input type="hidden" id="TipoPessoaSelect_id" name="TipoPessoaSelect_id" value="@Model.IdTipoPessoa" />
  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <label asp-for="TipoPlanoCreditoSelect" class="control-label"></label>
        @Html.Select2("TipoPlanoCreditoSelect", "GetTipoPlanoContasCreditoSelect", "Selecione a conta de crédito", "", "", Model.IdPlanoCredito == 0 ? "" : Model.TipoPlanoCreditoSelect.id, Model.TipoPlanoCreditoSelect == null ? "" : Model.TipoPlanoCreditoSelect.text, afterSelect: "TipoPessoaSelect")
      </div>
    </div>
    <div class="col-md-6">
      <div class="form-group">
        <label asp-for="TipoPlanoDebitoSelect" class="control-label"></label>
        @Html.Select2("TipoPlanoDebitoSelect", "GetTipoPlanoContasDebitoSelect", "Selecione a conta de débito", "", "", Model.IdPlanoCredito == 0 ? "" : Model.TipoPlanoDebitoSelect.id, Model.TipoPlanoDebitoSelect == null ? "" : Model.TipoPlanoDebitoSelect.text, afterSelect: "TipoPessoaSelect")
      </div>
    </div>
  </div>
</form>
