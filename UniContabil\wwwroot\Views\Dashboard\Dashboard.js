﻿var Dashboard = {};

Dashboard.init = function () {
  $(document).on("click", ".trtitle", function () {
    let ordem = $(this).data("ordem");
    let show = $(this).hasClass("rwhide");

    if (show) {
      $(".0_" + ordem).show();
      $("#colsminus_" + ordem).removeClass("colsp");
      $("#colsplus_" + ordem).addClass("colsp");
      $(this).removeClass("rwhide");
    }
    else {
      $(".0_" + ordem).hide();
      $("#colsminus_" + ordem).addClass("colsp");
      $("#colsplus_" + ordem).removeClass("colsp");
      $(this).addClass("rwhide");
    }
  });

  $(document).on("click", "#atualiza_dre", function () {
    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/Dashboard/AtualizaDRE',
      dataType: 'json',
      success: function (data) {
        if (!data.erro) {
          $.ajax({
            url: GetURLBaseComplete() + "/Dashboard/GetGridDRE",
            dataType: "html",
            type: "GET",
            success: function (data) {
              $("#gridDRE").html("");
              $("#gridDRE").html(data);
            }
          });
        }
        else {
          SweetAlert("Erro!", data.message, 'error')
        }
      }
    });
  });

  Dashboard.GetLancamentos();
};

Dashboard.GetLancamentos = function () {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/Dashboard/GetLancamentos?ano=2022',
    dataType: 'json',
    success: function (data) {
      if (!data.erro) {
        let lancamentos = data.lancamentos;
        $("#TotalReceita").html(lancamentos.receita.valorTotalFormatado);
        $("#TotalDespesa").html(lancamentos.despesa.valorTotalFormatado);

        Dashboard.ReceitasTreemap(lancamentos.receita.series);
        Dashboard.DepesasTreemap(lancamentos.despesa.series);
      }
      else {
        SweetAlert("Erro!", data.message, 'error')
      }
    }
  });
};

Dashboard.ReceitasTreemap = function (series) {

  var options = {
    series: [
      {
        data: series
      }
    ],
    legend: {
      show: false
    },
    chart: {
      height: 150,
      type: 'treemap'
    },
    dataLabels: {
      enabled: true,
      style: {
        fontSize: '14px',
        fontWeight: 'bold',
      },
      formatter: function (val, opt) {
        if (val.length > 15) {
          return val.substring(0, 15);
        } else {
          return val;
        }
      },
    },
    tooltip: {
      enabled: true,
      style: {
        fontSize: '15px',
      },
      y: {
        formatter: function (val, opt) {
          var formatter = new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
          });

          return formatter.format(val);
        },
        title: {
          formatter: (seriesName) => seriesName,
        },
      },
    },
    title: {
      text: 'Receitas'
    }
  };

  var chart = new ApexCharts(document.querySelector("#GuiaStatus"), options);
  chart.render();
};

Dashboard.DepesasTreemap = function (series) {

  var options = {
    series: [
      {
        data: series
      }
    ],
    legend: {
      show: false
    },
    chart: {
      height: 150,
      type: 'treemap'
    },
    title: {
      text: 'Despesas'
    },
    dataLabels: {
      enabled: true,
      offsetX: 0,
      offsetY: 0,
      style: {
        fontSize: "14",
        fontFamily: "Helvetica, Arial, sans-serif",
        fontWeight: "bold"
      },
      formatter: function (val, opt) {
        if (val.length > 15) {
          return val.substring(0, 15);
        } else {
          return val;
        }
      },
    },
    tooltip: {
      enabled: true,
      style: {
        fontSize: '15px',
      },
      y: {
        formatter: function (val, opt) {
          var formatter = new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
          });

          return formatter.format(val);
        },
        title: {
          formatter: (seriesName) => seriesName,
        },
      },
    },
  };

  var chart = new ApexCharts(document.querySelector("#DistribuicaoGlosas"), options);
  chart.render();
};


Dashboard.GetRepasseMes = function () {
  var options = {
    series: [44, 55, 41, 17, 15],
    chart: {
      type: 'donut',
    },
    plotOptions: {
      pie: {
        customScale: 0.7,
        donut: {
          size: 50,
        }
      }
    },
    responsive: [{
      breakpoint: 480,
      options: {
        chart: {
          width: 50
        },
        legend: {
          position: 'top'
        },

      }
    }]
  };

  var chart = new ApexCharts(document.querySelector("#RepasseMes"), options);
  chart.render()
};

Dashboard.GetDemonstrativoResult = function () {
  var options = {
    series: [{
      name: "Session Duration",
      data: [45, 52, 38, 24, 33, 26, 21, 20, 6, 8, 15, 10]
    },
    {
      name: "Page Views",
      data: [35, 41, 62, 42, 13, 18, 29, 37, 36, 51, 32, 35]
    },
    {
      name: 'Total Visits',
      data: [87, 57, 74, 99, 75, 38, 62, 47, 82, 56, 45, 47]
    }
    ],
    chart: {
      height: 215,
      type: 'line',
      zoom: {
        enabled: false
      },
    },
    dataLabels: {
      enabled: false
    },
    stroke: {
      width: [5, 5, 5],
      curve: 'straight',
      dashArray: [0, 0, 0]
    },
    legend: {
      tooltipHoverFormatter: function (val, opts) {
        return val + ' - ' + opts.w.globals.series[opts.seriesIndex][opts.dataPointIndex] + ''
      }
    },
    markers: {
      size: 0,
      hover: {
        sizeOffset: 6
      }
    },
    xaxis: {
      categories: ['01 Jan', '02 Jan', '03 Jan', '04 Jan', '05 Jan', '06 Jan', '07 Jan', '08 Jan', '09 Jan',
        '10 Jan', '11 Jan', '12 Jan'
      ],
    },
    tooltip: {
      y: [
        {
          title: {
            formatter: function (val) {
              return val + " (mins)"
            }
          }
        },
        {
          title: {
            formatter: function (val) {
              return val + " per session"
            }
          }
        },
        {
          title: {
            formatter: function (val) {
              return val;
            }
          }
        }
      ]
    },
    grid: {
      borderColor: '#f1f1f1',
    }
  };

  var chart = new ApexCharts(document.querySelector("#DemonstrativoResult"), options);
  chart.render();
};

Dashboard.GetDomonstrativo = function () {
  var options = {
    series: [{
      name: 'Marine Sprite',
      data: [37, 22, 43, 21]
    }, {
      name: 'Striking Calf',
      data: [ 52, 13, 43, 32]
    }, {
      name: 'Tank Picture',
      data: [9, 15, 11, 20]
    }, {
      name: 'Bucket Slope',
      data: [8, 6, 9, 4]
    }, {
      name: 'Reborn Kid',
      data: [32, 25, 24, 10]
    }],
    chart: {
      type: 'bar',
      height: 300,
      stacked: true,
      stackType: '100%'
    },
    plotOptions: {
      bar: {
        horizontal: true,
      },
    },
    stroke: {
      width: 1,
      colors: ['#fff']
    },
    xaxis: {
      categories: [2011, 2012, 2013, 2014],
    },
    tooltip: {
      y: {
        formatter: function (val) {
          return val + "K"
        }
      }
    },
    fill: {
      opacity: 1

    },
    legend: {
      position: 'top',
      horizontalAlign: 'left',
      offsetX: 40
    }
  };

  var chart = new ApexCharts(document.querySelector("#Demonstrativo"), options);
  chart.render();
};

$(document).ready(function () {
  Dashboard.init();
  Dashboard.GetRepasseMes();
});
