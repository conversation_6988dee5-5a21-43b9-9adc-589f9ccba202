﻿@model UniContabilEntidades.Models.SeriesNotasModel
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;

    @{
    ViewData["Title"] = @Html.DisplayNameFor(model => model);
    }
  <div class="table-group card">
    <div class="card-header">
      <div class="card-title">@Html.DisplayNameFor(model => model)</div>
      <div class="card-options">
        @if (ContextoUsuario.HasPermission("SeriesNotas", TipoFuncao.Create))
        {
        <a class="btn btn-success" asp-action="Create"><i class="mw-drip-plus"></i></a>
        }
      </div>
    </div>
    <div class="table-group-content">
      <table>
        <thead>
          <tr>
                <th>
                  @Html.DisplayNameFor(model => model.Serie)
                </th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          @foreach (UniContabilEntidades.Models.SeriesNotasModel item in ViewBag.PageItems) {
          <tr>
                <td>
                  @Html.DisplayFor(modelItem => item.Serie)
                </td>
              <td>
                @if (ContextoUsuario.HasPermission("SeriesNotas", TipoFuncao.Edit))
                {
                <a class="link link-secondary" asp-action="Edit" asp-route-id="@item.Codigo">Editar</a>
                }
                @if (ContextoUsuario.HasPermission("SeriesNotas", TipoFuncao.Delete))
                {
                <button class="link link-danger" onclick="DeleteItem('@this.ViewContext.RouteData.Values["controller"].ToString()', @item.Codigo)">Apagar</button>
                }
              </td>
          </tr>
          }
        </tbody>
      </table>
    </div>
    <div class="card-footer">
      @Html.PagedListPager((IPagedList)ViewBag.PageItems, page => Url.Action("Index", new { page }),
      new PagedListRenderOptions {
        LiElementClasses = new string[] { "page-item" },
        PageClasses = new string[] { "page-link" },
        Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
        MaximumPageNumbersToDisplay = 5
      })
    </div>
  </div>

