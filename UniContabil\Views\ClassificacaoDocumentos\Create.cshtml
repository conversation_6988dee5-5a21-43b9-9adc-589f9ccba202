﻿@using UniContabil.Infrastructure.Controls
@model UniContabilEntidades.Models.ClassificacaoDocumentosModel

@using (Html.BeginForm("Create", "ClassificacaoDocumentos", FormMethod.Post, new { id = "CreateClassificacao" }))
{
  @Html.AntiForgeryToken()
  @Html.HiddenFor(a => Model.IdCategoria)
  @Html.HiddenFor(a => Model.IdTipoEntrada)
  @Html.HiddenFor(a => Model.IdTipoPessoa)
  <div class="card">
    <div class="card-header">
      <h5>Nova Classificação</h5>
    </div>
    <div class="card-body">
      <div asp-validation-summary="ModelOnly" class="text-danger"></div>
      <div class="row">
        <div class="col-md-1">
          <div class="form-group">
            <label asp-for="Codigo" class="control-label"></label>
            <input asp-for="Codigo" class="form-control" />
            <span asp-validation-for="Codigo" class="text-danger"></span>
          </div>
        </div>
        <div class="col-md-5">
          <div class="form-group">
            <label asp-for="Descricao" class="control-label"></label>
            <input asp-for="Descricao" class="form-control" />
            <span asp-validation-for="Descricao" class="text-danger"></span>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <a class="btn btn-primary" id="saveClassCreate">Salvar</a>
      </div>
    </div>
  </div>

}