﻿@using UniContabil.Infrastructure.Controls
@model UniContabilEntidades.Models.NotaFiscalFatModel
@{
  ViewBag.Title = "Nota Fiscal Faturamento";
  //Layout = "~/Views/Shared/_LayoutAccount.cshtml";
}

<script src="~/Views/NotaFiscalFat/NotaFiscalFat.js"></script>

<div class="container" id="CabecalhoPedidoCompra">
  @using (Html.BeginForm("Create", "NotaFiscalFat", FormMethod.Post, new { enctype = "multipart/form-data" }))
  {
    <div class="row">
      @*<div class="col-md-4">
          <div class="form-group">
            @Html.LabelFor(a => a.DataEmissao)
            @Html.TextBoxFor(a => a.DataEmissao, new { @class = "form-control Data" })
            @Html.ValidationMessageFor(a => a.DataEmissao)
          </div>
        </div>*@
      <div class="col-md-4">
        <div class="form-group">
          @Html.Label("Organização")
          @Html.Select2("OrganizacaoSelect", "GetOrganizacaoSelect", "Selecione a Organização", "", "", Model.OrganizacaoSelect == null ? "" : Model.OrganizacaoSelect.id, Model.OrganizacaoSelect == null ? "" : Model.OrganizacaoSelect.text)
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          @Html.Label("Filial")
          @Html.Select2("FilialSelect", "GetFilialSelect", "Selecione a Filial", "", "", Model.FilialSelect == null ? "" : Model.FilialSelect.id, Model.FilialSelect == null ? "" : Model.FilialSelect.text)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.Label("Cliente")
          @Html.Select2("ClienteSelect", "GetClienteSelect", "Selecione o Cliente", "", "", Model.ClienteSelect == null ? "" : Model.ClienteSelect.id, Model.ClienteSelect == null ? "" : Model.ClienteSelect.text)
        </div>
      </div>

    </div>

    <div class="row">

      <div class="col-md-2">
        <div class="form-group">
          @Html.LabelFor(a => a.ValorTotal)
          @Html.TextBoxFor(a => a.ValorTotal, new { @class = "form-control money" })
          @Html.ValidationMessageFor(a => a.ValorTotal)
        </div>
      </div>

      <div class="col-md-2">
        <div class="form-group">
          @Html.LabelFor(a => a.DescontoTotal)
          @Html.TextBoxFor(a => a.DescontoTotal, new { @class = "form-control money" })
          @Html.ValidationMessageFor(a => a.DescontoTotal)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.Label("Forma Pagamento")
          @Html.Select2("FormaPagamentolSelect", "GetFormaPagamentoSelect", "Selecione a Forma de Pagamento", "", "", Model.FormaPagamentolSelect == null ? "" : Model.FormaPagamentolSelect.id, Model.FormaPagamentolSelect == null ? "" : Model.FormaPagamentolSelect.text)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.Label("Condicao Pagamento")
          @Html.Select2("CondicaoPagamentoSelect", "GetCondicaoPagamentoSelect", "Selecione a Condição de Pagamento", "", "", Model.CondicaoPagamentoSelect == null ? "" : Model.CondicaoPagamentoSelect.id, Model.CondicaoPagamentoSelect == null ? "" : Model.CondicaoPagamentoSelect.text)
        </div>
      </div>


    </div>

    <div class="row">
      <div class="col-md-2">
        <div class="form-group">
          @Html.LabelFor(a => a.Serie)
          @Html.TextBoxFor(a => a.Serie, new { @class = "form-control" })
          @Html.ValidationMessageFor(a => a.Serie)
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          @Html.LabelFor(a => a.TipoFrete)
          <select class="form-control" asp-for="TipoFrete" asp-items="Html.GetEnumSelectList<TipoFreteEnum>()">
            <option selected="selected" value="">Selecione</option>
          </select>
          @Html.ValidationMessageFor(a => a.ValorFrete)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.ValorFrete)
          @Html.TextBoxFor(a => a.ValorFrete, new { @class = "form-control money" })
          @Html.ValidationMessageFor(a => a.ValorFrete)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.CEPEntrega)
          @Html.TextBoxFor(a => a.CEPEntrega, new { @class = "form-control CEP" })
          @Html.ValidationMessageFor(a => a.CEPEntrega)
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.EndEntrega)
          @Html.TextBoxFor(a => a.EndEntrega, new { @class = "form-control" })
          @Html.ValidationMessageFor(a => a.EndEntrega)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          <label asp-for="CodigoUFEntrega" class="control-label"></label>
          @Html.Select2("UFSelect", "GetUFSelect", "Selecione " + @Html.DisplayNameFor(model => model.CodigoUFEntrega), "Select2", valor: Model.UFSelect == null ? "0" : Model.UFSelect.id, text: Model.UFSelect == null ? "" : Model.UFSelect.text)
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          <label asp-for="CodigoMunicipioEntrega" class="control-label"></label>
          @Html.Select2("MunicipioSelect", "GetMunicipioSelect", "Selecione " + @Html.DisplayNameFor(model => model.CodigoMunicipioEntrega), "Select2", afterSelect: "UFSelect", valor: Model.MunicipioSelect == null ? "0" : Model.MunicipioSelect.id, text: Model.MunicipioSelect == null ? "" : Model.MunicipioSelect.text)
        </div>
      </div>
    </div>

    <div class="box-footer">
      <button type="button" value="Voltar" class="btn btn-outline-dark" onclick="location.href='@Url.Action("Index")'">Cancelar</button>
      <button type="submit" value="Create" class="btn btn-outline-primary pull-right">Salvar &#x2713;</button>
    </div>
  }
</div>

<div class="container" id="DivItens">
</div>
