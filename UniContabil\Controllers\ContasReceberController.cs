﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;


namespace UniContabil.Controllers
{
  public class ContasReceberController : LibController
  {
    public ContasReceberController()
    { }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        IPagedList<ContasReceberModel> List = new ContasReceberServices().GetPagedList(page);
        ViewBag.PageItems = List;
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View(new ContasReceberModel());
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(ContasReceberModel contasReceberModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new ContasReceberServices(ContextoUsuario.UserLogged).Create(contasReceberModel);

          return RedirectToAction(nameof(Index));
        }
        else
        {
          return View(contasReceberModel);
        }
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(contasReceberModel);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(int id)
    {
      try
      {
        ContasReceberModel contasReceberModel = new ContasReceberModel().FromDatabase(new ContasReceberServices().GetById(id));

        if (contasReceberModel == null)
        {
          return NotFound();
        }

        return View(contasReceberModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(ContasReceberModel contasReceberModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new ContasReceberServices(ContextoUsuario.UserLogged).Edit(contasReceberModel);
          MessageAdd(new Message(MessageType.Success, "Conta a receber modificada com sucesso."));
          return RedirectToAction(nameof(Index));
        }
        return View(contasReceberModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(contasReceberModel);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string Delete(int id)
    {
      try
      {
        ContasReceberServices Service = new ContasReceberServices();
        var model = Service.GetById(id);
        Service.Delete(model);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }

    [HttpGet]
    public ActionResult GetClienteSelect(string term)
    {
      ClienteServices Service = new ClienteServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
    [HttpGet]
    public ActionResult GetHistoricoSelect(string term)
    {
      HistoricoContasServices Service = new HistoricoContasServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
    [HttpGet]
    public ActionResult GetTipoTituloSelect(string term)
    {
      TipoTituloServices Service = new TipoTituloServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
    [HttpGet]
    public ActionResult GetCentroCustoSelect(string term)
    {
      CentroCustoServices Service = new CentroCustoServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
    [HttpGet]
    public ActionResult GetContaContabelSelect(string term)
    {
      ContaContabelServices Service = new ContaContabelServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
    [HttpGet]
    public ActionResult GetFilialSelect(string term)
    {
      FilialServices Service = new FilialServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }

  }
}
