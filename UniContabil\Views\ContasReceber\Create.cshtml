﻿@model UniContabilEntidades.Models.ContasReceberModel
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Create";
}

<script src="~/Views/ContasReceber/ContasReceber.js"></script>

<h1>Criar @Html.DisplayNameFor(model => model)</h1>

<div class="row">
  <div class="col-md-4">
    <form asp-action="Create">
      @Html.AntiForgeryToken()
      <div asp-validation-summary="ModelOnly" class="text-danger"></div>
      @Html.HiddenFor(m => m.IdOrganizacao)

      <div class="form-group">
        <label asp-for="IdCliente" class="control-label"></label>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div style="width: 85%">
            @Html.Select2("ClienteSelect", "GetClienteSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdCliente), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.ClienteSelect == null ? "" : Model.ClienteSelect.id, Model.ClienteSelect == null ? "" : Model.ClienteSelect.text)
          </div>
          <div style="width: 10%; display: flex;">
            <i class="mw-drip-search" id="DetalhesCliente"></i>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label asp-for="Serie" class="control-label"></label>
        <input asp-for="Serie" class="form-control" />
        <span asp-validation-for="Serie" class="text-danger"></span>
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.DataEmissao)
        @Html.TextBoxFor(a => a.DataEmissao, new { @class = "form-control Data" })
        @Html.ValidationMessageFor(a => a.DataEmissao)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.ValorBruto)
        @Html.TextBoxFor(a => a.ValorBruto, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.ValorBruto)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.DataVencido)
        @Html.TextBoxFor(a => a.DataVencido, new { @class = "form-control Data" })
        @Html.ValidationMessageFor(a => a.DataVencido)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.Parcelas)
        @Html.TextBoxFor(a => a.Parcelas, new { @class = "form-control numerosOnly" })
        @Html.ValidationMessageFor(a => a.Parcelas)
      </div>
      <div class="form-group">
        <label asp-for="IdHistorico" class="control-label"></label>
        @Html.Select2("HistoricoSelect", "GetHistoricoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdHistorico), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.HistoricoSelect == null ? "" : Model.HistoricoSelect.id, Model.HistoricoSelect == null ? "" : Model.HistoricoSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="IdTipoTitulo" class="control-label"></label>
        @Html.Select2("TipoTituloSelect", "GetTipoTituloSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdTipoTitulo), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.TipoTituloSelect == null ? "" : Model.TipoTituloSelect.id, Model.TipoTituloSelect == null ? "" : Model.TipoTituloSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="IdCentroCusto" class="control-label"></label>
        @Html.Select2("CentroCustoSelect", "GetCentroCustoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdCentroCusto), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.CentroCustoSelect == null ? "" : Model.CentroCustoSelect.id, Model.CentroCustoSelect == null ? "" : Model.CentroCustoSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="IdContaContabel" class="control-label"></label>
        @Html.Select2("ContaContabelSelect", "GetContaContabelSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdContaContabel), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.ContaContabelSelect == null ? "" : Model.ContaContabelSelect.id, Model.ContaContabelSelect == null ? "" : Model.ContaContabelSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="NroBaixa" class="control-label"></label>
        <input asp-for="NroBaixa" class="form-control" />
        <span asp-validation-for="NroBaixa" class="text-danger"></span>
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.DataBaixa)
        @Html.TextBoxFor(a => a.DataBaixa, new { @class = "form-control Data" })
        @Html.ValidationMessageFor(a => a.DataBaixa)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.ValorTotalPago)
        @Html.TextBoxFor(a => a.ValorTotalPago, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.ValorTotalPago)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.Juros)
        @Html.TextBoxFor(a => a.Juros, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.Juros)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.Descontos)
        @Html.TextBoxFor(a => a.Descontos, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.Descontos)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.JurosDia)
        @Html.TextBoxFor(a => a.JurosDia, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.JurosDia)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.DescontoDia)
        @Html.TextBoxFor(a => a.DescontoDia, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.DescontoDia)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.MultaMes)
        @Html.TextBoxFor(a => a.MultaMes, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.MultaMes)
      </div>
      <div class="form-group">
        <label asp-for="JurosDiaPerc" class="control-label"></label>
        <input asp-for="JurosDiaPerc" class="form-control" />
        <span asp-validation-for="JurosDiaPerc" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="DescontoDiaPerc" class="control-label"></label>
        <input asp-for="DescontoDiaPerc" class="form-control" />
        <span asp-validation-for="DescontoDiaPerc" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="MultaMesPerc" class="control-label"></label>
        <input asp-for="MultaMesPerc" class="form-control" />
        <span asp-validation-for="MultaMesPerc" class="text-danger"></span>
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.ValorLiquido)
        @Html.TextBoxFor(a => a.ValorLiquido, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.ValorLiquido)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.IR)
        @Html.TextBoxFor(a => a.IR, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.IR)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.PIS)
        @Html.TextBoxFor(a => a.PIS, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.PIS)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.CSSL)
        @Html.TextBoxFor(a => a.CSSL, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.CSSL)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.ISS)
        @Html.TextBoxFor(a => a.ISS, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.ISS)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.COFINS)
        @Html.TextBoxFor(a => a.COFINS, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.COFINS)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.OutrasRetencoes)
        @Html.TextBoxFor(a => a.OutrasRetencoes, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.OutrasRetencoes)
      </div>
      <div class="form-group">
        <input type="submit" value="Adicionar" class="btn btn-primary" />
      </div>
    </form>
  </div>
</div>

<div>
  <a asp-action="Index">Voltar</a>
</div>

@await Html.PartialAsync("InfoAddCliente/_ModalPesquisaCliente", new ModalPesquisaCliente())
@await Html.PartialAsync("InfoAddCliente/_ModalDetalhesCliente")

