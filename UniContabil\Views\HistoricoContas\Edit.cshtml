﻿@model UniContabilEntidades.Models.HistoricoContasModel
@using UniContabil.Infrastructure.Controls

    @{
    ViewData["Title"] = "Editar " + @Html.DisplayNameFor(model => model);
    }
  <div class="card">
    <form asp-action="Edit">
      <div class="card-header">
        <div class="card-title">
          Editar @Html.DisplayNameFor(model => model)
        </div>
        <div class="card-options">
        </div>
      </div>
      <div class="card-body">
        @Html.AntiForgeryToken()
        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
              <div class="form-group">
                <label asp-for="IdOrganizacao" class="control-label"></label>
                @Html.Select2("OrganizacaoSelect", "GetOrganizacaoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdOrganizacao), this.ViewContext.RouteData.Values["controller"].ToString(),  "", Model.OrganizacaoSelect == null ? "" : Model.OrganizacaoSelect.id, Model.OrganizacaoSelect == null ? "" : Model.OrganizacaoSelect.text)
              </div>
              <div class="form-group">
                <label asp-for="IdFilial" class="control-label"></label>
                @Html.Select2("FilialSelect", "GetFilialSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdFilial), this.ViewContext.RouteData.Values["controller"].ToString(),  "", Model.FilialSelect == null ? "" : Model.FilialSelect.id, Model.FilialSelect == null ? "" : Model.FilialSelect.text)
              </div>
              <div class="form-group">
                <label asp-for="Descricao" class="control-label"></label>
                <input asp-for="Descricao" class="form-control" />
                <span asp-validation-for="Descricao" class="text-danger"></span>
              </div>
              <div class="form-group">
                <label asp-for="Codigo" class="control-label"></label>
                <input asp-for="Codigo" class="form-control" />
                <span asp-validation-for="Codigo" class="text-danger"></span>
              </div>
              <div class="form-group">
                @Html.LabelFor(a => a.DataEmissao)
                @Html.TextBoxFor(a => a.DataEmissao, new { @class = "form-control Data" })
                @Html.ValidationMessageFor(a => a.DataEmissao)
              </div>
      </div>
      <div class="card-footer">
        <a asp-action="Index" class="link link-info">Voltar</a>
        <input type="submit" value="Salvar" class="btn btn-primary" />
      </div>
    </form>
  </div>
