﻿@model UniContabilEntidades.Models.FilialModel
@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure

@{
  ViewData["Title"] = "Novo " + @Html.DisplayNameFor(model => model);
}

<script src="~/Views/Filial/Filial.js?12123=alsd"></script>

<div class="card">
  <form asp-action="Create">
    <input data-val="true" id="TipoPessoaSelect_id" name="TipoPessoaSelect.id" type="hidden" value="3">
    <div class="card-body">
      @Html.AntiForgeryToken()

      <div class="col-md-12" style="margin-top: 15px; margin-bottom: 10px;">
        <div class="col-md-12" style="">
          <hr style="margin:0;">
        </div>
        <div style="position: absolute;top: -18px;left: 42%;background: white;">
          <label style="font-size: 17pt;">Dados Cadastrais</label>
        </div>
      </div>

      <div class="form-group">
        <label asp-for="Nome" class="control-label"></label>
        <input asp-for="Nome" class="form-control" />
        <span asp-validation-for="Nome" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label>CNPJ</label>
        @Html.TextBoxFor(a => a.CPFCNPJ, new { @class = "form-control CPFCNPJ" })
        @Html.ValidationMessageFor(a => a.CPFCNPJ)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.CEP)
        @Html.TextBoxFor(a => a.CEP, new { @class = "form-control CEP", id = "campoCEP" })
        @Html.ValidationMessageFor(a => a.CEP)
      </div>

      <div class="form-group">
        <label asp-for="Logradouro" class="control-label"></label>
        <input asp-for="Logradouro" class="form-control" />
        <span asp-validation-for="Logradouro" class="text-danger"></span>
      </div>

      <div class="form-group">
        @Html.LabelFor(a => a.Numero)
        @Html.TextBoxFor(a => a.Numero, new { @class = "form-control numerosOnly" })
        @Html.ValidationMessageFor(a => a.Numero)
      </div>
      <div class="form-group">
        <label asp-for="Bairro" class="control-label"></label>
        <input asp-for="Bairro" class="form-control" />
        <span asp-validation-for="Bairro" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="Complemento" class="control-label"></label>
        <input asp-for="Complemento" class="form-control" />
        <span asp-validation-for="Complemento" class="text-danger"></span>
      </div>

      <div class="form-group">
        <label asp-for="IdUF" class="control-label"></label>
        @Html.Select2("UFSelect", "GetUFSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdUF), "Select2", this.ViewContext.RouteData.Values["controller"].ToString())
      </div>

      <div class="form-group">
        <label asp-for="IdMunicipio" class="control-label"></label>
        @Html.Select2("MunicipioSelect", "GetMunicipioSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdMunicipio), "Select2", this.ViewContext.RouteData.Values["controller"].ToString(), afterSelect: "UFSelect")
      </div>

      <div class="form-group">
        @Html.LabelFor(a => a.TelefoneFixo)
        @Html.TextBoxFor(a => a.TelefoneFixo, new { @class = "form-control telefone2" })
        @Html.ValidationMessageFor(a => a.TelefoneFixo)
      </div>

      <div class="form-group">
        @Html.LabelFor(a => a.Email)
        @Html.TextBoxFor(a => a.Email, new { @class = "form-control EMAIL" })
        @Html.ValidationMessageFor(a => a.Email)
      </div>

      <div class="form-group">
        @Html.LabelFor(a => a.TelefoneCelular)
        @Html.TextBoxFor(a => a.TelefoneCelular, new { @class = "form-control telefone" })
        @Html.ValidationMessageFor(a => a.TelefoneCelular)
      </div>

      @* <div class="form-group col-md-2" style=" margin-top: 7px;">
            <label class="form-check-label">
              <input class="form-check-input" style="margin-top:-3px;" asp-for="TelWhatsApp" /> @Html.DisplayNameFor(model => model.TelWhatsApp)
            </label>
          </div>

          <div class="form-group col-md-2" style=" margin-top: 7px;">
          <label class="form-check-label">
            <input class="form-check-input" asp-for="Ativo" style=" margin-top: -3px;" /> @Html.DisplayNameFor(model => model.Ativo)
          </label>
        </div>*@


      <!-- DADOS FISCAIS -->

      <div class="col-md-12" style="margin-top: 32px; margin-bottom: 10px;">
        <div class="col-md-12">
          <hr style="margin:0;">
        </div>
        <div style="position: absolute;top: -18px;left: 42%;background: white;">
          <label style="font-size: 17pt;">Dados Fiscais </label>
        </div>
      </div>

      <div class="form-group col-md-2">
        <label asp-for="InscricaoEstadual" class="control-label"></label>
        <input asp-for="InscricaoEstadual" class="form-control" />
        <span asp-validation-for="InscricaoEstadual" class="text-danger"></span>
      </div>

      <div class="form-group col-md-2">
        <label asp-for="InscricaoMunicipal" class="control-label"></label>
        <input asp-for="InscricaoMunicipal" class="form-control" />
        <span asp-validation-for="InscricaoMunicipal" class="text-danger"></span>
      </div>

      @*<div class="form-group col-md-2 form-check" style="margin-top: 8px; margin-left: -4px; ">
        <label class="form-check-label">
          <input class="form-check-input" asp-for="IncentivadorCulturalFloat" style=" margin-top: -3px;" /> Incentivador cultural
        </label>
      </div>*@
      <input type="hidden" name="IncentivadorCulturalFloat" value="0" />

      <div class="form-group col-md-2">
        <label asp-for="CodigoTributacaoMunicipio" class="control-label"></label>
        <input asp-for="CodigoTributacaoMunicipio" class="form-control" />
        <span asp-validation-for="CodigoTributacaoMunicipio" class="text-danger"></span>
      </div>

      <div class="form-group col-md-3">
        <label asp-for="IdRegimeEspecialTributacao"></label>
        @Html.Select2("RegimeEspecialTributacaoSelect", "GetRegimeEspecialTributacaoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdRegimeEspecialTributacao), this.ViewContext.RouteData.Values["controller"].ToString())
      </div>
    </div>

    <div class="card-body">

      <div class="col-md-12" style="margin-top: 15px; margin-bottom: 10px;">
        <div class="col-md-12" style="">
          <hr style="margin:0;">
        </div>
        <div style="position: absolute;top: -18px;left: 42%;background: white;">
          <label style="font-size: 17pt;">Contabilidade</label>
        </div>
      </div>

      <input type="hidden" name="IdContabilidade" id="IdContabilidade" />
      <div class="col-md-12">
        <div class="row" style="align-items: flex-end;">
          <div class="col-md-7">
            <div class="form-group">
              <label>Nome/Razão Sócial Contabilidade</label>
              <input class="form-control" id="Contabilidade_text" readonly="readonly" value="@(Model == null ? "" : Model.ContabilidadeSelect == null ? "" : Model.ContabilidadeSelect.text)" />
            </div>
          </div>

          <div class="col-md-5">
            <div class="form-group">
              <button type="button" class="btn btn-sm btn-info" id="addContab">Associar Contabilidade</button>
            </div>
          </div>

        </div>
      </div>
    </div>
    <div class="card-footer">
      <a asp-action="Index" class="link link-info">Voltar</a>
      <input type="submit" value="Adicionar" class="btn btn-primary" />
    </div>
  </form>
</div>

@Html.Partial("Modal")
@Html.Partial("_ModalEmailContab")
