﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model EntradaEstoqueModel
@{
  ViewBag.Title = "Entrada Estoque";
}

<link rel="stylesheet" href="~/Views/Estoque/Estoque.css?difa=rldlr" />
<script src="~/Views/Estoque/Estoque.js"></script>

@using (Html.BeginForm("EntradaEstoque", "Estoque", FormMethod.Post))
{
  <div class="table-group card">
    <div class="card-header">
      <div class="card-title">
        Lista de Pedidos
      </div>
      <div class="card-options">
        @if (ContextoUsuario.HasPermission("Estoque", TipoFuncao.CriarEntradaEstoque))
        {
          <div style="display: flex">
            @Html.CheckBoxFor(a => a.CheckTodos)
            <label for="CheckTodos" style="margin: 2.5px 0 0 5px;"> Selecionar Todos</label>
          </div>
          <div style="min-width: 150px;margin-left: 10px">
            @Html.Select2("LocalEstoqueSelect", "GetLocalEstoqueSelect", "Selecione Local Estoque", "", "", Model.LocalEstoqueSelect == null ? "" : Model.LocalEstoqueSelect.id, Model.LocalEstoqueSelect == null ? "" : Model.LocalEstoqueSelect.text)
          </div>
          <button type="submit" class="btn btn-success" style="margin-left: 10px">Criar Movimento Estoque</button>
        }
      </div>
    </div>
    <div class="card-body">
      @if (Model.ListaPedidoCompraEntradaEstoqueIndex != null)
      {
        @for (int i = 0; i < Model.ListaPedidoCompraEntradaEstoqueIndex.Count; i++)
        {
          @if (Model.ListaPedidoCompraEntradaEstoqueIndex[i].ListaItensPedidoCompraEstoqueIndex.Count > 0)
          {
            <div style="margin-bottom: 20px">
              @Html.HiddenFor(a => Model.ListaPedidoCompraEntradaEstoqueIndex[i].Codigo)
              @Html.HiddenFor(a => Model.ListaPedidoCompraEntradaEstoqueIndex[i].CodigoFilial)
              @Html.HiddenFor(a => Model.ListaPedidoCompraEntradaEstoqueIndex[i].NomeCliente)
              @Html.HiddenFor(a => Model.ListaPedidoCompraEntradaEstoqueIndex[i].NomeFilial)
              @Html.HiddenFor(a => Model.ListaPedidoCompraEntradaEstoqueIndex[i].DataEmissao)
              <div style="display: flex">
                @if (ContextoUsuario.HasPermission("Estoque", TipoFuncao.CriarEntradaEstoque))
                {
                  @Html.CheckBoxFor(a => Model.ListaPedidoCompraEntradaEstoqueIndex[i].Checked)
                }
                <span style="margin: 2.5px 0 0 5px; font-size: 11pt; font-family: Poppins-Medium;">
                  @string.Format("Filial: {0} - Cliente: {1} - Data: {2}", Model.ListaPedidoCompraEntradaEstoqueIndex[i].NomeFilial, Model.ListaPedidoCompraEntradaEstoqueIndex[i].NomeCliente, Model.ListaPedidoCompraEntradaEstoqueIndex[i].DataEmissao.ToString("dd/MM/yyyy"))
                </span>
              </div>
              <div class="table-group-content">
                <table>
                  <thead>
                    <tr>
                      <th scope="col">
                        Produto
                      </th>
                      <th scope="col">
                        Quantidade
                      </th>
                      <th scope="col">
                        Valor Unitário
                      </th>
                      <th scope="col">
                        Valor Total
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @for (int z = 0; z < Model.ListaPedidoCompraEntradaEstoqueIndex[i].ListaItensPedidoCompraEstoqueIndex.Count(); z++)
                    {
                      <tr>
                        <td style="display:none;">
                          @Html.HiddenFor(a => Model.ListaPedidoCompraEntradaEstoqueIndex[i].ListaItensPedidoCompraEstoqueIndex[z].Codigo)
                          @Html.HiddenFor(a => Model.ListaPedidoCompraEntradaEstoqueIndex[i].ListaItensPedidoCompraEstoqueIndex[z].DescricaoProduto)
                          @Html.HiddenFor(a => Model.ListaPedidoCompraEntradaEstoqueIndex[i].ListaItensPedidoCompraEstoqueIndex[z].Quantidade)
                          @Html.HiddenFor(a => Model.ListaPedidoCompraEntradaEstoqueIndex[i].ListaItensPedidoCompraEstoqueIndex[z].ValorTotal)
                          @Html.HiddenFor(a => Model.ListaPedidoCompraEntradaEstoqueIndex[i].ListaItensPedidoCompraEstoqueIndex[z].ValorUnitario)
                        </td>
                        <td>
                          @Model.ListaPedidoCompraEntradaEstoqueIndex[i].ListaItensPedidoCompraEstoqueIndex[z].DescricaoProduto
                        </td>
                        <td>
                          @Model.ListaPedidoCompraEntradaEstoqueIndex[i].ListaItensPedidoCompraEstoqueIndex[z].Quantidade
                        </td>
                        <td>
                          @Model.ListaPedidoCompraEntradaEstoqueIndex[i].ListaItensPedidoCompraEstoqueIndex[z].ValorUnitario
                        </td>
                        <td>
                          @Model.ListaPedidoCompraEntradaEstoqueIndex[i].ListaItensPedidoCompraEstoqueIndex[z].ValorTotal
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          }
        }
      }
      else
      {

      }
    </div>
    <div class="card-footer">

    </div>
  </div>
}