﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;


namespace UniContabil.Controllers
{
  public class TipoDocumentoFilialController : LibController
  {

    [HttpPost]
    public ActionResult Create(string Descricao)
    {
      try
      {

        if (ContextoUsuario.UserLogged.IdOrganizacao == 0 && !ContextoUsuario.UserLogged.IdFilial.HasValue)
        {
          MessageAdd(new Message(MessageType.Warning, "É necessário selecionar a clínica."));
          return RedirectToAction("Index", "DocumentoFilial");
        }

        new TipoDocumentoFilialServices(ContextoUsuario.UserLogged).CreateAux(Descricao);
        return RedirectToAction("Index","DocumentoFilial");
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return RedirectToAction("Index", "DocumentoFilial");
      }
    }
  }
}
