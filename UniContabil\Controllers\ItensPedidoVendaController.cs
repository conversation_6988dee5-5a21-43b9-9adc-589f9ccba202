﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Exceptions;
using UniContabilEntidades.Models;
using UniContabilDomain.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Estoque.Infrastructure;

namespace UniContabil.Controllers
{
  [AllowAnonymous]
  public class ItensPedidoVendaController : LibController
  {
    private ItensPedidoVendaServices ItensPedidoVendaService
    {
      get
      {
        if (_ItensPedidoVendaService == null)
          _ItensPedidoVendaService = new ItensPedidoVendaServices(ContextoUsuario.UserLogged);

        return _ItensPedidoVendaService;
      }
    }
    private ItensPedidoVendaServices _ItensPedidoVendaService;

    [HttpPost]
    public JsonResult Create(ItensPedidoVendaModel itensPedidoVenda)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {

        if (ModelState.IsValid)
        {
          ItensPedidoVendaService.Create(itensPedidoVenda);
          retornoAjax.Titulo = "Sucesso";
          retornoAjax.Mensagem = "Item adicionado com sucesso.";
          retornoAjax.Erro = false;
          return Json(retornoAjax);
        }
        List<string> ListaErro = ModelStateHelper.GetErroList(ViewData.ModelState.Values);
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = String.Join(" <br> ", ListaErro.ToArray());
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public ActionResult Edit(ItensPedidoVendaModel itensPedidoVenda)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        if (ModelState.IsValid)
        {
          ItensPedidoVendaService.Edit(itensPedidoVenda);
          retornoAjax.Titulo = "Sucesso";
          retornoAjax.Mensagem = "Item Editado com sucesso.";
          retornoAjax.Erro = false;
          return Json(retornoAjax);
        }
        List<string> ListaErro = ModelStateHelper.GetErroList(ViewData.ModelState.Values);
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = String.Join(" <br> ", ListaErro.ToArray());
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public JsonResult Delete(int CodItemPedidoVenda)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        new ItensPedidoVendaServices().Delete(CodItemPedidoVenda);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Item deletado com sucesso.";
        retornoAjax.Erro = false;
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public JsonResult GetByCodigo(int CodItemPedidoVenda)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        ItensPedidoVendaModel ItensPedidoVendaModel = new ItensPedidoVendaServices().GetItensModelById(CodItemPedidoVenda);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Item deletado com sucesso.";
        retornoAjax.Erro = false;
        return Json(new { retorno = retornoAjax, model = ItensPedidoVendaModel });
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
    }
  }
}