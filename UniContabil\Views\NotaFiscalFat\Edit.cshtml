﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@model UniContabilEntidades.Models.NotaFiscalFatModel

@{
  ViewBag.Title = "Detalhes da Nota Fiscal";
}

<script src="~/Views/NotaFiscalFat/NotaFiscalFat.js"></script>
<link rel="stylesheet" href="~/Views/NotaFiscalFat/NotaFiscalFat.css?a=a" />

<div class="card">
  @using (Html.BeginForm("Edit", "NotaFiscalFat", FormMethod.Post, new { enctype = "multipart/form-data" }))
  {
    @Html.HiddenFor(a => a.Codigo)
    @Html.HiddenFor(a => a.CodigoIdent)
    <div class="card-header">
      <div class="card-title">
        Nota Fiscal Faturamento - <text id="statusNota">@(UniContabil.Infrastructure.EnumHelper.Description(Model.StatusNota))</text>
      </div>
      <div class="card-options" id="notaControls">
        @if (ContextoUsuario.HasPermission("LogNotaFiscalFat", TipoFuncao.GridLogNotaFiscalVenda))
        {
          <button type="button" class="btn btn-outline-info" onclick="location.href='@Url.Action("Index", "LogNotaFiscalFat", new { id = Model.Codigo })'">Logs</button>
        }
        @if (ContextoUsuario.HasPermission("NotaFiscalFat", TipoFuncao.TransmitirNota))
        {
          <button type="button" class="btn btn-outline-primary" id="transmitirNota">Transmitir</button>
        }
        <button type="button" class="btn btn-outline-primary TransmitirNFSe" data-id="@Model.Codigo">Transmitir NFSe BH</button>

        <div id="acaoNota">
          <div class="loading-spinner" style="margin-top: 2.5px"></div>
          <p>
            <text></text>, aguarde...
          </p>
        </div>
      </div>
    </div>
    <div class="card-body" id="CabecalhoPedidoCompra">
      <div class="form-group">
        @Html.LabelFor(a => a.CodigoIdent)
        @Html.TextBoxFor(a => a.CodigoIdent, new { @class = "form-control", disabled = "disabled" })
        @Html.ValidationMessageFor(a => a.CodigoIdent)
      </div>
      <div class="form-group">
        @Html.Label("Cliente")
        <div style="display: flex; justify-content: space-between; align-items: flex-end;">
          <div style="width: 85%">
            @Html.Select2("ClienteSelect", "GetClienteSelect", "Selecione o Cliente", "", "", Model.ClienteSelect == null ? "" : Model.ClienteSelect.id, Model.ClienteSelect == null ? "" : Model.ClienteSelect.text)
          </div>
          <div style="width: 10%; display: flex;">
            <i class="mw-drip-search" id="DetalhesCliente"></i>
          </div>
        </div>
      </div>

      <div class="form-group">
        @Html.LabelFor(a => a.ValorTotal)
        @Html.TextBoxFor(a => a.ValorTotal, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.ValorTotal)
      </div>

      <div class="form-group">
        @Html.LabelFor(a => a.DescontoTotal)
        @Html.TextBoxFor(a => a.DescontoTotal, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.DescontoTotal)
      </div>

      <div class="form-group">
        @Html.LabelFor(a => a.Serie)
        @Html.TextBoxFor(a => a.Serie, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.Serie)
      </div>

      <div class="form-group">
        @Html.Label("Forma de Pagamento")
        @Html.Select2("FormaPagamentolSelect", "GetFormaPagamentoSelect", "Selecione a Forma de Pagamento", "", "", Model.FormaPagamentolSelect == null ? "" : Model.FormaPagamentolSelect.id, Model.FormaPagamentolSelect == null ? "" : Model.FormaPagamentolSelect.text)
      </div>
      <div class="form-group">
        @Html.Label("Condição de Pagamento")
        @Html.Select2("CondicaoPagamentoSelect", "GetCondicaoPagamentoSelect", "Selecione a Condição de Pagamento", "", "", Model.CondicaoPagamentoSelect == null ? "" : Model.CondicaoPagamentoSelect.id, Model.CondicaoPagamentoSelect == null ? "" : Model.CondicaoPagamentoSelect.text)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.TipoFrete)
        <select class="form-control" asp-for="TipoFrete" asp-items="Html.GetEnumSelectList<TipoFreteEnum>()">
          <option selected="selected" value="">Selecione</option>
        </select>
        @Html.ValidationMessageFor(a => a.ValorFrete)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.ValorFrete)
        @Html.TextBoxFor(a => a.ValorFrete, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.ValorFrete)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.DataEmissao)
        @Html.TextBoxFor(a => a.DataEmissao, new { @class = "form-control Data" })
        @Html.ValidationMessageFor(a => a.DataEmissao)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.CEPEntrega)
        @Html.TextBoxFor(a => a.CEPEntrega, new { @class = "form-control CEP" })
        @Html.ValidationMessageFor(a => a.CEPEntrega)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.EndEntrega)
        @Html.TextBoxFor(a => a.EndEntrega, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.EndEntrega)
      </div>
      <div class="form-group">
        <label asp-for="CodigoUFEntrega" class="control-label"></label>
        @Html.Select2("UFSelect", "GetUFSelect", "Selecione " + @Html.DisplayNameFor(model => model.CodigoUFEntrega), "Select2", valor: Model.UFSelect == null ? "0" : Model.UFSelect.id, text: Model.UFSelect == null ? "" : Model.UFSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="CodigoMunicipioEntrega" class="control-label"></label>
        @Html.Select2("MunicipioSelect", "GetMunicipioSelect", "Selecione " + @Html.DisplayNameFor(model => model.CodigoMunicipioEntrega), "Select2", afterSelect: "UFSelect", valor: Model.MunicipioSelect == null ? "0" : Model.MunicipioSelect.id, text: Model.MunicipioSelect == null ? "" : Model.MunicipioSelect.text)
      </div>
    </div>
    <div class=" card-footer">

    </div>
  }
</div>
<div class="table-group card">
  @using (Html.BeginForm("Edit", "NotaFiscalFat", FormMethod.Post, new { enctype = "multipart/form-data" }))
  {
    <div class="card-header">
      <div class="card-title">
        Itens Nota Fiscal
      </div>
      <div class="card-options">
        <button id="open-modal-Itens" data-toggle="modal" class="btn btn-outline-primary" title="Nova Etapa">
          Novo Item
        </button>
      </div>
    </div>
    <div id="GridItensNotaFiscalFat">
      <div class="form-group">
        @await Html.PartialAsync("_GridItensNotaFiscalFat", Model.ItensNotaFiscalFat)
      </div>
    </div>

    <div class="card-footer">
      <button type="button" value="Voltar" class="btn btn-outline-dark" onclick="location.href='@Url.Action("Index")'">Voltar</button>
      @*<button type="submit" value="Create" style="float: right;" class="btn btn-outline-primary pull-right">Salvar &#x2713;</button>*@
    </div>
  }
</div>

@await Html.PartialAsync("InfoAddCliente/_ModalPesquisaCliente", new ModalPesquisaCliente())
@await Html.PartialAsync("InfoAddCliente/_ModalDetalhesCliente")





@*<div class="container" id="CabecalhoPedidoCompra">
  @using (Html.BeginForm("Edit", "NotaFiscalFat", FormMethod.Post, new { enctype = "multipart/form-data" }))
  {
    @Html.HiddenFor(a => a.Codigo)
    @Html.HiddenFor(a => a.CodigoIdent)
    <div class="row">*@
@*<div class="col-md-4">
    <div class="form-group">
      @Html.LabelFor(a => a.DataEmissao)
      @Html.TextBoxFor(a => a.DataEmissao, new { @class = "form-control Data" })
      @Html.ValidationMessageFor(a => a.DataEmissao)
    </div>
  </div>*@

@*<div class="col-md-2">
      <div class="form-group">
        @Html.LabelFor(a => a.CodigoIdent)
        @Html.TextBoxFor(a => a.CodigoIdent, new { @class = "form-control", disabled = "disabled" })
        @Html.ValidationMessageFor(a => a.CodigoIdent)
      </div>
    </div>

    <div class="col-md-4">
      <div class="form-group">
        @Html.Label("Cliente")
        <div style="display: flex; justify-content: space-between; align-items: flex-end;">
          <div style="width: 85%">
            @Html.Select2("ClienteSelect", "GetClienteSelect", "Selecione o Cliente", "", "", Model.ClienteSelect == null ? "" : Model.ClienteSelect.id, Model.ClienteSelect == null ? "" : Model.ClienteSelect.text)
          </div>
          <div style="width: 10%; display: flex;">
            <i class="mw-drip-search" id="DetalhesCliente"></i>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-2">
      <div class="form-group">
        @Html.LabelFor(a => a.ValorToral)
        @Html.TextBoxFor(a => a.ValorToral, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.ValorToral)
      </div>
    </div>

    <div class="col-md-2">
      <div class="form-group">
        @Html.LabelFor(a => a.DescontoTotal)
        @Html.TextBoxFor(a => a.DescontoTotal, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.DescontoTotal)
      </div>
    </div>

    <div class="col-md-2">
      <div class="form-group">
        @Html.LabelFor(a => a.Serie)
        @Html.TextBoxFor(a => a.Serie, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.Serie)
      </div>
    </div>

  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="form-group">
        @Html.Label("Forma Pagamento")
        @Html.Select2("FormaPagamentolSelect", "GetFormaPagamentoSelect", "Selecione a Forma de Pagamento", "", "", Model.FormaPagamentolSelect == null ? "" : Model.FormaPagamentolSelect.id, Model.FormaPagamentolSelect == null ? "" : Model.FormaPagamentolSelect.text)
      </div>
    </div>

    <div class="col-md-4">
      <div class="form-group">
        @Html.Label("Condicao Pagamento")
        @Html.Select2("CondicaoPagamentoSelect", "GetCondicaoPagamentoSelect", "Selecione a Condição de Pagamento", "", "", Model.CondicaoPagamentoSelect == null ? "" : Model.CondicaoPagamentoSelect.id, Model.CondicaoPagamentoSelect == null ? "" : Model.CondicaoPagamentoSelect.text)
      </div>
    </div>

    <div class="col-md-2">
      <div class="form-group">
        @Html.LabelFor(a => a.TipoFrete)
        <select class="form-control" asp-for="TipoFrete" asp-items="Html.GetEnumSelectList<TipoFreteEnum>()">
          <option selected="selected" value="">Selecione</option>
        </select>
        @Html.ValidationMessageFor(a => a.ValorFrete)
      </div>
    </div>

    <div class="col-md-2">
      <div class="form-group">
        @Html.LabelFor(a => a.ValorFrete)
        @Html.TextBoxFor(a => a.ValorFrete, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.ValorFrete)
      </div>
    </div>

  </div>

  <div class="row">

    <div class="col-md-2">
      <div class="form-group">
        @Html.LabelFor(a => a.DataEmissao)
        @Html.TextBoxFor(a => a.DataEmissao, new { @class = "form-control Data" })
        @Html.ValidationMessageFor(a => a.DataEmissao)
      </div>
    </div>

  </div>

  <div class="row">
    <div class="col-md-2">
      <div class="form-group">
        @Html.LabelFor(a => a.CEPEntrega)
        @Html.TextBoxFor(a => a.CEPEntrega, new { @class = "form-control CEP" })
        @Html.ValidationMessageFor(a => a.CEPEntrega)
      </div>
    </div>
    <div class="col-md-2">
      <div class="form-group">
        @Html.LabelFor(a => a.EndEntrega)
        @Html.TextBoxFor(a => a.EndEntrega, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.EndEntrega)
      </div>
    </div>

    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.CidadeEntrega)
        @Html.TextBoxFor(a => a.CidadeEntrega, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.CidadeEntrega)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.UFEntrega)
        @Html.TextBoxFor(a => a.UFEntrega, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.UFEntrega)
      </div>
    </div>
  </div>


  <div class="box box-primary">
    <div class="box-header">
      <button id="open-modal-Itens" style="float: right;" data-toggle="modal" type="button" class="btn btn-outline-secondary btn-sm" title="Nova Etapa">
        Novo Item
      </button>
      <h3 class="box-title">Itens Nota Fiscal</h3>
      <div class="box-tools pull-right" style="float:right">

      </div>*@
@*<hr style="margin-top: 0rem!important;" />
  </div>*@

<!-- /.box-header -->
@*<br />
  <div class="box-body">
    <div class="row" id="GridItensNotaFiscalFat">
      @await Html.PartialAsync("_GridItensNotaFiscalFat", Model.ItensNotaFiscalFat)
    </div>
  </div>*@
<!-- /.box-body -->
@*<div class="box-footer">
      <button type="button" value="Voltar" class="btn btn-outline-dark" onclick="location.href='@Url.Action("Index")'">Cancelar</button>
      <button type="submit" value="Create" style="float: right;" class="btn btn-outline-primary pull-right">Salvar &#x2713;</button>
    </div>

  </div>*@

@*}*@
@*</div>*@

@*@await Html.PartialAsync("InfoAddCliente/_ModalPesquisaCliente", new ModalPesquisaCliente())
  @await Html.PartialAsync("InfoAddCliente/_ModalDetalhesCliente")*@