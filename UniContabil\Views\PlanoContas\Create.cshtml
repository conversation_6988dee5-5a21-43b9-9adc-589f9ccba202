﻿@model UniContabilEntidades.Models.PlanoContasModel
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Novo " + @Html.DisplayNameFor(model => model);
  Layout = "~/Views/Shared/_Layout.cshtml";
}

<script src="~/Views/PlanoContas/PlanoContas.js"></script>

<div class="card">
  <form asp-action="Create">
    <div class="card-header">
      <div class="card-title">
      </div>
      <div class="card-options">
      </div>
    </div>
    @Html.AntiForgeryToken()
    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
    <div class="form-group">
      <div class="row">
        <div class="col-md-2">
          <div class="form-group">
            <label asp-for="Codigo" class="control-label"></label>
            <input asp-for="Codigo" class="form-control" maxlength="12" />
            <span asp-validation-for="Codigo" class="text-danger"></span>
          </div>
        </div>
        <div class="col-md-4">
          <div class="form-group">
            <label class="control-label">Descrição</label>
            <input asp-for="Descricao" class="form-control" />
            <span asp-validation-for="Descricao" class="text-danger"></span>
          </div>
        </div>
        <div class="col-md-4">
          <div class="form-group">
            <label asp-for="TipoPesssoaSelect" class="control-label"></label>
            @Html.Select2("TipoPesssoaSelect", "GetTipoPessoaSelect", "Selecione tipo de pessoa", "", "", Model.TipoPesssoaSelect.id, Model.TipoPesssoaSelect.text)
          </div>
        </div>
        <div class="col-md-4">
          <div class="form-group">
            <label asp-for="PlanoSelect" class="control-label"></label>
            @Html.Select2("PlanoSelect", "GetTipoPlanoContasCreditoSelect", "Selecione plano de conta superior", "", "", Model.PlanoSelect.id, Model.PlanoSelect.text, afterSelect: "TipoPesssoaSelect")
          </div>
        </div>
        <div class="col-md-1">
          <div class="form-group">
            <label class="control-label">Bloqueado?</label>
            <input type="checkbox" style="margin-top:5px;" name="Bloqueado" id="Bloqueado" />
            @Html.HiddenFor(a => a.Bloq, new { @id = "Bloq" })
            <span asp-validation-for="Bloq" class="text-danger"></span>
          </div>
        </div>
      </div>

    </div>
    <div class="card-footer">
      <a asp-action="Index" class="link link-info">Voltar</a>
      <button type="submit" value="Adicionar" class="btn btn-primary">Salvar</button>
    </div>
  </form>
</div>

