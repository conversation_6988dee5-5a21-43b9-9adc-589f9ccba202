﻿@using X.PagedList.Mvc.Core;
@using X.PagedList;
@model List<UniContabilEntidades.Models.DependentesModel>
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;

@{
  ViewData["Title"] = @Html.DisplayNameFor(model => model);
  Layout = "";
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();
}

<div class="table-group card">
  <div class="table-group-content">
    <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
      <thead>
        <tr>
          <th>
            Nome
          </th>
          <th>
            CPF
          </th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        @foreach (UniContabilEntidades.Models.DependentesModel item in Model)
        {
          <tr>
            <td>
              @Html.DisplayFor(modelItem => item.NomeDep)
            </td>
            <td>
              @Html.DisplayFor(modelItem => item.CPFdep)
            </td>

            <td>

              <a class="link link-secondary" id="editarDependente" data-cdd="@item.Id">Editar</a>


              <button class="link link-danger" id="deleteDependentes" data-cod="@item.Id">Apagar</button>

            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
</div>

<div class="modal fade" id="modalDependentesEdit" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Editar Dependente </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div id="bodyEdit">

        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
        <button type="button" class="btn btn-primary" id="enviarEditDependente">Salvar</button>
      </div>
    </div>
  </div>
</div>
<style>
  .modal {
    padding: 0 !important;
    // override inline padding-right added from js
  }

    .modal .modal-dialog {
      width: 100%;
      max-width: none;
      height: 80%;
      margin: 100px;
      margin-left: 330px;
    }

    .modal .modal-content {
      height: 60%;
      border: 0;
      border-radius: 0;
    }

    .modal .modal-body {
      overflow-y: auto;
    }
</style>