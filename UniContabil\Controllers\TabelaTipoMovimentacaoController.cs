﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using UniContabil.Infrastructure.Exceptions;

using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;
using UniContabilDomain.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Estoque.Infrastructure;

namespace UniContabil.Controllers
{
  [AllowAnonymous]
  public class TabelaTipoMovimentacaoController : LibController
  {
    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index()
    {
      try
      {
        List<TabelaTipoMovimentacaoModel> Index = new TabelaTipoMovimentacaoServices().GetModel();
        return View(Index);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }

    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View(new TabelaTipoMovimentacaoModel());
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(TabelaTipoMovimentacaoModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new TabelaTipoMovimentacaoServices(ContextoUsuario.UserLogged).Create(model);
          return RedirectToAction(nameof(Index));
        }
        else
        {
          List<string> ListaErro = ModelStateHelper.GetErroList(ViewData.ModelState.Values);
          MessageAdd(new Message(MessageType.Error, String.Join(" <br> ", ListaErro.ToArray())));
          return View(model);
        }

      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(model);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(int id)
    {
      try
      {
        TabelaTipoMovimentacaoModel model = new TabelaTipoMovimentacaoModel();
        model = new TabelaTipoMovimentacaoServices().GetModelById(id);

        return View(model);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(TabelaTipoMovimentacaoModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new TabelaTipoMovimentacaoServices().Edit(model);
          return RedirectToAction("Index", "TabelaTipoMovimentacao");
        }

        List<string> ListaErro = ModelStateHelper.GetErroList(ViewData.ModelState.Values);
        MessageAdd(new Message(MessageType.Error, String.Join(" <br> ", ListaErro.ToArray())));
        return View(model);

      }
      catch (CustomException ex)
      {
        MessageAdd(new Message(MessageType.Success, ex.Message));
        return View(model);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Success, ex.Message));
        return View(model);
      }
    }

  }
}