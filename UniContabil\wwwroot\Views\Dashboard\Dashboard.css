﻿.totalFat {
  /*background-image: linear-gradient(#fa6d08, white);
  color: #000065;
  box-shadow: none;*/
  margin-bottom: 0 !important;
  /*height: calc(9vh - 10px) !important;*/
  border-left: 5px solid #1eef1b;
}

.glosas {
  /*  background-image: linear-gradient(#e4cb0e, white);
  box-shadow: none;*/
  margin-bottom: 0 !important;
  /*height: calc(9vh - 10px) !important;*/
  border-left: 5px solid #ff0000;
}

.PrevisaoRepasse {
  /* background-image: linear-gradient(#f0a40e, white); */
  /* box-shadow: none; */
  margin-bottom: 0 !important;
  /* height: 10vh !important; */
  border-left: 5px solid #2bb0d1;
}

.card {
  margin-top: 10px !important;
  margin-bottom: 0 !important;
}

.card-header {
  align-items: center !important;
  height: 4vh !important;
  padding: 0 !important;
}

.card-title {
  margin: 0 !important;
}

.card-body {
  /*min-height: 35vh !important;*/
  display: flex !important;
  padding: 0 !important;
  padding-left: 4% !important;
  overflow-y: unset !important;
}

.small-box h3 {
  font-size: x-large !important;
}

#detailstab .active {
  background-color: #f0a30a;
  color: black !important;
  font-weight: bolder;
  border: transparent;
}

#empresatab > .active {
  background-color: #f0a30a;
  color: black !important;
  font-weight: bolder;
  border: transparent;
}