<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Creator: CorelDRAW X8 -->
<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="162.56mm" height="91.4398mm" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"
viewBox="0 0 16256 9144"
 xmlns:xlink="http://www.w3.org/1999/xlink">
 <defs>
  <style type="text/css">
   <![CDATA[
    .str0 {stroke:#B5BBBC;stroke-width:7.62}
    .fil1 {fill:none}
    .fil0 {fill:#F9F9F8}
   ]]>
  </style>
   <clipPath id="id0">
    <path d="M0 0l16256 0 0 9144 -16256 0 0 -9144z"/>
   </clipPath>
     <mask id="id1">
  <linearGradient id="id2" gradientUnits="userSpaceOnUse" x1="4316.83" y1="4346.4" x2="7102.96" y2="5516.05">
   <stop offset="0" style="stop-opacity:1; stop-color:white"/>
   <stop offset="1" style="stop-opacity:0; stop-color:white"/>
  </linearGradient>
      <rect style="fill:url(#id2)" x="-22" y="-15" width="9164" height="9164"/>
     </mask>
 </defs>
 <g id="Camada_x0020_1">
  <metadata id="CorelCorpID_0Corel-Layer"/>
  <polygon class="fil0" points="0,0 16256,0 16256,9144 0,9144 "/>
  <g style="clip-path:url(#id0)">
   <g id="_2693037520352">
    <path id="1" class="fil1" style="mask:url(#id1)" d="M9132 9139l-9144 0 0 -9144 9144 0 0 9144zm-6935 -824c60,172 1091,309 2355,309 1251,0 2275,-134 2352,-304 3,-7 5,-14 5,-21 0,-179 -1055,-325 -2357,-325 -1302,0 -2358,146 -2358,325 0,6 1,11 3,16zm2355 -341l0 0zm-2358 325l0 0zm1894 -917c199,59 405,90 613,90 108,0 217,-9 325,-26 478,-74 899,-305 1235,-646 448,-134 817,-391 1050,-767 170,-276 249,-589 248,-918 236,-400 333,-836 250,-1268 -76,-401 -299,-748 -625,-1023 -112,-411 -333,-759 -665,-998 -299,-215 -654,-317 -1033,-317 -53,0 -107,2 -161,6 -292,-133 -604,-205 -923,-205 -108,0 -216,8 -325,25 -396,62 -754,232 -1056,482 -528,116 -966,388 -1228,814 -245,397 -303,871 -200,1359 -65,140 -114,283 -144,429 -162,517 -140,1033 105,1469 161,287 405,511 703,666 120,211 278,392 476,534 299,215 654,317 1033,317 106,0 213,-8 322,-23zm640 6l-7 0 -6 0 -7 0 -7 -1 -6 0 -7 0 -6 0 0 0 -7 0 -7 0 0 0 -6 -1 -7 0 -7 0 0 0 -6 0 0 0 -7 -1 -6 0 -7 0 -7 -1 0 0 -6 0 -7 -1 -6 0 0 0 -14 -1 0 0 -6 -1 0 0 -13 -1 0 0 -7 0 0 0 -7 -1 0 0 -6 -1 -13 -1 0 0 -7 -1 -6 0 -1 0 -6 -1 0 0 -7 -1 0 0 -6 -1 -13 -1 0 0 -7 -1 0 0 -13 -2 0 0 -6 -1 0 0 -7 -1 0 0 -7 -1 0 0 -6 -1 0 0 -7 -1 0 0 -6 -1 0 0 -7 -1 0 0 -6 -1 0 0 -7 -1 0 0 -6 -1 0 0 -13 -3 0 0 -7 -1 0 0 -6 -1 0 0 -7 -1 0 0 -6 -2 0 0 -7 -1 0 0 -6 -1 0 0 -7 -2 0 0 -6 -1 0 0 -7 -1 0 0 -6 -2 0 0 -7 -1 0 0 -6 -2 6 2 0 0 7 1 0 0 6 2 0 0 7 1 0 0 6 1 0 0 7 2 0 0 6 1 0 0 7 1 0 0 6 2 0 0 7 1 0 0 6 1 0 0 7 1 0 0 13 3 0 0 6 1 0 0 7 1 0 0 6 1 0 0 7 1 0 0 6 1 0 0 7 1 0 0 6 1 0 0 7 1 0 0 7 1 0 0 6 1 0 0 13 2 0 0 7 1 0 0 13 1 6 1 0 0 7 1 0 0 6 1 1 0 6 0 7 1 0 0 13 1 6 1 0 0 7 1 0 0 7 0 0 0 13 1 0 0 6 1 0 0 14 1 0 0 6 0 7 1 6 0 0 0 7 1 7 0 6 0 7 1 0 0 6 0 0 0 7 0 7 0 6 1 0 0 7 0 7 0 0 0 6 0 7 0 6 0 7 1 7 0 6 0 7 0zm-420 -48l0 0 0 0zm-608 -42l0 0c0,0 0,0 0,0 0,0 0,0 0,0l-16 0 -16 0 0 0 -15 0 -1 0 -15 -1 -16 0 0 0 -16 -1 0 0 -15 -1 0 0 -16 -1 -15 -1 0 0c-11,-1 -21,-2 -31,-3 -11,-1 -21,-2 -31,-3l0 0 -15 -2 0 0c-11,-1 -21,-2 -31,-4l0 0c-10,-1 -20,-2 -30,-4l0 0 -15 -3 0 0 -15 -2 0 0 -15 -3 0 0 -15 -3 0 0 -15 -3c-10,-2 -20,-4 -30,-6l0 0 -15 -4 0 0 -15 -3 0 0 -14 -4 0 0 -15 -4 0 0 -14 -4 0 0 -15 -4 0 0 -14 -4 0 0 -15 -5 0 0 -14 -4 0 0c-10,-3 -19,-6 -29,-10l0 0c-9,-3 -19,-6 -28,-10l0 0 -14 -5 0 0 -14 -5 0 0 -14 -6 0 0 -14 -6 0 0 -14 -5 0 0 -13 -6 0 0 -14 -6 0 0c-9,-5 -18,-9 -27,-13l0 0 -14 -7 0 0 -13 -6 0 0 -14 -7 0 0 -13 -7 0 0 -13 -7 0 0 -13 -7 0 0 -14 -8 0 0 -13 -7 0 0 -13 -8 0 0 -13 -8 0 0 -12 -8 0 0 -13 -8 0 0 -13 -8 -12 -8 -13 -9 0 0 -13 -9 -12 -8 12 8 13 9 0 0 13 9 12 8 13 8 0 0 13 8 0 0 12 8 0 0 13 8 0 0 13 8 0 0 13 7 0 0 14 8 0 0 13 7 0 0 13 7 0 0 13 7 0 0 14 7 0 0 13 6 0 0 14 7 0 0c9,4 18,8 27,13l0 0 14 6 0 0 13 6 0 0 14 5 0 0 14 6 0 0 14 6 0 0 14 5 0 0 14 5 0 0c9,4 19,7 28,10l0 0c10,4 19,7 29,10l0 0 14 4 0 0 15 5 0 0 14 4 0 0 15 4 0 0 14 4 0 0 15 4 0 0 14 4 0 0 15 3 0 0 15 4 0 0c10,2 20,4 30,6l15 3 0 0 15 3 0 0 15 3 0 0 15 2 0 0 15 3 0 0c10,2 20,3 30,4l0 0c10,2 20,3 31,4l0 0 15 2 0 0c10,1 20,2 31,3 10,1 20,2 31,3l0 0 15 1 16 1 0 0 15 1 0 0 16 1 0 0 16 0 15 1 1 0 15 0 0 0 16 0 16 0zm-1555 -978c-40,-26 -78,-54 -115,-83l0 0c-17,-14 -34,-27 -50,-42l0 0c-8,-7 -16,-14 -24,-21l0 0c-11,-10 -21,-20 -31,-30l0 0c-8,-7 -16,-15 -23,-22l0 0c-5,-5 -10,-10 -15,-16l0 0 -15 -15 0 0 -7 -8 0 0c-8,-8 -15,-16 -22,-24l0 0 -7 -8 0 0 -7 -8 0 0 -7 -8 0 0 -7 -9 0 0 -7 -8 0 0 -6 -8 0 0 -7 -9 -7 -8 -6 -9 0 0 -7 -8 0 0 -6 -9 0 0 -7 -8 0 0 -6 -9 -6 -9 0 0 -6 -9 -7 -9 0 0 -6 -9 -6 -9 -6 -9 -5 -9 -6 -9 -6 -9 -6 -9 -5 -10 -6 -9 -5 -9 -6 -10 6 10 5 9 6 9 5 10 6 9 6 9 6 9 5 9 6 9 6 9 6 9 0 0 7 9 6 9 0 0 6 9 6 9 0 0 7 8 0 0 6 9 0 0 7 8 0 0 6 9 7 8 7 9 0 0 6 8 0 0 7 8 0 0 7 9 0 0 7 8 0 0 7 8 0 0 7 8 0 0c7,8 14,16 22,24l0 0 7 8 0 0 15 15 0 0c5,6 10,11 15,16l0 0c7,7 15,15 23,22l0 0c10,10 20,20 31,30l0 0c8,7 16,14 24,21l0 0c17,15 33,28 50,42l0 0c37,29 75,57 115,83l0 0zm5552 -2176c0,-90 -8,-181 -25,-270 17,89 25,180 25,270zm-25 -270c-1,-1 -1,-2 -1,-3 0,1 0,2 1,3zm-2 -9l-3 -14 -3 -14 0 0 -3 -14 -3 -14 -4 -13 0 0 -3 -14 -4 -14 0 0 -4 -13 0 0 -3 -14 0 0 -4 -13 0 0c-3,-9 -6,-18 -9,-27l0 0c-3,-9 -6,-18 -9,-27l0 0 -5 -13 0 0c-4,-13 -9,-26 -14,-39l0 0 -5 -13 0 0c-14,-35 -30,-69 -46,-103l0 0c-77,-155 -181,-297 -309,-426l0 0c128,129 232,271 309,426l0 0c16,34 32,68 46,103l0 0 5 13 0 0c5,13 10,26 14,39l0 0 5 13 0 0c3,9 6,18 9,27l0 0c3,9 6,18 9,27l0 0 4 13 0 0 3 14 0 0 4 13 0 0 4 14 3 14 0 0 4 13 3 14 3 14 0 0 3 14 3 14zm-748 -1326l0 0c-111,-241 -272,-446 -485,-599 213,153 374,358 485,599zm-485 -599l-11 -8 -12 -8 -11 -8 0 0 -12 -7 0 0 -11 -8 -12 -8 -12 -7 -12 -7 0 0 -12 -7 -12 -7 -12 -7 0 0 -12 -7 -12 -7 0 0 -12 -6 -12 -7 -13 -6 0 0 -12 -6 -12 -6 0 0 -13 -6 0 0 -12 -6 0 0 -13 -6 -13 -5 0 0 -12 -6 -13 -5 0 0c-13,-5 -26,-10 -39,-15l-13 -5 0 0 -13 -5 0 0c-8,-3 -17,-6 -26,-9l0 0 -13 -4 0 0c-9,-3 -17,-6 -26,-9l0 0 -13 -4 0 0c-14,-4 -27,-7 -41,-11l0 0c-13,-4 -26,-7 -40,-10l0 0 -14 -4 0 0 -13 -3 0 0c-9,-2 -19,-4 -28,-6l0 0 -13 -2 0 0 -14 -3 0 0 -14 -2 0 0 -14 -3 0 0 -14 -2 0 0 -14 -2 0 0 -14 -2 0 0 -14 -2 0 0 -14 -2 0 0 -14 -2 0 0 -14 -1 -14 -2 -15 -1 -14 -1 -14 -1 -14 -1 0 0 -15 -1 0 0 -14 -1 0 0 -15 -1 15 1 0 0 14 1 0 0 15 1 0 0 14 1 14 1 14 1 15 1 14 2 14 1 0 0 14 2 0 0 14 2 0 0 14 2 0 0 14 2 0 0 14 2 0 0 14 2 0 0 14 3 0 0 14 2 0 0 14 3 0 0 13 2 0 0c10,2 19,4 28,6l0 0 13 3 0 0 14 4 0 0c14,3 27,6 40,10l0 0c14,4 27,7 41,11l0 0 13 4 0 0c9,3 17,6 26,9l0 0 13 4 0 0c9,3 18,6 26,9l0 0 13 5 0 0 13 5c13,5 26,10 39,15l0 0 13 5 12 6 0 0 13 5 13 6 0 0 12 6 0 0 13 6 0 0 12 6 12 6 0 0 13 6 12 7 12 6 0 0 12 7 12 7 0 0 12 7 12 7 12 7 0 0 12 7 12 7 12 8 11 8 0 0 12 7 0 0 11 8 12 8 11 8zm-837 -278l0 0 0 0zm-2268 107c230,-154 487,-258 765,-302 -278,44 -535,148 -765,302l0 0zm765 -302c1,0 1,0 2,0 -1,0 -1,0 -2,0zm929 90l-10 -4 0 0 -10 -3 0 0 -10 -3 0 0 -9 -3 0 0 -10 -4 0 0 -10 -3 0 0 -10 -3 0 0 -10 -3 0 0 -9 -3 -1 0 -9 -3 0 0 -10 -3 0 0 -10 -3 0 0 -10 -2 0 0 -10 -3 -10 -3 0 0 -10 -2 0 0 -10 -3 -9 -2 0 0 -10 -3 0 0 -10 -2 0 0 -10 -3 0 0 -10 -2 -10 -2 -10 -2 0 0 -10 -3 -10 -2 0 0 -10 -2 -10 -2 -10 -2 0 0 -10 -2 0 0 -10 -1 -10 -2 0 0 -10 -2 -11 -2 0 0 -10 -1 0 0 -10 -2 0 0 -10 -1 0 0 -10 -2 0 0 -10 -1 -10 -2 0 0 -10 -1 -10 -1 -10 -1 -11 -1 -10 -2 0 0 -10 -1 -10 -1 0 0 -10 -1 -10 0 -11 -1 -10 -1 -10 -1 -10 0 0 0 -10 -1 -11 0 -10 -1 -10 0 -10 -1 -11 0 -10 0 -10 0 -10 -1 -11 0 -10 0 10 0 11 0 10 1 10 0 10 0 11 0 10 1 10 0 10 1 11 0 10 1 0 0 10 0 10 1 10 1 11 1 10 0 10 1 0 0 10 1 10 1 0 0 10 2 11 1 10 1 10 1 10 1 0 0 10 2 10 1 0 0 10 2 0 0 10 1 0 0 10 2 0 0 10 1 0 0 11 2 10 2 0 0 10 2 10 1 0 0 10 2 0 0 10 2 10 2 10 2 0 0 10 2 10 3 0 0 10 2 10 2 10 2 0 0 10 3 0 0 10 2 0 0 10 3 0 0 9 2 10 3 0 0 10 2 0 0 10 3 10 3 0 0 10 2 0 0 10 3 0 0 10 3 0 0 9 3 1 0 9 3 0 0 10 3 0 0 10 3 0 0 10 3 0 0 10 4 0 0 9 3 0 0 10 3 0 0 10 3 0 0 10 4 0 0 0 0z"/>
    <g>
     <ellipse class="fil1 str0" transform="matrix(1.39143 -0.696157 0.333874 0.667323 8984.64 6157.99)" rx="5803" ry="4445"/>
     <ellipse class="fil1 str0" transform="matrix(1.39143 -0.696157 0.333874 0.667323 3437.71 2886.94)" rx="5803" ry="4445"/>
     <ellipse class="fil1 str0" transform="matrix(-2.09083 -1.04608 -0.501698 1.00276 8596.59 5643.56)" rx="5803" ry="4445"/>
     <ellipse class="fil1 str0" transform="matrix(1.39143 -0.696157 0.333874 0.667323 14331.9 5612.56)" rx="5803" ry="4445"/>
     <ellipse class="fil1 str0" transform="matrix(1.39143 -0.696157 0.333874 0.667323 1937.35 1007.43)" rx="5803" ry="4445"/>
     <ellipse class="fil1 str0" transform="matrix(-1.39143 -0.696157 -0.333874 0.667323 3021.29 8098.63)" rx="5803" ry="4445"/>
     <ellipse class="fil1 str0" transform="matrix(-1.39143 -0.696157 -0.333874 0.667323 10466.3 3265.21)" rx="5803" ry="4445"/>
    </g>
   </g>
  </g>
  <polygon class="fil1" points="0,0 16256,0 16256,9144 0,9144 "/>
 </g>
</svg>
