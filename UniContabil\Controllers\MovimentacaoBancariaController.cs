﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models; 


namespace UniContabil.Controllers
{
    public class MovimentacaoBancariaController : LibController
    {
        public MovimentacaoBancariaController()
        { }

        [HttpGet]
        [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
        public ActionResult Index(int page = 1)
        {
            try 
            {
                IPagedList<MovimentacaoBancariaModel> List = new MovimentacaoBancariaServices().GetPagedList(page);
                ViewBag.PageItems = List;
                return View();
            }
            catch(Exception Ex) 
            {
                MessageAdd(new Message(MessageType.Error, Ex.Message));
                return View();
            }
        }

        [HttpGet]
        [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
        public ActionResult Create()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
        public ActionResult Create(MovimentacaoBancariaModel movimentacaoBancariaModel)
        {
            try 
            {
                if (ModelState.IsValid)
                {
                  var model = movimentacaoBancariaModel.ToDatabase();
                  new MovimentacaoBancariaServices().Create(model);

                  return RedirectToAction(nameof(Index));
                }
                else {
                    return View(movimentacaoBancariaModel);
                }
            }
            catch(Exception Ex) 
            {
                MessageAdd(new Message(MessageType.Error, Ex.Message));
                return View(movimentacaoBancariaModel);
            }
        }
  
        [HttpGet]
        [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
        public ActionResult Edit(int id)
        {
            try 
            {
                MovimentacaoBancariaModel movimentacaoBancariaModel = new MovimentacaoBancariaModel().FromDatabase(new MovimentacaoBancariaServices().GetById(id));

                if (movimentacaoBancariaModel == null)
                {
                    return NotFound();
                }                

                return View(movimentacaoBancariaModel);
            }
            catch(Exception Ex) 
            {
                MessageAdd(new Message(MessageType.Error, Ex.Message));
                return View();
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
        public ActionResult Edit(MovimentacaoBancariaModel movimentacaoBancariaModel)
        {
            try 
            {
                if (ModelState.IsValid)
                {
                    var model = movimentacaoBancariaModel.ToDatabase();
                    new MovimentacaoBancariaServices().Edit(model);
                    return RedirectToAction(nameof(Index));
                }
                return View(movimentacaoBancariaModel);
            }
            catch(Exception Ex) 
            {
                MessageAdd(new Message(MessageType.Error, Ex.Message));
                return View(movimentacaoBancariaModel);
            }
        }

        [HttpPost]
        [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
        public string Delete(int id)
        {
            try
            {
                MovimentacaoBancariaServices Service = new MovimentacaoBancariaServices();
                var model = Service.GetById(id);
                Service.Delete(model);
                return JsonConvert.SerializeObject(new {Sucesso = true});
            }
            catch(Exception Ex) 
            {
                return JsonConvert.SerializeObject(new {Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult)});
            }
        }
  
        [HttpGet]
        public ActionResult GetOrganizacaoSelect(string term)
        {
          OrganizacaoServices Service = new OrganizacaoServices();
          List<Select2Model> List = Service.GetByTerm(term);
          return Json(new { items = List });
        }
        [HttpGet]
        public ActionResult GetFilialSelect(string term)
        {
          FilialServices Service = new FilialServices();
          List<Select2Model> List = Service.GetByTerm(term);
          return Json(new { items = List });
        }
        [HttpGet]
        public ActionResult GetCentroCustoSelect(string term)
        {
          CentroCustoServices Service = new CentroCustoServices();
          List<Select2Model> List = Service.GetByTerm(term);
          return Json(new { items = List });
        }
        [HttpGet]
        public ActionResult GetContaContabelSelect(string term)
        {
          ContaContabelServices Service = new ContaContabelServices();
          List<Select2Model> List = Service.GetByTerm(term);
          return Json(new { items = List });
        }
        [HttpGet]
        public ActionResult GetHistoricoPadraoSelect(string term)
        {
          HistoricoPadraoServices Service = new HistoricoPadraoServices();
          List<Select2Model> List = Service.GetByTerm(term);
          return Json(new { items = List });
        }
        [HttpGet]
        public ActionResult GetBancoSelect(string term)
        {
          BancoServices Service = new BancoServices();
          List<Select2Model> List = Service.GetByTerm(term);
          return Json(new { items = List });
        }
        [HttpGet]
        public ActionResult GetTipoLancamentoSelect(string term)
        {
          TipoLancamentoServices Service = new TipoLancamentoServices();
          List<Select2Model> List = Service.GetByTerm(term);
          return Json(new { items = List });
        }
    }
}
