﻿@model UniContabilEntidades.Models.ModuloModel
@using UniContabil.Infrastructure.Controls

    @{
    ViewData["Title"] = "Novo " + @Html.DisplayNameFor(model => model);
    }
  <div class="card">
    <form asp-action="Create">
      <div class="card-header">
        <div class="card-title">
        </div>
        <div class="card-options">
        </div>
      </div>
      <div class="card-body">
        @Html.AntiForgeryToken()
        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
              <div class="form-group">
                <label asp-for="Nome" class="control-label"></label>
                <input asp-for="Nome" class="form-control" />
                <span asp-validation-for="Nome" class="text-danger"></span>
              </div>
      </div>
      <div class="card-footer">
        <a asp-action="Index" class="link link-info">Voltar</a>
        <input type="submit" value="Adicionar" class="btn btn-primary" />
      </div>
    </form>
  </div>

