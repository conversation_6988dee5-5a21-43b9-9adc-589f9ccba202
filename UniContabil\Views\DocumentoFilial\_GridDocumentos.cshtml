﻿@model List<UniContabilEntidades.Models.DocumentoFilialListModel>
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;


@{
  ViewData["Title"] = @Html.DisplayNameFor(model => model);
  Layout = "";
}

<div style="width: 100%; max-height: calc(100vh - 80px); overflow: auto">
  <table class="table table-bordered table-striped table-sm">
    <thead>
      <tr>
        <th>Data Documento</th>
        <th>Descrição</th>
        <th>Usuário Criação</th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      @foreach (UniContabilEntidades.Models.DocumentoFilialListModel document in Model)
    {
      <tr>
        <td>@document.DataDocumento</td>
        <td style=" overflow: auto; flex-wrap: nowrap; max-width: 220px; ">@document.Nome</td>
        <td>@document.Usuario</td>
        <td>
          <a class="btn btn-warning btn-sm showfile" title="Visualizar" style="padding: 4px !important;" data-cod="@document.IdDocumentoFilial"><i class="mw-drip-expand" style="font-size: 8pt !important;"></i></a>

          <a class="btn btn-warning btn-sm" style="padding: 4px !important;" href="@Url.Action("Download","DocumentoFilial", new { id = document.IdDocumentoFilial })" target="_blank" title="dowload">
            <i class="mw-drip-download" style="font-size: 8pt !important;"></i>
          </a>
          <a class="btn btn-danger btn-sm delete" title="Deletar" data-cod="@document.IdDocumentoFilial" style="padding: 4px !important;"><i class="mw-drip-trash" style="font-size: 8pt !important; "></i></a>
        </td>
      </tr>
      @*<div class="card" data-cod="@document.IdDocumentoFilial" style=" width: 99%; margin: 0 !important; padding: 0 5px 0px 0px; margin-bottom: 5px !important; max-height: 10vh;">
          <div class="card-body" style=" margin-top: 10px; padding: 0 !important;">
            <div class="col-md-12">
              <div class="row" style="align-items:center">
                <div class="col-md-10">
                  <p style="margin: 0 !important;">
                    @document.DataDocumento - @document.Nome - @document.Usuario
                  </p>
                </div>
                <div class="col-md-2" style="display:flex;justify-content:space-between">
                  <a class="btn btn-warning btn-sm showfile" title="Visualizar" data-cod="@document.IdDocumentoFilial"><i class="mw-drip-expand" style="font-size: 8pt !important;"></i></a>

                  <a class="btn btn-warning btn-sm" href="@Url.Action("Download","DocumentoFilial", new { id = document.IdDocumentoFilial })" target="_blank" title="dowload">
                    <i class="mw-drip-download" style="font-size: 8pt !important;"></i>
                  </a>
                  <a class="btn btn-danger btn-sm delete" title="Deletar" data-cod="@document.IdDocumentoFilial"><i class="mw-drip-trash" style="font-size: 8pt !important;"></i></a>
                </div>
              </div>
            </div>
          </div>
        </div>*@
  }
    </tbody>
  </table>
</div>
