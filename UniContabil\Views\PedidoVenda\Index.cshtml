﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model List<PedidoVendaIndex>
@{
  ViewBag.Title = "Pedido Venda";
}

<script src="~/Views/PedidoVenda/PedidoVenda.js"></script>

<div class=" table-group card">
  <div class="card-header">
    <div class="card-title">@Html.DisplayNameFor(model => model)</div>
    <div class="card-options">
      @if (ContextoUsuario.HasPermission("PedidoVenda", TipoFuncao.Create))
      {
        <button type="button" class="btn btn-success" onclick="location.href='@Url.Action("CreateInicial", "PedidoVenda")'">Adicionar Pedido Venda</button>
      }

    </div>
  </div>
  <div class="table-group-content">
    <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
      <thead>
        <tr>
          <th scope="col">
            Código
          </th>
          <th scope="col">
            Filial
          </th>
          <th scope="col">
            Data Emissão
          </th>
          <th scope="col">
            Cliente
          </th>
          @if (ContextoUsuario.HasPermission("PedidoVenda", TipoFuncao.Visualizar)
|| ContextoUsuario.HasPermission("PedidoVenda", TipoFuncao.Edit))
          {
            <th scope="col">
            </th>
          }

        </tr>
      </thead>
      <tbody>
        @foreach (PedidoVendaIndex PedidoVendaIndex in Model)
        {
          <tr>
            <td>
              @PedidoVendaIndex.CodigoIdent
            </td>
            <td>
              @if (string.IsNullOrEmpty(PedidoVendaIndex.NomeFilial))
              {
                <text> - </text>
              }
              else
              {
                @PedidoVendaIndex.NomeFilial
              }
            </td>
            <td>
              @PedidoVendaIndex.DataEmissao
            </td>
            <td>
              @if (string.IsNullOrEmpty(PedidoVendaIndex.NomeCliente))
              {
                <text> - </text>
              }
              else
              {
                @PedidoVendaIndex.NomeCliente
              }
            </td>
            @if (ContextoUsuario.HasPermission("PedidoVenda", TipoFuncao.Visualizar)
            || ContextoUsuario.HasPermission("PedidoVenda", TipoFuncao.Edit))
            {
              <td>
                <button  class="link link-primary" onclick="location.href='@Url.Action("Edit", "PedidoVenda", new { CodPedidoVenda = PedidoVendaIndex.Codigo })'">Editar</button>
              </td>
            }
          </tr>
        }

      </tbody>


    </table>
  </div>
  <div class="card-footer">

  </div>

  </div>





  @*<div class="container">
    @if (ContextoUsuario.HasPermission("PedidoVenda", TipoFuncao.Create))
    {
      <button type="button" class="btn btn-success" onclick="location.href='@Url.Action("CreateInicial", "PedidoVenda")'">Adicionar Pedido Venda</button>
    }
    <table class="table table-sm table-striped table-hover">
      <thead>
        <tr>
          <th scope="col">
            Código
          </th>
          <th scope="col">
            Filial
          </th>
          <th scope="col">
            Data Emissão
          </th>
          <th scope="col">
            Cliente
          </th>
          @if (ContextoUsuario.HasPermission("PedidoVenda", TipoFuncao.Visualizar)
      || ContextoUsuario.HasPermission("PedidoVenda", TipoFuncao.Edit))
          {
            <th scope="col">
            </th>
          }
        </tr>
      </thead>









      <tbody>
        @foreach (PedidoVendaIndex PedidoVendaIndex in Model)
        {
          <tr>
            <td>
              @PedidoVendaIndex.CodigoIdent
            </td>
            <td>
              @if (string.IsNullOrEmpty(PedidoVendaIndex.NomeFilial))
              {
                <text> - </text>
              }
              else
              {
                @PedidoVendaIndex.NomeFilial
              }
            </td>
            <td>
              @PedidoVendaIndex.DataEmissao
            </td>
            <td>
              @if (string.IsNullOrEmpty(PedidoVendaIndex.NomeCliente))
              {
                <text> - </text>
              }
              else
              {
                @PedidoVendaIndex.NomeCliente
              }
            </td>
            @if (ContextoUsuario.HasPermission("PedidoVenda", TipoFuncao.Visualizar)
             || ContextoUsuario.HasPermission("PedidoVenda", TipoFuncao.Edit))
            {
              <td>
                <button type="button" class="btn btn-success" onclick="location.href='@Url.Action("Edit", "PedidoVenda", new { CodPedidoVenda = PedidoVendaIndex.Codigo })'">Editar</button>
              </td>
            }
          </tr>
        }
      </tbody>
    </table>


  </div>*@

