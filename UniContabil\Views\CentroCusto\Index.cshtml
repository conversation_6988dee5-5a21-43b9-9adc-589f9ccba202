﻿@model IPagedList<UniContabilEntidades.Models.CentroCustoModel>
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;


@{

  ViewData["Title"] = @Html.DisplayNameFor(model => model);
  Layout = "~/Views/Shared/_Layout.cshtml";
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();
}
<div class="table-group card">
  <div class="card-header">
    <div class="row">
      @if (ContextoUsuario.HasPermission(controllerName, TipoFuncao.Create))
      {
        <a class="btn btn-success" asp-action="Create"><i class="mw-drip-plus"></i></a>
      }
    </div>
  </div>
  <div class="table-group-content">

    <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
      <thead>
        <tr>
          <th>
            Organização
          </th>
          <th>
            Filial
          </th>
          <th>
            Descrição
          </th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        @foreach (UniContabilEntidades.Models.CentroCustoModel item in Model)
        {
          <tr>
            <td>
              @Html.DisplayFor(modelItem => item.NomeOrganizacao)
            </td>
            <td>
              @Html.DisplayFor(modelItem => item.NomeFilial)
            </td>
            <td>
              @Html.DisplayFor(modelItem => item.Descricao)
            </td>
            <td>
              @if (ContextoUsuario.HasPermission(controllerName, TipoFuncao.Edit))
              {
                <a class="btn btn-secondary btn-sm" asp-action="Edit" asp-route-id="@item.Id"><i class="mw-drip-document-edit"></i></a>
              }
              @if (ContextoUsuario.HasPermission(controllerName, TipoFuncao.Delete))
              {
                <a class="btn btn-danger btn-sm" onclick="DeleteItem('@this.ViewContext.RouteData.Values["controller"].ToString()', @item.Id)"><i class="mw-drip-trash"></i></a>
              }
            </td>
          </tr>
        }
      </tbody>
    </table>
    @Html.PagedListPager(Model, page => Url.Action("Index", new { page }),
      new PagedListRenderOptions
      {
       LiElementClasses = new string[] { "page-item" },
        PageClasses = new string[] { "page-link" },
        Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
        MaximumPageNumbersToDisplay = 5
      })
  </div>
</div>

