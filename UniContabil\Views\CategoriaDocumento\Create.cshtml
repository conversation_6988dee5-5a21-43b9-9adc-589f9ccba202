﻿@model UniContabilEntidades.Models.CategoriaDocumentoModel
@using UniContabil.Infrastructure.Controls

<div class="card">
  <form asp-action="Create" id="CreateCategoria">
    <div class="card-header">
      <h5>Nova Categoria</h5>
    </div>
    <div class="card-body">
      @Html.AntiForgeryToken()
      <input type="hidden" name="TipoEntrada" id="TipoEntrada" />
      <div asp-validation-summary="ModelOnly" class="text-danger"></div>
      <div class="form-group">
        <label asp-for="Codigo" class="control-label"></label>
        <input asp-for="Codigo" class="form-control" />
        <span asp-validation-for="Codigo" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="Descricao" class="control-label"></label>
        <input asp-for="Descricao" class="form-control" />
        <span asp-validation-for="Descricao" class="text-danger"></span>
      </div>
    </div>
    <div class="card-footer">
      <a asp-action="Index" class="link link-info">Voltar</a>
      <a class="btn btn-primary" id="saveCatCreate">Salvar</a>
    </div>
  </form>
</div>

