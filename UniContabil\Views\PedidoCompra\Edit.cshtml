﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@model UniContabilEntidades.Models.PedidoCompraModel
@{
  ViewBag.Title = "Pedido Compra";
  //Layout = "~/Views/Shared/_LayoutAccount.cshtml";
}

<script src="~/Views/PedidoCompra/PedidoCompra.js"></script>
<link rel="stylesheet" href="~/Views/PedidoCompra/PedidoCompra.css?b=b" />

<div class="card">
  @using (Html.BeginForm("Edit", "PedidoCompra", FormMethod.Post@*, new { @style = "text-align: end;" }*@))
{
<div class="card-header">
  <div class="card-title">
    Editar Pedido Compra
  </div>
  <div class="card-options">
  </div>
</div>
<div class="card-body" id="CabecalhoPedidoCompra">
  @Html.HiddenFor(a => a.Codigo)
  @Html.HiddenFor(a=> a.CodigoIdent)
  <div class="form-group">
    @Html.LabelFor(a => a.CodigoIdent)
    @Html.TextBoxFor(a => a.CodigoIdent, new { @class = "form-control", disabled = "true" })
    @Html.ValidationMessageFor(a => a.CodigoIdent)
  </div>

  <div class="form-group">
    @Html.LabelFor(a => a.DataEmissao)
    @Html.TextBoxFor(a => a.DataEmissao, new { @class = "form-control Data" })
    @Html.ValidationMessageFor(a => a.DataEmissao)
  </div>

  <div class="form-group">
    @Html.Label("Cliente")
    <div style="display: flex; justify-content: space-between; align-items: flex-end;">
      <div style="width: 85%">
        @Html.Select2("ClienteSelect", "GetClienteSelect", "Selecione o Cliente", "", "", Model.ClienteSelect == null ? "" : Model.ClienteSelect.id, Model.ClienteSelect == null ? "" : Model.ClienteSelect.text)
      </div>
      <div style="width: 10%; display: flex;">
        <i class="mw-drip-search" id="DetalhesCliente"></i>
      </div>
    </div>
  </div>


  <div class="form-group">
    @Html.Label("Forma Pagamento")
    @Html.Select2("FormaPagamentolSelect", "GetFormaPagamentoSelect", "Selecione a Forma de Pagamento", "", "", Model.FormaPagamentolSelect == null ? "" : Model.FormaPagamentolSelect.id, Model.FormaPagamentolSelect == null ? "" : Model.FormaPagamentolSelect.text)
  </div>

  <div class="form-group">
    @Html.Label("Condicao Pagamento")
    @Html.Select2("CondicaoPagamentoSelect", "GetCondicaoPagamentoSelect", "Selecione a Condição de Pagamento", "", "", Model.CondicaoPagamentoSelect == null ? "" : Model.CondicaoPagamentoSelect.id, Model.CondicaoPagamentoSelect == null ? "" : Model.CondicaoPagamentoSelect.text)
  </div>

  <div class="form-group">
    @Html.LabelFor(a => a.ValorTotal)
    @Html.TextBoxFor(a => a.ValorTotal, new { @class = "form-control money" })
    @Html.ValidationMessageFor(a => a.ValorTotal)
  </div>

  <div class="form-group">
    @Html.LabelFor(a => a.TipoFrete)
    <select class="form-control" asp-for="TipoFrete" asp-items="Html.GetEnumSelectList<TipoFreteEnum>()">
      <option selected="selected" value="">Selecione</option>
    </select>
    @Html.ValidationMessageFor(a => a.ValorFrete)
  </div>

  <div class="form-group">
    @Html.LabelFor(a => a.ValorFrete)
    @Html.TextBoxFor(a => a.ValorFrete, new { @class = "form-control money" })
    @Html.ValidationMessageFor(a => a.ValorFrete)
  </div>

  <div class="form-group">
    @Html.LabelFor(a => a.CEPEntrega)
    @Html.TextBoxFor(a => a.CEPEntrega, new { @class = "form-control CEP" })
    @Html.ValidationMessageFor(a => a.CEPEntrega)
  </div>

  <div class="form-group">
    @Html.LabelFor(a => a.EnderecoEntrega)
    @Html.TextBoxFor(a => a.EnderecoEntrega, new { @class = "form-control" })
    @Html.ValidationMessageFor(a => a.EnderecoEntrega)
  </div>

  <div class="form-group">
    @Html.LabelFor(a => a.CidadeEntrega)
    @Html.TextBoxFor(a => a.CidadeEntrega, new { @class = "form-control" })
    @Html.ValidationMessageFor(a => a.CidadeEntrega)
  </div>

  <div class="form-group">
    @Html.LabelFor(a => a.UFEntrega)
    @Html.TextBoxFor(a => a.UFEntrega, new { @class = "form-control" })
    @Html.ValidationMessageFor(a => a.UFEntrega)
  </div>

</div>
<div class="card-footer">
  @if (ContextoUsuario.HasPermission("PedidoCompra", TipoFuncao.Edit))
  {
    <button type="submit" class="btn btn-success">Salvar Pedido</button>
  }

</div>

}
</div>





@if (ContextoUsuario.HasPermission("PedidoCompra", TipoFuncao.GridItensPedidoCompra)
|| ContextoUsuario.HasPermission("ItensPedidoCompra", TipoFuncao.Create)
|| ContextoUsuario.HasPermission("ItensPedidoCompra", TipoFuncao.Edit)
|| ContextoUsuario.HasPermission("ItensPedidoCompra", TipoFuncao.Delete))
{

  <div class="table-group card">
    @using (Html.BeginForm("Edit", "PedidoCompra", FormMethod.Post@*, new { @style = "text-align: end;" }*@))
{
<div class="card-header">
  <div class="card-title">
    Itens de Compra
  </div>
  <div class="card-options">

  </div>
</div>

<div  id="DivPedidosCompra">
  <div class="form-group">
    @await Html.PartialAsync("_GridItensCompra", Model.ListaItensPedidoCompra)
  </div>

</div>

<div class="card-footer">

</div>
}
  </div>
}


@if (ContextoUsuario.HasPermission("ItensPedidoCompra", TipoFuncao.Create)
|| ContextoUsuario.HasPermission("ItensPedidoCompra", TipoFuncao.Edit))
{
<div class="table-group card">
  @using (Html.BeginForm("Edit", "PedidoCompra", FormMethod.Post))
  {
    <div class="card-header">
      <div class="card-title">
        Adicionar itens de compra
      </div>
      <div class="card-options">
        <div class="row" style="justify-content: flex-end;">
          @if (ContextoUsuario.HasPermission("ItensPedidoCompra", TipoFuncao.Create))
          {
            <button type="button" class="btn btn-primary" id="AdicionarItemCompra" value="0">Adicionar Item Compra</button>
          }
          @if (ContextoUsuario.HasPermission("ItensPedidoCompra", TipoFuncao.Edit))
          {
            <button type="button" style="display:none;" class="btn btn-success" id="EditarItemCompra" value="1">Editar Item Compra</button>
          }
        </div>
      </div>
    </div>
    <div class="card-body" id="DivAddItensPedidoCompra">
      <input id="CodigoModal" name="CodigoModal" type="hidden" value="">
      <input id="CodigoOrganizacaoModal" name="CodigoOrganizacaoModal" type="hidden" value="">
      <input id="CodigoFilialModal" name="CodigoFilialModal" type="hidden" value="">
      <input id="CodPedidoCompraModal" name="CodPedidoCompraModal" type="hidden" value="">
      <input id="Descricao" name="Descricao" type="hidden" value="">

      <div class="form-group  col-md-1">
        <label for="Unidade">Sequencia</label>
        <input class="form-control" data-val="true" id="SequenciaItem" name="SequenciaItem" type="text" value="" disabled="disabled">
      </div>

      <div class="form-group  col-md-5">
        @Html.Label("Produto")
        <div style="display: flex; justify-content: space-between; align-items: flex-end;">
          <div style="width: 85%">
            @Html.Select2("ProdutoSelect", "GetProdutoSelect", "Selecione o Produto")
          </div>
          <div style="width: 10%; display: flex;">
            <i class="mw-drip-search" id="DetalhesProduto"></i>
          </div>
        </div>
      </div>

      <div class="form-group col-md-1">
        <label for="Unidade">Unidade</label>
        <input class="form-control number" data-val="true" id="Unidade" name="Unidade" type="text" value="0">
      </div>



      <div class="form-group col-md-1">
        <label for="Quantidade">Quantidade</label>
        <input class="form-control number" data-val="true" id="Quantidade" name="Quantidade" type="text" value="0">
      </div>

      <div class="form-group col-md-2">
        <label for="Quantidade">Valor Unitário</label>
        <input class="form-control money" data-val="true" id="ValorUnitario" name="ValorUnitario" type="text" value="0">
      </div>

      <div class="form-group col-md-2">
        <label for="ValorTotalItem money">Valor Total Item</label>
        <input class="form-control money" data-val="true" id="ValorTotalItem" name="ValorTotalItem" type="text" value="0">
      </div>

      <div class="form-group col-md-2">
        <label for="DescontoPercentual">Desconto %</label>
        <input class="form-control porcentagem" data-val="true" id="DescontoPercentual" name="DescontoPercentual" type="text" value="0">
      </div>
      <div class="form-group col-md-2">
        <label for="Desconto">Desconto R$</label>
        <input class="form-control money" data-val="true" id="Desconto" name="Desconto" type="text" value="0">
      </div>
      <div class="form-group col-md-12">
        <label for="Observacoes">Observações</label>
        <textarea class="form-control" data-val="true" id="Observacoes" name="Observacoes" type="text" value=""> </textarea>
      </div>








    </div>

    <div class="card-footer">
      <button type="button" class="btn btn-secondary" onclick="location.href='@Url.Action("Index", "PedidoCompra")'">Voltar</button>

      
      @if (ContextoUsuario.HasPermission("NotaFiscalCompras", TipoFuncao.GerarNotaFiscalCompra))
      {
        <button type="button" class="btn btn-primary" id="OpenModalSeries" value="">Gerar Nota Fiscal</button>
      }
    </div>
  }
</div>
 }

    @await Html.PartialAsync("_ModalSerie", new SeriesNotasModel())
    @await Html.PartialAsync("InfoAddProduto/_ModalPesquisaProduto", new ModalPesquisaProduto())
    @await Html.PartialAsync("InfoAddProduto/_ModalDetalhesProduto")
    @await Html.PartialAsync("InfoAddCliente/_ModalPesquisaCliente", new ModalPesquisaCliente())
    @await Html.PartialAsync("InfoAddCliente/_ModalDetalhesCliente")








 





    
