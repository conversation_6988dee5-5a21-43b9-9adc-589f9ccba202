﻿@model UniContabilEntidades.Models.BancoEditModel
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Editar " + @Html.DisplayNameFor(model => model);
  Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="card">
  <form asp-action="Edit">
    <div class="card-header">
      <div class="card-title">
      </div>
      <div class="card-options">
      </div>
    </div>
    <div class="card-body">
      @Html.AntiForgeryToken()
      <div asp-validation-summary="ModelOnly" class="text-danger"></div>
      <input type="hidden" asp-for="Id" />
      <div class="form-group">
        <label asp-for="NomeConta" class="control-label"></label>
        <input asp-for="NomeConta" class="form-control" />
        <span asp-validation-for="NomeConta" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="CodBanco" class="control-label"></label>
        <input asp-for="CodBanco" class="form-control CodBancoAgencia" />
        <span asp-validation-for="CodBanco" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="CodAgencia" class="control-label"></label>
        <input asp-for="CodAgencia" class="form-control CodBancoAgencia" />
        <span asp-validation-for="CodAgencia" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="CodConta" class="control-label"></label>
        <input asp-for="CodConta" class="form-control CodConta" />
        <span asp-validation-for="CodConta" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="DVConta" class="control-label"></label>
        <input asp-for="DVConta" class="form-control CodDV" />
        <span asp-validation-for="DVConta" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label class="control-label"> Conta Contábil</label>
        @Html.Select2("PlanoSelect", "GetTipoPlanoContasCreditoSelectTeste", "Selecione o plano de conta", "", "", Model.PlanoSelect.id, Model.PlanoSelect.text)
      </div>
    </div>
    <div class="card-footer">
      <a asp-action="Index" class="link link-info">Voltar</a>
      <input type="submit" value="Salvar" class="btn btn-primary" />
    </div>
  </form>
</div>
