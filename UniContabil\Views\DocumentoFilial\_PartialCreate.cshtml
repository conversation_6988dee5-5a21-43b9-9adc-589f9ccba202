﻿@model UniContabilEntidades.Models.TipoDocumentoFilialCreateModel
@using UniContabil.Infrastructure.Controls

<form asp-action="Create" asp-controller="TipoDocumentoFilial">
  <div class="row">
    <div class="col-md-12">
      <div class="form-group">
        <label>Nome</label>
        @Html.TextBoxFor(m => m.<PERSON>, new { type = "text", @class = "form-control" })
        @Html.ValidationMessageFor(m => m.Descricao)
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-primary col-5" type="submit">Salvar</button>
  </div>
</form>
