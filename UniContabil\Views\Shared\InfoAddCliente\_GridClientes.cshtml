﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure
@model List<GridCliente>

<table class="table table-sm table-striped table-hover">
  <thead>
    <tr>
      <th scope="col">
        Nome
      </th>
      <th scope="col">
        CPF/CNPJ
      </th>
      <th scope="col">
        Email
      </th>
      <th scope="col">
        TelefoneFixo
      </th>
      <th scope="col">
        TelefoneCelular
      </th>
      <th>
      </th>
    </tr>
  </thead>
  <tbody>
    @foreach (GridCliente gridCliente in Model)
    {
      <tr>
        <td>
          @gridCliente.Nome
        </td>
        <td>
          @gridCliente.CPFCNPJ
        </td>
        <td>
          @gridCliente.Email
        </td>
        <td>
          @gridCliente.TelefoneFixo
        </td>
        <td>
          @gridCliente.TelefoneCelular
        </td>
        <td>
          <i class="mw-drip-information MaisInformacoesClientes" data-CodigoCliente="@gridCliente.CodigoCliente"></i>
        </td>
      </tr>
    }
  </tbody>
</table>

