﻿@model List<GrupoUsuarioModel>
@using UniContabil.Infrastructure.Controls

<div class="row form-group" style="margin: 20px 0 !important">
  <div class="col-8">
    @Html.Select2("UsuarioSelect", "GetUsuariosSelect", "Busque o usuário por Nome ou CPF")
  </div>
  <button class="btn btn-outline-primary btn-sm" id="novoGrupoUsuario" type="button">Adicionar</button>
</div>
<div class="table-group-content">
  <table id="users-table">
    <thead>
      <tr>
        <th>
          CPF
        </th>
        <th>
          Nome
        </th>
        <th>
        </th>
      </tr>
    </thead>
    <tbody>
      @foreach (GrupoUsuarioModel usuario in Model)
      {
        <tr data-grupousuario="@usuario.IdGrupoUsuario">
          <td>
            @usuario.CPF
          </td>
          <td>
            @usuario.Nome
          </td>
          <td>
            <button class="btn btn-outline-danger btn-sm deletarGrupoUsuario" type="button">Remover</button>
          </td>
        </tr>
      }
    </tbody>
  </table>
</div>

<script>
  $(document).ready(function () {
    $("#novoGrupoUsuario").click(function () {
      const idUsuario = $("#UsuarioSelect_id").val();

      if (!idUsuario) {
        Alerta("Aviso", "Selecione um usuário antes de tentar adicionar ao grupo.", "red");
        return;
      }

      let obj = {
        IdGrupo: $(".list-group li.active").data("grupo"),
        IdUsuario: idUsuario
      };

      $.post(GetURLBaseComplete() + "/GrupoUsuario/Vincular", obj, function (data) {
        data = JSON.parse(data);
        if (data.Sucesso) {
          $(".list-group li.active").click();
          Alerta("Sucesso", "Usuário vinculado com sucesso.", "green", 5000);
        }
        else {
          Alerta("Erro", data.Mensagem, "red", 5000);
        }
      });
    });

    $(".deletarGrupoUsuario").click(function () {
      let idGrupoUsuario = $(this).parent().parent().data("grupousuario");

      if (!idGrupoUsuario) {
        Alerta("Erro", "Houve um erro ao tentar desvincular o usuário do grupo.", "red");
        return;
      }

      $.post(GetURLBaseComplete() + "/GrupoUsuario/Desvincular/" + idGrupoUsuario, function (data) {
        data = JSON.parse(data);
        if (data.Sucesso) {
          $(".list-group li.active").click();
          Alerta("Sucesso", "Usuário desvinculado com sucesso.", "green", 5000);
        }
        else {
          Alerta("Erro", data.Mensagem, "red", 5000);
        }
      });
    });
  });
</script>