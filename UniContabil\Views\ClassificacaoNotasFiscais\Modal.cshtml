﻿@using UniContabil.Infrastructure

<div class="modal fade" id="modalConvite" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="modalConvite" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" style=" max-width: 40%; ">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Convite para Associação a contabilidade</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" style="max-height: 75vh; overflow: auto;">
        @Html.Partial("_PartialGridConvite", ContextoUsuario.UserLogged.ListConvite)
      </div>
    </div>
  </div>
</div>