﻿var Treeview = {};
var Tipo = 2;
var IdReloadGrid = null;

Treeview.makeTreeItem = function (el) {
  return $("<a>", {
    id: $(el).attr("id") + "_anchor",
    class: "jstree-anchor",
    href: "#"
  });
}

Treeview.init = function () {
  $(document).on("click", ".custom-radio", function () {
    Tipo = $(this).val();
    $("#tree").jstree(true).refresh()
  });
};

Treeview.GetTipo = function () {
  var listcheck = $(".custom-radio");

  for (var i = 0; i < listcheck.length; i++) {
    if ($(".custom-radio")[i].checked)
      Tipo = $(".custom-radio")[i].value;

    return Tipo;
  }
}
Treeview.LoadTree = function () {

  $("#tree").on('select_node.jstree', function (event, element) {
    if (element.event != undefined && element.event.originalEvent != undefined) {

      Alerta("Info", "Salvando alterações...", "blue", 2000);

      const codclass = element.node.a_attr.codClassificacao;

      var treev = {
        Id: null,
        IdUsuario: null,
        IdFilial: $("#Filial").data("filial"),
        IdOrganizacao: parseInt($("#OrganizacaoUserSelect_id").val()),
        IdClassificacao: codclass
      };

      $.ajax({
        url: GetURLBaseComplete() + "/Treeview/Desbloqtree",
        dataType: "json",
        type: "GET",
        data: treev,
        success: function (retorno) {
          if (!retorno.erro) {
           
          } else {
            swal({
              title: "Atenção",
              text: retorno.mensage,
              icon: "warning",
            });
          }
        }
      });

    }

  }).on('deselect_node.jstree', function (event, element) {
    if (element.event != undefined && element.event.originalEvent != undefined) {
      Alerta("Info", "Salvando alterações...", "blue", 2000);

      const codclass = element.node.a_attr.codClassificacao;

      var treev = {
        Id: null,
        IdUsuario: null,
        IdFilial: $("#Filial").data("filial"),
        IdOrganizacao: parseInt($("#OrganizacaoUserSelect_id").val()),
        IdClassificacao: codclass
      };

      $.ajax({
        url: GetURLBaseComplete() + "/Treeview/Bloqtree",
        dataType: "json",
        type: "GET",
        data: treev,
        success: function (retorno) {
          if (!retorno.erro) {
            swal({
              title: "Sucesso",
              text: retorno.mensage,
              icon: "success",
            });
          } else {
            swal({
              title: "Atenção",
              text: retorno.mensage,
              icon: "warning",
            });
          }
        }
      });
    }
  }).jstree({
    'core': {
      'check_callback': function (
        operation,
        node,
        node_parent,
        node_position,
        more
      ) {
        if (more.dnd != null && more.dnd == true) {
          if (operation === "move_node") {
            return false;
          } else if (operation === "delete_node") {
            return false;
          }
        }
      },
      'data': {
        'url': function() {
          return GetURLBaseComplete() + '/Treeview/_GetTreeview?Tipo=' + Treeview.GetTipo();
        },
        'dataType': 'json'
      },
      'themes': {
        'icons': false
      }
    }
    ,
    "checkbox": {
      'whole_node': false,
    },
    "plugins": ["checkbox"]
  });
  $(".spinner-menu").attr("style", "display: none !important");
}

$(document).ready(function () {
  Treeview.init();
  Treeview.LoadTree();
});
