﻿@using UniContabil.Infrastructure
@using UniContabil.Infrastructure.Controls

<div class="modal fade" id="modalContabilidade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="modalContabilidade" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" style="max-width:40%">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Associar Contabilidade</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" id="editContab" style="max-height: 75vh; overflow: auto;">
        @Html.Partial("_PartialAddContab", new UniContabilEntidades.Models.FilialModel())
      </div>
     
      <div class="modal-footer">
        @if (ContextoUsuario.UserLogged.Contabilidade == null)
        {
          <button type="button" class="btn btn-sm btn-warning" id="enviaEmailContab">indique seu contador</button>
        }
        <button class="btn btn-default" data-dismiss="modal" aria-label="Close">Cancelar</button>
        <button class="btn btn-success" type="button" id="save">Salvar</button>
      </div>
    </div>
  </div>
</div>