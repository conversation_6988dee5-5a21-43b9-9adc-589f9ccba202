﻿@model UniContabilEntidades.Models.GrupoProdutoModel
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Create";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Criar @Html.DisplayNameFor(model => model)</h1>

<div class="row">
  <div class="col-md-4">
    <form asp-action="Create">
      @Html.AntiForgeryToken()
      <div asp-validation-summary="ModelOnly" class="text-danger"></div>
      <div class="form-group">
        <label asp-for="IdOrganizacao" class="control-label"></label>
        @Html.Select2("OrganizacaoSelect", "GetOrganizacaoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdOrganizacao), this.ViewContext.RouteData.Values["controller"].ToString())
      </div>
      <div class="form-group">
        <label asp-for="IdFilial" class="control-label"></label>
        @Html.Select2("FilialSelect", "GetFilialSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdFilial), this.ViewContext.RouteData.Values["controller"].ToString())
      </div>
      <div class="form-group">
        <label asp-for="Descricao" class="control-label"></label>
        <input asp-for="Descricao" class="form-control" />
        <span asp-validation-for="Descricao" class="text-danger"></span>
      </div>
      <div class="form-group">
        <input type="submit" value="Adicionar" class="btn btn-primary" />
      </div>
    </form>
  </div>
</div>

<div>
  <a asp-action="Index">Voltar</a>
</div>

