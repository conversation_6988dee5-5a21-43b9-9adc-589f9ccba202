﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model UniContabilEntidades.Models.DemonstrativoResultadoExercicioModel

@{
  ViewData["Title"] = "Editar " + @Html.DisplayNameFor(model => model);
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();
  Layout = "~/Views/Shared/_Layout.cshtml";
}

<script src="~/Views/DemonstrativoResultadoExercicio/DRE.js?a=a"></script>

@using (Html.BeginForm("Edit", "DemonstrativoResultadoExercicio", FormMethod.Post))
{
  <div class="card">

    <div class="card-body">
      @Html.AntiForgeryToken()
      <div asp-validation-summary="ModelOnly" class="text-danger"></div>
      <input type="hidden" asp-for="Id" />
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label asp-for="Descricao" class="control-label"></label>
            <input asp-for="Descricao" class="form-control" />
            <span asp-validation-for="Descricao" class="text-danger"></span>
          </div>
        </div>
        <div class="col-md-4">
          <div class="form-group">
            <label asp-for="DRE" class="control-label"></label>
            @Html.Select2("TipoDRESelect", "GetTipoDRESelect", "Selecione", "", "")
          </div>
        </div>
      </div>
    </div>


    <div class="form-group">
    </div>

    <div class="card-footer">
      <button type="button" class="btn btn-secondary" onclick="location.href='@Url.Action("Index")'">Voltar</button>
      <button type="submit" class="btn btn-success">Salvar</button>
    </div>

  </div>
  <div class="table-group card">
    <table class=" table table-hover table-striped">
      <thead>
        <div class="row">
          <tr>
            <div class="col-md-3">
              <th>Código</th>
            </div>
            <div class="col-md-6">
              <th>Descrição</th>
            </div>
            <div class="col-md-3">
              <th>
                <button type="button" id="ModalGridClassificacao" class="btn btn-primary">
                  Incluir
                </button>
              </th>
            </div>

          </tr>
        </div>
      </thead>
      <tbody>
        @foreach (UniContabilEntidades.Models.ClassificacaoDRE item in Model.ListaClassificacaoDocumentos)
        {
          <tr>
            <td>
              @Html.DisplayFor(modelItem => item.Codigo)
            </td>
            <td>
              @Html.DisplayFor(modelItem => item.Descricao)
            </td>
            <td>
              <button class="deleteClassificacao btn btn-danger" data-id="@item.Id">Apagar</button>



            </td>
          </tr>
        }
      </tbody>
    </table>

  </div>

}
<div class="modal fade" id="GridModalClassDoc" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Incluir Classificação Documentos</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" id="gridClassificacao">
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary">Save changes</button>
      </div>
    </div>
  </div>
</div>
