﻿@model IPagedList<UniContabilEntidades.Models.ConfigureContextoOrganizacaoModel>
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;

@{
  ViewData["Title"] = @Html.DisplayNameFor(model => model);
  Layout = "~/Views/Shared/_Layout.cshtml";
}
<script src="~/Views/Configure/Configure.js?nh=qwe"></script>

<div class="table-group card">
  <div class="card-header">
    <div @*class="card-options"*@>

    </div>
  </div>
  <div class="table-group-content">
    <table>
      <thead>
        <tr>
          <th>
            Médico/Clinica
          </th>
          <th>
          </th>
        </tr>
      </thead>
      <tbody>
        @if (Model != null)
        {
          @foreach (ConfigureContextoOrganizacaoModel item in Model)
          {
            <tr>
              <td>
                @Html.DisplayFor(modelItem => item.NomeOrganizacao)
              </td>
              <td>
                @if (!item.Ativo)
                {
                  <a class="btn btn-outline-secondary" onclick="Configure.AlterarOrgAtiva('@item.IdOrganizacao', '@item.IdFilial')">Selecionar</a>
                }
                else
                {
                   <span>Em utilização!</span>
                }
              </td>
            </tr>
          }
        }
      </tbody>
    </table>
  </div>
  <div class="card-footer">
    @Html.PagedListPager(Model, page => Url.Action("Index", new { page }),
    new PagedListRenderOptions
         {
      LiElementClasses = new string[] { "page-item" },
      PageClasses = new string[] { "page-link" },
      Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
      MaximumPageNumbersToDisplay = 5
    })
  </div>
</div>

