﻿@model UniContabilEntidades.Models.CartaoDeCreditoCreateModel
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Novo " + @Html.DisplayNameFor(model => model);
  Layout = "~/Views/Shared/_Layout.cshtml";
}


<div class="card">
  <form asp-action="Create">
    <div class="card-header">
      <div class="card-title">
      </div>
      <div class="card-options">
      </div>
    </div>

    <div class="card-body">
      @Html.AntiForgeryToken()
      <div class="form-group">
        <label asp-for="FinalCartao" class="control-label">Cartão</label>
        <input asp-for="FinalCartao" class="form-control CartaoMask" />
        <span asp-validation-for="FinalCartao" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="Nome" class="control-label">Nome</label>
        <input asp-for="Nome" class="form-control" />
        <span asp-validation-for="Nome" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label class="control-label">Plano de Contas</label>
        @Html.Select2("PlanoSelect", "GetTipoPlanoContasCreditoSelectTeste", "Selecione plano de conta", "", "", Model.PlanoSelect.id, Model.PlanoSelect.text)
      </div>
    </div>
    <div class="card-footer">
      <a asp-action="Index" class="link link-info">Voltar</a>
      <input type="submit" value="Adicionar" class="btn btn-primary" />
    </div>
  </form>
</div>