﻿@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;
@model IPagedList<ContabilidadeIndexModel>

@{
  ViewData["Title"] = @Html.DisplayNameFor(model => model);
}

<div class="table-group card">
  @using (Html.BeginForm("IndexAprovacao", "Contabilidade", FormMethod.Get))
  {
    <div class="card-header col-md-12">

      <div class=" col-md-12">
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              <input type="text" value="@ViewBag.nome" name="nome" id="nome" class="form-control" placeholder="Nome" />
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <input type="text" value="@ViewBag.cpfcnpj" name="cpfcnpj" id="cpfcnpj" class="form-control" placeholder="CPF/CNPJ" />
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <select class="form-control" name="status" asp-items="Html.GetEnumSelectList<EnumStatusContabilidade>()">
                <option selected="selected" value="">Selecione</option>
              </select>
            </div>
          </div>
          <div class="col">
            <button class="btn btn-outline-info" type="submit">Filtrar</button>
          </div>
        </div>
      </div>
    </div>
  }

  @if (Model != null)
  {
    <div class="col-md-12" style="font-size:8pt">
      <div class="row">
        <div class="col-md-2">
          <i class="mw-circle-full" style="color: orange" title="Em Cadastro"></i>
          Em Cadastro
        </div>
        <div class="col-md-2">
          <i class="mw-circle-full" style="color: lightskyblue" title="Em Aprovação"></i>
          Em Aprovação
        </div>
        <div class="col-md-2">
          <i class="mw-circle-full" style="color: green" title="Aprovado"></i>
          Aprovado
        </div>
        <div class="col-md-2">
          <i class="mw-circle-full" style="color: red" title="Reprovado"></i>
          Reprovado
        </div>
      </div>
    </div>
    <div class="table-group-content ">
      <div class="table-responsive">
        <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
          <thead style="text-align:center">
            <tr>
              <th>
                Status
              </th>
              <th>
                CPF/CNPJ
              </th>
              <th>
                Razão Social
              </th>
              <th>
                E-mail
              </th>
              <th>
                Tel. Celular
              </th>
              <th>
                Ativo?
              </th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            @foreach (var item in Model)
            {
              <tr>
                <td>
                  @if (item.EnumStatus == (int)EnumStatusContabilidade.EmCadastro)
                  {
                    <i class="mw-circle-full" style="color: orange" title="Em Cadastro"></i>
                  }
                  @if (item.EnumStatus == (int)EnumStatusContabilidade.EmAprovacao)
                  {
                    <i class="mw-circle-full" style="color: lightskyblue" title="Em Aprovação"></i>
                  } @if (item.EnumStatus == (int)EnumStatusContabilidade.Aprovado)
                  {
                    <i class="mw-circle-full" style="color: green" title="Aprovado"></i>
                  }
                  @if (item.EnumStatus == (int)EnumStatusContabilidade.Reprovado)
                  {
                    <i class="mw-circle-full" style="color: red" title="Reprovado"></i>
                  }
                </td>
                <td>
                  @Html.DisplayFor(modelItem => item.CPFCNPJ)
                </td>
                <td>
                  @Html.DisplayFor(modelItem => item.Nome)
                </td>
                <td>
                  @Html.DisplayFor(modelItem => item.Email)
                </td>
                <td>
                  @Html.DisplayFor(modelItem => item.TelefoneCelular)
                </td>
                <td style="text-align:center">
                  @Html.DisplayFor(modelItem => item.Ativo)
                </td>
                <td>
                  <a class="btn btn-warning btn-sm" asp-action="Details" asp-route-id="@item.Id"><i class="mw-drip-pencil"></i>Avaliar</a>
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
      <div class="card-footer">
        @Html.PagedListPager((IPagedList)Model, page => Url.Action("IndexAprovacao", new { page }),
        new PagedListRenderOptions
             {
          LiElementClasses = new string[] { "page-item" },
          PageClasses = new string[] { "page-link" },
          Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
          MaximumPageNumbersToDisplay = 5
        })
      </div>
    </div>
  }
</div>

