﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model BasicRegisterModel

@{
  ViewData["Title"] = "Novo Usuario";
}
<style>
  .hide-body {
    display: none !important;
  }

  .card-children {
    box-shadow: none !important;
  }
</style>
<div class="card">
  <div class="card card-children" id="body-aba-users">
    @using (Html.BeginForm("CreateSecretaria", "Usuarios", FormMethod.Post))
    {
      <div class="card-body">
        @Html.HiddenFor(m => m.TipoCadastro)
        <div class="form-group">
          @Html.LabelFor(m => m.Nome)
          @Html.TextBoxFor(m => m.Nome, new { type = "text", @class = "form-control" })
          @Html.ValidationMessageFor(m => m.Nome)
        </div>
        <div class="form-group">
          @Html.LabelFor(m => m.Email)
          @Html.TextBoxFor(m => m.Email, new { type = "text", @class = "EMAIL form-control" })
          @Html.ValidationMessageFor(m => m.Email)
        </div>
        <div class="form-group">
          @Html.LabelFor(m => m.ConfirmarEmail)
          @Html.TextBoxFor(m => m.ConfirmarEmail, new { type = "text", @class = "EMAIL form-control" })
          @Html.ValidationMessageFor(m => m.ConfirmarEmail)
        </div>
        <div class="form-group">
          @Html.LabelFor(m => m.CPF)
          @Html.TextBoxFor(m => m.CPF, new { type = "text", @class = "CPF form-control" })
          @Html.ValidationMessageFor(m => m.CPF)
        </div>
        <div class="form-group">
          @Html.LabelFor(m => m.TelefoneCelular)
          @Html.TextBoxFor(m => m.TelefoneCelular, new { type = "text", @class = "telefone form-control" })
          @Html.ValidationMessageFor(m => m.TelefoneCelular)
        </div>
        <div class="form-group">
          @Html.LabelFor(m => m.Senha)
          @Html.TextBoxFor(m => m.Senha, new { type = "password", @class = "form-control" })
          @Html.ValidationMessageFor(m => m.Senha)
        </div>
        <div class="form-group">
          @Html.LabelFor(m => m.ConfirmarSenha)
          @Html.TextBoxFor(m => m.ConfirmarSenha, new { type = "password", @class = "form-control" })
          @Html.ValidationMessageFor(m => m.ConfirmarSenha)
        </div>
      </div>
      <div class="card-footer">
        <a asp-action="IndexSecretaria" class="link link-info">Voltar</a>
        <button type="submit" class="btn btn-success">Salvar</button>
      </div>
    }
  </div>
</div>
