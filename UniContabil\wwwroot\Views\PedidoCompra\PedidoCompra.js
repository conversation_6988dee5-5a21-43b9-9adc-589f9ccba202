﻿var PedidoCompra = function () {
};

PedidoCompra.init = function () {

  $(document).on("click", "#AdicionarItemCompra, #EditarItemCompra", function () {
    var url = '';
    $('#AdicionarItemCompra').show();
    $('#EditarItemCompra').hide();

    var create = $(this).val();

    if ($(this).val() == "0") {
      $('#CodigoModal').val("0");
      url = GetURLBaseComplete() + '/ItensPedidoCompra/Create';
    }
    else {
      url = GetURLBaseComplete() + '/ItensPedidoCompra/Edit';
    }

    var model = {
      CodigoOrganizacaoModal: $('#CodigoOrganizacao').val(),
      CodigoFilialModal: $('#FilialSelect_id').val(),
      CodPedidoCompraModal: $('#Codigo').val(),
      DescricaoProduto: $('#ProdutoSelect_text').val()
    };

    $('#CodigoOrganizacaoModal').val(model.CodigoOrganizacaoModal);
    $('#CodigoFilialModal').val(model.CodigoFilialModal);
    $('#CodPedidoCompraModal').val(model.CodPedidoCompraModal);
    $('#Descricao').val(model.DescricaoProduto);


    var valorUnitarioUnmask = $('#ValorUnitario').inputmask('unmaskedvalue');
    var DescontoUnmask = $('#Desconto').inputmask('unmaskedvalue');
    var DescontoPorcentagemUnmask = $('#DescontoPercentual').inputmask('unmaskedvalue');
    var ValorTotalUnmask = $('#ValorTotalItem').inputmask('unmaskedvalue');

    if (DescontoPorcentagemUnmask > 100) {
      DescontoPorcentagemUnmask = 100;
    }

    $('#ValorUnitario').inputmask('remove');
    $('#Desconto').inputmask('remove');
    $('#DescontoPercentual').inputmask('remove');
    $('#ValorTotalItem').inputmask('remove');

    $('#ValorUnitario').val(valorUnitarioUnmask);
    $('#Desconto').val(DescontoUnmask);
    $('#DescontoPercentual').val(DescontoPorcentagemUnmask);
    $('#ValorTotalItem').val(ValorTotalUnmask);

    var model = $('form').serialize();
    var idPedidoCompra = $('#Codigo').val();

    $.ajax({
      type: 'POST',
      url: url,
      dataType: 'json',
      data: model,
      success: function (data) {
        if (!data.erro) {
          $("#_ModalItensCompra").modal("hide");
          PedidoCompra.GridItensCompra(idPedidoCompra);
          MWERP.IniciaMascaras();
          Alerta(data.titulo, data.mensagem, "green", 5000);
          $('#AdicionarItemCompra').show();
          $('#ProdutoSelect_id').val("0");
          $('#ProdutoSelect_text').val("");
          $('#ProdutoSelectLookup').val(null);
          $('#ProdutoSelectLookup').trigger('change');
          $('#DivAddItensPedidoCompra input').val(null);
        }
        else {
          if (create != "0") {
            $('#AdicionarItemCompra').hide();
            $('#EditarItemCompra').show();
          }
          MWERP.IniciaMascaras();
          Alerta(data.titulo, data.mensagem, "red", 5000);
        }
      }
    });
  });

  $(document).on("click", ".RemoverItemCompra", function () {
    var model = {
      CodItemPedidoCompra: $(this).data("codigo")
    };
    var idPedidoCompra = $('#Codigo').val();

    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/ItensPedidoCompra/Delete',
      dataType: 'json',
      data: model,
      success: function (data) {
        if (!data.erro) {
          PedidoCompra.GridItensCompra(idPedidoCompra);
          Alerta(data.titulo, data.mensagem, "green", 5000);
        }
        else {
          Alerta(data.titulo, data.mensagem, "red", 5000);
        }
      }
    });
  });

  $(document).on("click", ".EditarItemCompra", function () {
    var model = {
      CodItemPedidoCompra: $(this).data("codigo")
    };
    var idPedidoCompra = $('#Codigo').val();

    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/ItensPedidoCompra/GetByCodigo',
      dataType: 'json',
      data: model,
      success: function (data) {
        if (!data.retorno.erro) {
          if (data.model.CodigoFilialModal == "" || data.model.CodigoFilialModal == "0")
            SweetAlert('Atenção', 'Gentileza selecionar uma filial antes de editar itens', 'warning', 'Fechar');
          else {
            $('#CodigoModal').val(data.model.codigoModal);
            $('#CodigoOrganizacaoModal').val(data.model.codigoOrganizacaoModal);
            $('#CodigoFilialModal').val(data.model.codigoFilialModal);
            $('#CodPedidoCompraModal').val(data.model.codPedidoCompraModal);

            $('#Unidade').val(data.model.unidade);
            $('#Quantidade').val(data.model.quantidade);
            $('#ValorUnitario').val(data.model.valorUnitario);
            $('#ValorTotalItem').val(data.model.valorTotalItem);
            $('#DescontoPercentual').val(data.model.descontoPercentual);
            $('#Desconto').val(data.model.desconto);
            $('#Descricao').val(data.model.descricao);
            $('#Observacoes').val(data.model.observacoes);
            $('#SequenciaItem').val(data.model.SequenciaItem);

            $('#ProdutoSelect_id').val(data.model.produtoSelect.id);
            $('#ProdutoSelect_text').val(data.model.produtoSelect.text);
            $('#ProdutoSelectLookup').val(data.model.produtoSelect.id);
            $('#ProdutoSelectLookup').append(`<option value='${data.model.produtoSelect.id}'>${data.model.produtoSelect.text}</option>`)
            $('#ProdutoSelectLookup').trigger('change');

            $('#ProdutoSelectLookup').trigger('change');

            $('#AdicionarItemCompra').hide();
            $('#EditarItemCompra').show();
          }
        }
        else {
          Alerta(data.titulo, data.mensagem, "red", 5000);
        }
      }
    });
  });

  $(document).on("click", "#OpenModalSeries", function () {
    $('#_ModalSerie input').val(null);

    var CodigoPedido = $('#Codigo').val();
    $('#CodigoPedido').val(CodigoPedido);
    $('#TipoPedido').val("1");
    $('#_ModalSerie').modal('show');

  });

  $(document).on("change", "#DescontoPercentual", function () {

    var desc = $(this).inputmask('unmaskedvalue');
    if (desc > 100) {
      $(this).val(100);
    }
  });
};

PedidoCompra.GridItensCompra = function (idPedidoCompra) {
  var url = GetURLBaseComplete() + '/PedidoCompra/_GridItensPedidoCompra';
  var model = {
    CodPedidoCompra: idPedidoCompra
  }

  $.ajax({
    url: url,
    cache: false,
    dataType: "html",
    type: "GET",
    data: model,
    success: function (resposta) {
      if (resposta.Erro) {
        Alerta('Erro', resposta.Mensagem, 'red');
      } else {
        $('#DivPedidosCompra').html("");
        $('#DivPedidosCompra').html(resposta);
      }
    }
  });
}

$(document).ready(function () {
  PedidoCompra.init();
});


