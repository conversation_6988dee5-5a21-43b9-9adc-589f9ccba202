﻿@model List<E_Grupo>
@using UniContabilEntidades.Models.DataBase

<ul class="list-group" style="overflow-y: auto">
  @foreach (E_Grupo grupo in Model)
  {
    <li class="list-group-item" data-grupo="@grupo.G_Id">@grupo.G_Nome</li>
  }
  @if(Model.Count == 0)
  {
    <li class="list-group-item">Nenhum grupo cadastrado.</li>
  }
</ul>

<script>
  $(document).ready(function () {
    $(".list-group li").click(function () {
      $(".list-group li").removeClass("active");
      $(this).addClass("active");

      getUsersGroup();
    });
  });
</script>