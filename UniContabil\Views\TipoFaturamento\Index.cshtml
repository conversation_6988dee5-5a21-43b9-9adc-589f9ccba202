﻿@model UniContabilEntidades.Models.TipoFaturamentoModel
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;

@{
  ViewData["Title"] ="Tipos de Faturamento";
  Layout = "~/Views/Shared/_Layout.cshtml";
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();
}

<div class="table-group card">
  <div class="card-header">
    <div class="card-title">Tipo Faturamento</div>
    <div class="card-options">
      @if (ContextoUsuario.HasPermission("TipoFaturamento", TipoFuncao.Create))
      {
        <p>
          <a class="btn btn-success" asp-action="Create"><i class="mw-drip-plus"></i></a>
        </p>
      }
    </div>
  </div>
  <div class="table-group-content">
    <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
      <thead>
        <tr>
          <th>
            @Html.DisplayNameFor(model => model.Codigo)
          </th>
          <th>
            @Html.DisplayNameFor(model => model.NomeOrganizacao)
          </th>

          <th>
            @Html.DisplayNameFor(model => model.NomeFilial)
          </th>
          <th>
            @Html.DisplayNameFor(model => model.Descricao)
          </th>
          <th>
            @Html.DisplayNameFor(model => model.RetenIss)
          </th>
          <th>
            @Html.DisplayNameFor(model => model.CalculaCMS)
          </th>
          <th>
            @Html.DisplayNameFor(model => model.CalculaConfins)
          </th>
          <th>
            @Html.DisplayNameFor(model => model.CalculaCSLL)
          </th>
          <th>
            @Html.DisplayNameFor(model => model.CalculaIR)
          </th>
          <th>
            @Html.DisplayNameFor(model => model.CalculaIss)
          </th>
          <th>
            @Html.DisplayNameFor(model => model.CalculaPI)
          </th>
          <th>
            @Html.DisplayNameFor(model => model.CalculaPIS)
          </th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        @foreach (UniContabilEntidades.Models.TipoFaturamentoModel item in ViewBag.PageItems)
        {
          <tr>
            <td>
              @Html.DisplayFor(modelItem => item.Codigo)
            </td>
            <td>
              @Html.DisplayFor(modelItem => item.NomeOrganizacao)
            </td>
            <td>
              @Html.DisplayFor(modelItem => item.NomeFilial)
            </td>
            <td>
              @Html.DisplayFor(modelItem => item.Descricao)
            </td>
            <td>
              @if (item.RetenIss == true)
              {
                <text>Sim</text>
              }
              else
              {
                <text>Não</text>
              }
            </td>
            <td>
              @if (item.CalculaCMS == true)
              {
                <text>Sim</text>
              }
              else
              {
                <text>Não</text>
              }
            </td>
            <td>
              @if (item.CalculaCMS == true)
              {
                <text>Sim</text>
              }
              else
              {
                <text>Não</text>
              }
            </td>
            <td>
              @if (item.CalculaConfins == true)
              {
                <text>Sim</text>
              }
              else
              {
                <text>Não</text>
              }
            </td>
            <td>
              @if (item.CalculaCSLL == true)
              {
                <text>Sim</text>
              }
              else
              {
                <text>Não</text>
              }
            </td>
            <td>
              @if (item.CalculaIR == true)
              {
                <text>Sim</text>
              }
              else
              {
                <text>Não</text>
              }
            </td>
            <td>
              @if (item.CalculaIss == true)
              {
                <text>Sim</text>
              }
              else
              {
                <text>Não</text>
              }
            </td>
            <td>
              @if (item.CalculaPI == true)
              {
                <text>Sim</text>
              }
              else
              {
                <text>Não</text>
              }
            </td>
            <td>
              @if (item.CalculaPIS == true)
              {
                <text>Sim</text>
              }
              else
              {
                <text>Não</text>
              }
            </td>
            <td>
              @if (ContextoUsuario.HasPermission("TipoFaturamento", TipoFuncao.Edit))
              {
               <a class="btn btn-warning btn-sm" asp-action="Edit" asp-route-id="@item.Id"><i class="mw-drip-document-edit"></i></a>
              }
              @if (ContextoUsuario.HasPermission("TipoFaturamento", TipoFuncao.Delete))
              {
                <button class="link link-danger" onclick="DeleteItem('@this.ViewContext.RouteData.Values["controller"].ToString()', @item.Id)">Apagar</button>
              }
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
  <div class="card-footer">

    @Html.PagedListPager((IPagedList)ViewBag.PageItems, page => Url.Action("Index", new { page }),
      new PagedListRenderOptions
      {
        LiElementClasses = new string[] { "page-item" },
        PageClasses = new string[] { "page-link" },
        Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
        MaximumPageNumbersToDisplay = 5
      })

  </div>
</div>
