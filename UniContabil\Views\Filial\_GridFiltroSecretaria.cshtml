﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model List<UsuarioBasico>

@{
  ViewData["Title"] = "Usuario";
}

@if (Model.Count > 0)
{
  <div class="table-group card">
    <div class="table-group-content">
      <div class="table-responsive">
        <table id="filiais-table"  >
          <thead>
            <tr>
              <th>
              </th>
            </tr>
            <tr>
              <th>
                Nome
              </th>
              <th>
                CNPJ/CPF
              </th>
              <th>
                Endereço
              </th>
              <th></th>
            </tr>

          </thead>

          <tbody>
            @for (int i = 0; i < Model.Count; i++)
            @*@foreach (var item in Model)*@
            {
              <tr data-trfilial="@Model[i].Codigo">
                @Html.HiddenFor(a => a[i].CPF)
                @Html.HiddenFor(a => a[i].Nome)
                <td>
                  <span> @Model[i].Nome </span>
                </td>
                <td> <span> @Model[i].CPF </span></td>
                <td> <span> @Model[i].Email </span></td>
                <td> <button class="btn btn-outline-primary btn-sm" onclick="Filial.SaveVinculoSecretaria('@Model[i].Codigo')" id="" style="height: 28px;margin-left: 2px;"> Associar </button> </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
      </div>
  </div>
}

