﻿@using UniContabil.Infrastructure.Controls
@using UniContabilEntidades.Models
@model OrganizacaoModal


<div class="form-group">
  <label for="UsuarioOrganizacaoSelect">Organização</label>
  @Html.Select2("UsuarioOrganizacaoSelect", "GetUsuarioOrganizacaoSelect", "Selecione " + Html.DisplayNameFor(model => model.IdOrganizacao), "Select2", "", Model.OrganizacaoSelect == null ? "" : Model.OrganizacaoSelect.id, Model.OrganizacaoSelect == null ? "" : Model.OrganizacaoSelect.text)
  @*@Html.Select2("UsuarioOrganizacaoSelect", "GetUsuarioOrganizacaoSelect", "Selecione a organização")*@
  <small id="mudarOrgHelp" class="form-text text-muted">O nome da orgnização ativa.</small>
</div>
<div class="form-group">
  <label for="UsuarioFilialSelect">Filial</label>
  @Html.Select2("UsuarioFilialSelect", "GetUsuarioFilialSelect", "Selecione " + Html.DisplayNameFor(model => model.IdFilial), "Select2", "", Model.FilialSelect == null ? "" : Model.FilialSelect.id, Model.FilialSelect == null ? "" : Model.FilialSelect.text, "UsuarioOrganizacaoSelect")
  <small id="mudarOrgHelp" class="form-text text-muted">O nome da filial ativa.</small>
</div>
<style>
  .form-text {
    margin-top: 3% !important;
  }
</style>