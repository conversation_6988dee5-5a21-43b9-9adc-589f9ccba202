<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Creator: CorelDRAW X8 -->
<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="211.785mm" height="196.521mm" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"
viewBox="0 0 21179 19652"
 xmlns:xlink="http://www.w3.org/1999/xlink">
 <defs>
  <style type="text/css">
   <![CDATA[
    .str0 {stroke:#499AC5;stroke-width:25}
    .fil1 {fill:url(#id0)}
    .fil0 {fill:url(#id1)}
   ]]>
  </style>
  <linearGradient id="id0" gradientUnits="userSpaceOnUse" x1="11662.4" y1="9808.21" x2="21194" y2="9808.21">
   <stop offset="0" style="stop-opacity:1; stop-color:#8BC216"/>
   <stop offset="0.501961" style="stop-opacity:1; stop-color:#A9F56E"/>
   <stop offset="0.988235" style="stop-opacity:1; stop-color:#C7FFC7"/>
   <stop offset="1" style="stop-opacity:1; stop-color:#C7FFC7"/>
  </linearGradient>
  <linearGradient id="id1" gradientUnits="userSpaceOnUse" x1="16256.7" y1="19652.1" x2="16256.7" y2="35.62">
   <stop offset="0" style="stop-opacity:1; stop-color:#C5DDE7"/>
   <stop offset="0.243137" style="stop-opacity:1; stop-color:#E2EEF3"/>
   <stop offset="0.568627" style="stop-opacity:1; stop-color:#E5EEED"/>
   <stop offset="1" style="stop-opacity:1; stop-color:white"/>
  </linearGradient>
 </defs>
 <g id="Camada_x0020_1">
  <metadata id="CorelCorpID_0Corel-Layer"/>
  <g id="_1676980068496">
   <path class="fil0" d="M11604 19652l-11604 0 0 -19616 21120 0c0,2668 -6026,3223 -6026,7637 0,2749 2195,4071 2195,6821 0,2313 -1648,4432 -5685,5158z"/>
   <path class="fil1 str0" d="M21179 0c0,0 -1,25 -3,74 -1,25 -1,55 -6,91 -4,36 -7,77 -16,124 -31,185 -113,458 -314,751 -198,293 -506,599 -894,901 -387,305 -852,609 -1357,944 -503,336 -1050,701 -1579,1149 -133,112 -263,230 -390,355 -128,124 -252,254 -371,392 -238,275 -453,581 -627,916 -88,168 -162,344 -226,524 -64,181 -115,368 -152,559 -37,190 -63,384 -73,581 -4,49 -6,98 -6,147 -1,49 -3,99 -2,148 3,99 2,198 10,297 27,395 97,791 217,1174 57,193 129,381 204,568 78,185 161,369 250,550 356,723 783,1414 1084,2152 150,368 271,747 341,1131 71,383 97,771 81,1149 -20,379 -80,749 -188,1098 -108,349 -255,677 -436,973 -362,595 -842,1062 -1331,1418 -244,180 -493,331 -736,462 -242,133 -481,240 -707,337 -455,189 -866,316 -1206,410 -342,90 -613,150 -799,186 -185,36 -285,55 -285,55 0,0 99,-19 285,-55 186,-37 457,-98 798,-188 340,-95 751,-223 1205,-412 226,-98 465,-205 706,-339 242,-131 491,-282 734,-462 487,-357 965,-824 1326,-1418 180,-296 325,-624 432,-971 107,-348 166,-718 186,-1094 14,-378 -12,-764 -84,-1146 -71,-382 -192,-759 -342,-1126 -302,-735 -730,-1424 -1087,-2149 -89,-181 -173,-365 -250,-552 -75,-187 -147,-376 -204,-570 -120,-385 -189,-783 -215,-1180 -9,-100 -7,-199 -10,-298 -1,-50 1,-99 2,-148 1,-50 3,-99 7,-148 10,-198 37,-393 74,-584 37,-192 89,-379 154,-561 65,-181 140,-357 228,-525 176,-337 392,-643 631,-918 119,-139 244,-269 372,-393 128,-124 259,-242 392,-354 531,-448 1078,-812 1583,-1147 505,-334 970,-637 1358,-941 388,-301 697,-606 895,-899 201,-291 284,-564 315,-749 10,-47 12,-88 17,-124 4,-36 5,-66 6,-91 2,-49 3,-74 3,-74z"/>
  </g>
 </g>
</svg>
