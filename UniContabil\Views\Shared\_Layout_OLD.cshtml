﻿<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>@ViewData["Title"] - MWERP</title>
  <link rel="stylesheet" href="~/Content/MWFont.css" />
  <link rel="stylesheet" href="~/Content/Site-1.0.0.css?oioi=oioi" />
  <link rel="stylesheet" href="~/Content/select2.min.css" />
  <link rel="stylesheet" href="~/Content/iziToast.min.css" />
  <link rel="stylesheet" href="~/Content/jstree.min.css" />
  <link rel="stylesheet" href="~/Content/mmmicons.css" />
  <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
  <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap-grid.min.css" />

  <script src="~/lib/jquery/dist/jquery.min.js"></script>
  <script src="~/js/Site-1.0.0.js?c=c"></script>
  <script src="~/js/jquery-ui.min.js"></script>
  <script src="~/lib/bootstrap/dist/js/bootstrap.js"></script>
  <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="~/js/select2.min.js"></script>
  <script src="~/js/Select2Controls-1.0.0.js"></script>
  <script src="~/js/iziToast.min.js"></script>
  <script src="~/js/SweetAlert/SweetAlert.js"></script>
  <script src="~/js/jstree.min.js"></script>
  <script src="~/Scripts/inputmask/inputmask.js"></script>
  <script src="~/Scripts/inputmask/jquery.inputmask.js"></script>
  <script src="~/Scripts/inputmask/inputmask.extensions.js"></script>
  <script src="~/Scripts/inputmask/inputmask.date.extensions.js"></script>
  <script src="~/Scripts/inputmask/inputmask.numeric.extensions.js"></script>
  <script src="~/Views/Shared/Shared.js"></script>

</head>
<body>
  @if (ContextoUsuario.UserLogged != null)
  {
    <div class="layout-background">
      <div style="width: 15%">
        <img class="logo" src="~/Content/img/logo-mw.png" />
        <!--sidebar-->
        <div class="sidebar">
          <!--modulos-->
          @foreach (SidebarModuloModel Modulo in ContextoUsuario.UserLogged.Sidemenu)
          {
            <div class="sidebar-group-title">
              <div></div>
              <p>@(Modulo.Nome)</p>
              <div></div>
            </div>
            <div class="sidebar-group">
              @foreach (SidebarItemModel Item in Modulo.Itens)
              {
                <div class="sidebar-menu">
                  <i class="sidebar-menu-icon"></i>
                  <a href="@if (!string.IsNullOrEmpty(Item.ActionName)) { <text> @Url.Action(Item.ActionName, Item.ControllerName) </text>} else { <text>#</text> } ">@Item.Nome</a>

                  @if (Item.Itens.Count > 0)
                  {
                    <i class="sidebar-menu-arrow mw-drip-chevron-down collapseDown" href="@string.Format("#collapse{0}", Item.Id)" data-toggle="collapse" aria-expanded="false" aria-controls="@string.Format("collapse{0}", Item.Id)"></i>
                  }
                  <!--submenus-->
                  <div class="sidebar-item collapse" id="@string.Format("collapse{0}", Item.Id)">
                    @foreach (SidebarItemModel Option in Item.Itens)
                    {
                      @:<a href="@Url.Action(Option.ActionName, Option.ControllerName)">@Option.Nome</a>
                    }
                  </div>
                </div>
              }
            </div>
          }
          <div class="sidebar-group">
            <div class="sidebar-menu">
              <a href="@Url.Action("Logout", "Account")">
                Sair
              </a>
            </div>
          </div>
        </div>
      </div>
      <div class="content">
        <!--header-->
        <div class="header">
          <!--logo-->
          <div class="view-title">@ViewData["Title"]</div>
          <div style="margin-right: 10px">
            <p class="username">@ContextoUsuario.UserLogged.Nome</p>
            <div style="display: flex">
              <p class="userdata">Organização: <span class="datavalue">@ContextoUsuario.GetOrganizacaoNome()</span></p>
              <p class="userdata">
                Filial:
                <span class="datavalue">@ContextoUsuario.GetFilialNome()</span>
                <a class="change-data link link-info" href="#" data-toggle="modal" data-target="#modalMudarOrg">Alterar</a>
              </p>
            </div>
          </div>
        </div>
        <hr style="margin-bottom: 0 !important;"/>
        <!--page-->
        <div class="page-content">
          @RenderBody()
        </div>
      </div>
    </div>
  }

  @using UniContabil.Infrastructure
  @using UniContabil.Infrastructure.Controls
  @Html.LibMessageBoxAlert()
  @RenderSection("Scripts", required: false)
  @{
    await Html.RenderPartialAsync("_ModalOrganizacao");
  }
</body>
</html>
