﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using UniContabil.Infrastructure.Exceptions;

using UniContabilEntidades.Models;
using UniContabilDomain.Services;
using UniContabilEntidades.Models.DataBase;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Linq;

namespace UniContabil.Controllers
{
  [Authorize]
  public class AccountController : LibController
  {
    [AllowAnonymous]
    public ActionResult Confirm(string Id)
    {
      try
      {
        if (string.IsNullOrEmpty(Id))
          return RedirectToAction("Login", "Account");

        string Email = Encoding.UTF8.GetString(Convert.FromBase64String(Id));

        UsuariosServices UserServices = new UsuariosServices();
        //E_Usuarios User = UserServices.GetByEmail(Email);

        //if (User == null)
        //{
        //  string Erro = string.Format("Usuário com código inválido na confirmação de email. Código: {0} Decriptado: {1}", Id, Email);
        //  throw new Exception(Erro);
        //}

        //User.U_ConfirmaEmail = true;
        //UserServices.Edit(User);

        //string Mensagem = string.Format(@"",
        //                                  User.U_Nome);

        //EMailHelper.Send(new string[1] { User.U_Email }, "Bem-vindo ao MWERP", Mensagem);

        MessageAdd(new Message(MessageType.Success, "Sua conta foi confirmada com sucesso."));
        return RedirectToAction("Login", "Account");
      }
      catch (Exception ex)
      {
        string Erro = string.Format("\n[{0}] {1}.", DateTime.Now.ToString("dd/MM/yyyy HH:mm"), ex.Message);
        //System.IO.File.AppendAllText(ConfigurationManager.AppSettings["PathSaveTXT"] + "out.txt", Erro);

        MessageAdd(new Message(MessageType.Error, "Houve um erro ao tentar confirmar a conta. Tente mais tarde."));
        return RedirectToAction("Login", "Account");
      }
    }

    [AllowAnonymous]
    public async Task<ActionResult> Login()
    {
      try
      {
        ContextoUsuario.Logout();
        await HttpContext.SignOutAsync();
        return View();
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [AllowAnonymous]
    public ActionResult ForgotPassword()
    {
      return View();
    }

    [AllowAnonymous]
    public ActionResult ResetPass(Guid Token)
    {
      AccountServices service = new AccountServices();
      bool TokenIsTrue = service.VerificaToken(Token);
      if (TokenIsTrue)
      {
        MessageAdd(new Message(MessageType.Success, "Digite sua nova senha"));
        ViewBag.Token = Token;
        return View();
      }
      else
      {
        ViewBag.Token = Token;
        MessageAdd(new Message(MessageType.Info, "Este token não existe, ou está expirado!"));
        return RedirectToAction(nameof(Login));
      }
    }

    [HttpPost]
    [AllowAnonymous]
    public ActionResult ResetPass(PassToRecovery model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          if (model.IsEquals())
          {
            new AccountServices().PassReset(model);
            MessageAdd(new Message(MessageType.Success, "Senha alterada com sucesso!"));
            return RedirectToAction(nameof(Login));
          }
          ViewBag.Token = model.token;
          MessageAdd(new Message(MessageType.Error, "Senhas não conferem!"));
          return View(model);
        }
        ViewBag.Token = model.token;
        MessageAdd(new Message(MessageType.Error, "Verifique os campos!"));
        return View(model);
      }
      catch (Exception ex)
      {
        ViewBag.Token = model.token;
        MessageAdd(new Message(MessageType.Error, "Aconteceu algum erro desconhecido!"));
        return View(model);
      }
    }


    [HttpPost]
    [AllowAnonymous]
    public ActionResult ForgotPassword(RecuperaAcc model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new AccountServices().ResetAccount(model);
          MessageAdd(new Message(MessageType.Success, "Foi enviado um email com instruções para você!"));
          return RedirectToAction(nameof(Login));
        }
        MessageAdd(new Message(MessageType.Success, "Verifique os campos!"));
        return View(model);
      }
      catch (CustomException ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        return RedirectToAction(nameof(Login));
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, "Aconteceu um erro inesperado!"));
        return RedirectToAction(nameof(Login));
      }
    }

    [HttpPost]
    [AllowAnonymous]
    public ActionResult Login(LoginModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          AccountServices accountServices = new AccountServices(model.ConnectionId);
          E_Usuarios TcUsuarios = accountServices.Login(model);

          if (TcUsuarios == null)
            throw new Exception("Usuário não encontrato");

          var claims = new List<Claim>
          {
              new Claim("UserCodigo", TcUsuarios.U_Id.ToString()),
              new Claim("UserName", TcUsuarios.U_Nome)
          };

          var principal = new ClaimsPrincipal();
          principal.AddIdentity(new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme));
          HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal);

          List<PermissaoUsuarioModel> Permissoes = new GrupoUsuarioServices().GetPermissoesByIdUsuario(TcUsuarios.U_Id);

          if (Permissoes != null)
          {
            bool hasPermissionContb = Permissoes.Where(a => a.NomeGrupo.Equals("Contabilidade")).Count() > 0;
            bool hasPermissionUni = Permissoes.Where(a => a.NomeGrupo.Equals("Unicooper")).Count() > 0;
            bool hasPermissionMed = Permissoes.Where(a => a.NomeGrupo.Equals("Cooperado")).Count() > 0;

            if (hasPermissionContb)
            {
              var contabilidade = new ContabilidadeService().GetByIdUsuario(TcUsuarios.U_Id);
              int idStatus = new StatusContabilidadeServices().GetIdByEnum(EnumStatusContabilidade.EmCadastro);
              if (contabilidade != null)
              {
                if (contabilidade.C_IdStatus == idStatus)
                  return RedirectToAction("Edit", "Contabilidade", new { Id = contabilidade.C_Id });

                return RedirectToAction("Index", "ClassificacaoDocumentos");
              }
            }
            else if (hasPermissionUni)
            {
              return RedirectToAction("IndexAprovacao", "Contabilidade");
            }
            else if (hasPermissionMed)
            {
              return RedirectToAction("Index", "ClassificacaoNotasFiscais");
            }
            
          }
          //MessageAdd(new Message(MessageType.Success, "Login realizado com sucesso."));
          return RedirectToAction("Index", "ClassificacaoNotasFiscais");
        }

        MessageAdd(new Message(MessageType.Warning, "Verifique os campos preenchidos."));
        return View(model);
      }
      catch (CustomException ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        TimeOutMessage = "120000";
        return View(model);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        TimeOutMessage = "120000";
        return View(model);
      }
    }

    [HttpGet]
    public ActionResult ContinuaRegistro()
    {
      return View();
    }

    [AllowAnonymous]
    //[NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public async Task<ActionResult> LogOutAsync()
    {
      try
      {
        ContextoUsuario.Logout();
        await HttpContext.SignOutAsync();
        return RedirectToAction("Login", "Account");
      }
      catch (CustomException Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [AllowAnonymous]
    public ActionResult Register()
    {
      return View();
    }

    [HttpPost]
    [AllowAnonymous]
    public ActionResult Register(BasicRegisterModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          AccountServices accountServices = new AccountServices();
          accountServices.RegistrarUsuario(model);

          if (model.EnumTipoCadastro == EnumTipoCadastro.Contabilidade)
          {
            LoginModel lm = new LoginModel();
            lm.CPF = model.CPF;
            lm.Senha = model.ConfirmarSenha;

            E_Usuarios TcUsuarios = accountServices.Login(lm);

            if (TcUsuarios == null)
              throw new Exception("Usuário não encontrato");

            var claims = new List<Claim>
            {
                new Claim("UserCodigo", TcUsuarios.U_Id.ToString()),
                new Claim("UserName", TcUsuarios.U_Nome)
            };

            var principal = new ClaimsPrincipal();
            principal.AddIdentity(new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme));
            HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal);
            var contabilidade = new ContabilidadeService().GetByIdUsuario(TcUsuarios.U_Id);
            int idStatus = new StatusContabilidadeServices().GetIdByEnum(EnumStatusContabilidade.EmCadastro);

            if (contabilidade != null)
            {
              if (contabilidade.C_IdStatus == idStatus)
                return RedirectToAction("Edit", "Contabilidade", new { Id = contabilidade.C_Id });

              return RedirectToAction("Index", "ClassificacaoDocumentos");
            }

            //MessageAdd(new Message(MessageType.Success, "Login realizado com sucesso."));
            return RedirectToAction("Index", "ClassificacaoNotasFiscais");
          }
          else if (model.EnumTipoCadastro == EnumTipoCadastro.Medico)
          {
            LoginModel lm = new LoginModel();
            lm.CPF = model.CPF;
            lm.Senha = model.ConfirmarSenha;

            E_Usuarios TcUsuarios = accountServices.Login(lm);

            if (TcUsuarios == null)
              throw new Exception("Usuário não encontrato");

            var claims = new List<Claim>
            {
                new Claim("UserCodigo", TcUsuarios.U_Id.ToString()),
                new Claim("UserName", TcUsuarios.U_Nome)
            };

            var principal = new ClaimsPrincipal();
            principal.AddIdentity(new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme));
            HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal);

            E_Filial filial = new FilialServices().GetFilialUser(TcUsuarios.U_CPF);
            return RedirectToAction("Index", "Filial");
          }

          //MessageAdd(new Message(MessageType.Success, "Cadastro efetuado com sucesso, acesse seu e-mail para confirmar."));
          return RedirectToAction("Login");
        }
        MessageAdd(new Message(MessageType.Warning, "Verifique os campos preenchidos."));
        return View(model);
      }
      catch (CustomException ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        TimeOutMessage = "120000";
        return View(model);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.ToString()));
        TimeOutMessage = "120000";
        return View(model);
      }
    }

    public string ChangeOrganizacao(int idFilial)
    {
      try
      {
        E_OrganizacaoUsuario OU = new OrganizacaoUsuarioServices().ChangeOrganizacao(idFilial);

        if (OU != null)
        {
          ContextoUsuario.ChangeOrganizacao(OU.IdOrganizacao, OU.IdFilial);

          return JsonConvert.SerializeObject(new { Sucesso = true });
        }

        return JsonConvert.SerializeObject(new { Sucesso = false, Reload = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    public string GetMessageErroModelState(ModelStateDictionary ModelState)
    {
      StringBuilder modelErrors = new StringBuilder();
      foreach (var modelState in ModelState.Values)
      {
        foreach (ModelError modelError in modelState.Errors)
        {
          modelErrors.AppendLine(modelError.ErrorMessage + " ");
        }
      }
      return modelErrors.ToString();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
      return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
  }
}