﻿var MovimentacaoRecorrente = function () { }

var Tipo = 2;
var IdReloadGrid = null;


MovimentacaoRecorrente.GeraLancamento = function () {
  $.ajax({
    type: "GET",
    url: GetURLBaseComplete() + "/MovimentacaoRecorrente/Gerar?IdMov=" + $("#IdItem").val(),
    success: function (data) {
      if (data.erro) {
        MovimentacaoRecorrente.AtualizaLancamentos();
        SweetAlert("Erro!", data.message, 'error')
      } else {
        MovimentacaoRecorrente.AtualizaLancamentos();
        SweetAlert("Sucesso!", data.message, 'sucess')
      }
    }
  })
}

MovimentacaoRecorrente.Delete = function (id) {
  swal({
    title: "Apagar",
    text: "Tem certeza que deseja apagar?",
    icon: "warning",
    buttons: ["Cancelar", "Apagar"],
    dangerMode: true,
  }).then((continuar) => {
    if (continuar) {
      $.ajax({
        type: "POST",
        url: GetURLBaseComplete() + "/MovimentacaoRecorrente/Delete/" + id,
        dataType: "json",
        success: function (data) {
          if (!data.error) {
            SweetAlert("Sucesso", data.message, "success");
            location.reload();
          } else {
            SweetAlert("Erro", data.message, "error");
          }
        }
      })
    }
  });

}

MovimentacaoRecorrente.AtualizaLancamentos = function () {
  var data = {
    Id: $("#IdItem").val(),
    dataAte: $("#DataAte").val(),
    dataDe: $("#DataDe").val()
  }
  $.ajax({
    type: "POST",
    data: data,
    url: GetURLBaseComplete() + "/MovimentacaoRecorrente/_GetPartialItens/" + $("#IdItem").val(),
    success: function (data) {
      $("#GridLancamentos").html(data)
    }
  })
}

//MovimentacaoRecorrete.Delete = function (url, id) {
//  swal({
//    title: "Apagar",
//    text: "Tem certeza que deseja apagar?",
//    icon: "warning",
//    buttons: ["Cancelar", "Apagar"],
//    dangerMode: true,
//  })
//    .then((continuar) => {
//      if (continuar) {
//        $.post(GetURLBaseComplete() + "/" + url + "/Delete/" + id, function (data) {
//          data = JSON.parse(data);
//          if (data.Sucesso) {
//            location.reload();
//          }
//          else {
//            Alerta("Erro", data.Mensagem, "red");
//          }
//        });
//      }
//    });
//}


MovimentacaoRecorrente.GetTipo = function () {
  var listcheck = $(".custom-radio");

  for (var i = 0; i < listcheck.length; i++) {
    if ($(".custom-radio")[i].checked)
      Tipo = $(".custom-radio")[i].value;

    return Tipo;
  }
}

MovimentacaoRecorrente.makeTreeItem = function (el) {
  return $("<a>", {
    id: $(el).attr("id") + "_anchor",
    class: "jstree-anchor",
    href: "#"
  });
}

MovimentacaoRecorrente.LoadTree = function () {
  $("#tree").jstree({
    'core': {
      'check_callback': function (
        operation,
        node,
        node_parent,
        node_position,
        more
      ) {
        if (more.dnd != null && more.dnd == true) {
          if (operation === "move_node") {
            return false;
          } else if (operation === "delete_node") {
            return false;
          }
        }
      },
      'data': {
        'url': function () {
          return GetURLBaseComplete() + '/ClassificacaoNotasFiscais/_GetTreeview?Tipo=' + MovimentacaoRecorrente.GetTipo();
        },
        'dataType': 'json'
      },
      'themes': {
        'icons': false
      }
    },
    'plugins': ["dnd"]
  }).on("select_node.jstree", function (e, data) {
    IdReloadGrid = data.node.a_attr.codClassificacao;
  });
}

MovimentacaoRecorrente.AtualizaGrid = function (tipo, codclass) {
  $.ajax({
    url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/_GetGridClassificado",
    dataType: "html",
    type: "GET",
    data: {
      Tipo: tipo,
      Ano: $("#ano").val(),
      codclass: codclass
    },
    success: function (retorno) {
      $(".divTable").html("");
      $(".divTable").html(retorno);
    }
  });
};

MovimentacaoRecorrente.init = function () {

  $(document).on("click", "#classificar", function () {
    $("#modalclassificacao").modal("show");
  });

  $(document).on("click", ".custom-radio", function () {
    Tipo = $(this).val();

    $("#tree").jstree(true).refresh()
  });


  $(document).on("dblclick.jstree", function (e) {
    let node = $(e.target).closest("a");
    const attr = node[0].attributes;
    $("#Classificacao").val(node[0].outerText);
    $("#IdClassificacao").val(attr.codclassificacao.value);
    $("#modalclassificacao").modal("hide");
  });

  var FileName = $("#FileName")
  if (FileName.val() != "" && FileName.val() != 'undefined') {
    $("#textoAnexo").text(FileName.val());
    $("#btnAdcAnexo").text("Remover");
    $("#btnAdcAnexo").removeClass();
    $("#btnAdcAnexo").addClass("btn btn-outline-danger");
    $("#btnAdcAnexo").attr("id", "btnRemoveAnexo");
  }
  $(document).on("click", "#SubmitRequest", function () {
    swal({
      title: 'Confirmar alteração?',
      text: "Os lançamentos dessa movimentação serão recriados.",
      icon: 'warning',
      buttons: [
        'Não, cancelar.',
        'Sim, editar e gerar!',
      ]
    }).then(function (isConfirm) {
      if (isConfirm) {
        $("#SubmitForm").trigger('click');
      } else {
      }
    });
  });


  $(document).on("click", "#btnAdcAnexo", function () {
    $("#anexo").trigger('click');
  })

  $(document).on("click", "#btnRemoveAnexo", function () {
    $("#textoAnexo").text("Nada Anexado");
    $("#btnRemoveAnexo").text("Anexar");
    $("#btnRemoveAnexo").removeClass();
    $("#btnRemoveAnexo").addClass("btn btn-outline-success");
    $("#btnRemoveAnexo").attr("id", "btnAdcAnexo");
    $("#anexo").val("");
    $("#HasRemovedAnexo").val("true");
  });

  $(document).on("change", "#anexo", function (a) {
    if (typeof (FileReader) !== "undefined") {
      var fileName = a.target.value.split('\\').pop();
      var extensao = fileName.split(".")[fileName.split(".").length - 1];
      var reader = new FileReader();
      if (extensao == "pdf" || extensao == "png" || extensao == "jpg") {
        $("#textoAnexo").text(fileName);
        $("#FileName").text(fileName);
        $("#btnAdcAnexo").text("Remover");
        $("#btnAdcAnexo").removeClass();
        $("#HasRemovedAnexo").val("false");
        $("#btnAdcAnexo").addClass("btn btn-outline-danger");
        $("#btnAdcAnexo").attr("id", "btnRemoveAnexo");
      } else {
        $("#anexo").val("");
        SweetAlert("Erro", "O arquivo anexado é diferente do arquivo permitido", "error")
      }


      //reader.onload = function (e) {

      //}
      //reader.readAsDataURL($('#anexo')[0].files[0]);

    }
    else {
      Alerta("Compatibilidade", "Este navegador não suporta FileReader.", 'yellow', 5000);
    }
  })

  $(document).on("click", "#btnGerar", function () {
    swal({
      title: 'Você tem certeza?',
      text: "Caso já tenha lançamentos desta movimentação, os mesmo serão substituidos.",
      icon: 'warning',
      buttons: [
        'Não, cancelar.',
        'Sim, gerar!',
      ]
    }).then(function (isConfirm) {
      if (isConfirm) {
        MovimentacaoRecorrente.GeraLancamento();
      } else {
      }
    });
  });

  $(document).on("click", "#btnPesquisar", function () {
    MovimentacaoRecorrente.AtualizaLancamentos();
  });

  $(document).on("change", "#selectAll", function () {
    const check = this.checked
    $('input:checkbox').not(this).prop('checked', check);

  });

  $(document).on("click", "input[name='changeBody']", function () {
    var input = $("input[name='changeBody']:checked")
    if (input.attr('value') == 1) {
      //$("#inputValue").removeClass()
      //$("#inputValue").addClass('form-control percent')
      $(".SelectItemAdcOrSub").attr('hidden', false)
      $("#labelInputValue").text('Percentual:');
      $("#inputValue").maskMoney("destroy");
      $("#inputValue").val("");
      $("#inputValue").inputmask('decimal', {
        removeMaskOnSubmit: true,
        alias: 'numeric',
        groupSeparator: '.',
        autoGroup: true,
        digits: 2,
        integerDigits: 7,
        radixPoint: ",",
        digitsOptional: false,
        allowMinus: false,
        suffix: ' %',
        placeholder: ''
      });
    } else if (input.attr('value') == 2) {
      //$("#inputValue").removeClass()
      //$("#inputValue").addClass('form-control money')
      $(".SelectItemAdcOrSub").attr('hidden', true)
      $("#selectItemAcres").prop('checked', true)
      $("#labelInputValue").text('Valor do lançamento:')
      $("#inputValue").inputmask("remove");
      $("#inputValue").val("");
      $("#inputValue").maskMoney({
        prefix: 'R$ ',
        affixesStay: false,
        allowNegative: false,
        thousands: '',
        decimal: ',',
        affixesStay: false
      });
    }
  });

  $(document).on("click", "#EditItens", function () {
    $.ajax({
      type: "GET",
      url: GetURLBaseComplete() + "/MovimentacaoRecorrente/GetPartialEditItem",
      success: function (data) {
        $("#ModalValor .modal-body").html(data)
        $("#ModalValor").modal('show');
        $("#inputValue").inputmask('decimal', {
          removeMaskOnSubmit: true,
          alias: 'numeric',
          groupSeparator: '.',
          autoGroup: true,
          digits: 2,
          integerDigits: 7,
          radixPoint: ",",
          digitsOptional: false,
          allowMinus: false,
          suffix: ' %',
          placeholder: ''
        });
      }
    })
  });

  $(document).on("click", "#DeleteItens", function () {
    swal({
      title: 'Você tem certeza?',
      text: "Isso irá apagar os lançamentos selecionados!",
      icon: 'warning',
      buttons: [
        'Não, cancelar.',
        'Sim, Apagar!',
      ]
    }).then(function (isConfirm) {
      if (isConfirm) {
        var model = {
          Ids: []
        }
        var checkeds = $("#TableRow input[type='checkbox']:checked")
        if (checkeds.length < 1) {
          SweetAlert('Erro', "Favor selecionar um item.", 'error');
        } else {
          for (var i = 0; i < checkeds.length; i++) {
            model.Ids.push($(checkeds[i]).attr("Codigo"));
          }
          $.ajax({
            type: 'POST',
            data: model,
            url: GetURLBaseComplete() + "/MovimentacaoRecorrente/DeleteLancamento",
            success: function (data) {
              if (data.erro) {
                SweetAlert('Erro', data.message, 'error');
              } else {
                SweetAlert('Sucesso', data.message, 'sucess');
                MovimentacaoRecorrente.AtualizaLancamentos();
              }
            }
          })
        }
      }
    });


  })

  $(document).on("click", "#btnEnviarEdit", function () {
    var model = {
      Ids: [],
      percentOrFixed: $("input[name='changeBody']:checked").attr('value'),
      adcOrSub: $("input[name='choseOp']:checked").attr('value'),
      Valor: $("#inputValue").inputmask('unmaskedvalue')
    }
    var checkeds = $("#TableRow input[type='checkbox']:checked")
    for (var i = 0; i < checkeds.length; i++) {
      model.Ids.push($(checkeds[i]).attr("Codigo"));
    }

    if (checkeds.length == 0) {
      SweetAlert('Erro', "Favor selecione um item", 'error');
    } else if (model.percentOrFixed == 1 && parseFloat(model.Valor) > 100) {
      SweetAlert('Erro', "Selecione um valor até 100%.", 'error');
    }
    else {
      $.ajax({
        type: 'POST',
        data: model,
        url: GetURLBaseComplete() + "/MovimentacaoRecorrente/AlterarItens",
        success: function (data) {
          if (data.erro) {
            SweetAlert('Erro', data.message, 'error');
          } else {
            SweetAlert('Sucesso', data.message, 'sucess');
            $("#ModalValor").modal('hide');
            MovimentacaoRecorrente.AtualizaLancamentos();
          }
        }
      })
    }
  })

  $(document).on("change", "#selectAll", function () {
    const check = this.checked
    $('input:checkbox').not(this).prop('checked', check);


  });
  $(document).on("change", "#TableRow input:checkbox", function () {
    var checks = $(".check")
    var checkeds = $(".check:checked")
    console.log('Clicado')
    if (checkeds.length == checks.length) {
      $("#selectAll").prop('checked', true);
    } else if (checkeds.length < checks.length) {
      $("#selectAll").prop('checked', false);

    }
  });

  MovimentacaoRecorrente.AtualizaLancamentos();
  $(document).on("keyup", "#CPFORCNPJ", function () {
    if ($(this).inputmask('unmaskedvalue').length == 11) {
      $("#nomeCPF").removeAttr('hidden')
      $("#dataCPF").removeAttr('hidden')
    } else {
      $("#nomeCPF").attr('hidden', true)
      $("#dataCPF").attr('hidden', true)
      $("#DataNascimento").val('')
      $("#Nome").val('')
    }
  })
}

$(document).ready(function () {
  MovimentacaoRecorrente.init();
})