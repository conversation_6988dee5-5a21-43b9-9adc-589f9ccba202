﻿var Perfil = {};

Perfil.Init = function () {

  $.ajax({
    url: GetURLBaseComplete() + "/Perfil/_GridDependentes",
    dataType: "html",
    type: "GET",
    success: function (data) {
      $("#DependentesPartial").html("");
      $("#DependentesPartial").html(data);

    }
  });
  $(document).on('click', '#createDependente', function () {
    $("#modalDependentesCreate").modal('show');

  })

  $(document).on('click', '#editarDependente', function () {
    $("#modalDependentesEdit").modal('show');
  })

  $(document).on('click', '#deleteDependentes', function () {
    var id = $(this).data("cod");
    $.ajax({
      url: GetURLBaseComplete() + '/Perfil/DeleteDependente/' + id,
      cache: false,
      dataType: "json",
      data: $("#formCreateSocios").serialize(),
      type: "POST",
      success: function (resposta) {
        if (resposta.erro) {
          alert("Alerta", resposta.mensagem, "error", 5000);
        }
        else {
          SweetAlert("Dependente Deletado com sucesso!", resposta.mensagem, "success", 5000);
          location.reload();
        }
      },
      error: function (error) {
        console.log(error);
      }
    });

  });

  $(document).on('click', '#enviarDependente', function () {
    $.ajax({
      url: GetURLBaseComplete() + '/Perfil/CreateDependentes',
      cache: false,
      dataType: "json",
      data: $("#formCreateDependentes").serialize(),
      type: "POST",
      success: function (resposta) {
        if (resposta.erro) {
          SweetAlert("Favor verificar os campos", resposta.mensagem, "error", 5000);
        } else {
          SweetAlert("Dependente Criado com sucesso!", resposta.mensagem, "success", 5000);

          location.reload();
          //$(document).on('click', '#createDependente', function () {
          //  $("#modalDependentesCreate").modal('hide');
          //})
        }
      },
      error: function (error) {
        console.log(error);
      }
    });
  });

  $(document).on('click', '#enviarEditDependente', function () {
    var id = $(this).data("cdd");

    $.ajax({
      url: GetURLBaseComplete() + '/Perfil/EditDependentes/' + id,
      cache: false,
      dataType: "json",
      data: $("#formEditDependentes").serialize(),
      type: "POST",
      success: function (resposta) {
        if (resposta.erro) {
          SweetAlert("Favor Verificar os campos", resposta.mensage, "error", 5000);
        } else {
          SweetAlert("Dependente Editado com sucesso!", resposta.mensage, "success", 5000);

          location.reload();
          //$(document).on('click', '#createDependente', function () {
          //  $("#modalDependentesCreate").modal('hide');
          //})
        }
      },
      error: function (error) {
        console.log(error);
      }
    });

  });

  $(document).on('click', '#editarDependente', function () {
    var id = $(this).data("cdd");
    $.ajax({
      url: GetURLBaseComplete() + '/Perfil/_GridEditDependentes/' + id,
      cache: false,
      dataType: "html",
      type: "GET",
      success: function (data) {
        $("#modalDependentesEdit").modal('show');
        $("#bodyEdit").html(data);
        MWERP.IniciaMascaras();
      },
      error: function (error) {
        console.log(error);
      }
    });
  })

  $(document).on('click', '.editarDependentes', function () {
    var id = $(this).data("cod");
    $.ajax({
      url: GetURLBaseComplete() + '/Perfil/_partialEditDependentes/' + id,
      cache: false,
      dataType: "html",
      type: "GET",
      success: function (data) {
        $("#modalEditColaboradores").modal('show');
        $("#bodyEdit").html(data);
      },
      error: function (error) {
        console.log(error);
      }
    });
  })
}

$(document).ready(function () {
  Perfil.Init();
});