﻿@model UniContabilEntidades.Models.ClienteModel
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;

@{
  ViewData["Title"] = @Html.DisplayNameFor(model => model);
  Layout = "~/Views/Shared/_Layout.cshtml";
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();
}

<div class="table-group card">
  <div class="card-header">
    <div class="row">
      @if (ContextoUsuario.HasPermission("Cliente", TipoFuncao.Create))
      {
        <p>
          <a class="btn btn-success" asp-action="Create"><i class="mw-drip-plus"></i></a>
        </p>
      }
    </div>
  </div>
  <div class="table-group-content">
    <div class="table-responsive">
      <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
        <thead>
          <tr>
            <th> @Html.DisplayNameFor(model => model.CodigoIdent)</th>
            <th>
              Médico/Clinica
            </th>
            <th>
              Tipo
            </th>
            <th>
              @Html.DisplayNameFor(model => model.CPFCNPJ)
            </th>
            <th>
              @Html.DisplayNameFor(model => model.Nome)
            </th>
            <th>
              @Html.DisplayNameFor(model => model.Email)
            </th>
            <th>
              Tel. Fixo
            </th>
            <th>
              Celular
            </th>
            <th>
              Whatsapp?
            </th>
            <th>
              Ativo?
            </th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          @foreach (UniContabilEntidades.Models.ClienteModel item in ViewBag.PageItems)
          {
            <tr>
              <td>
                @Html.DisplayFor(modelItem => item.CodigoIdent)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.NomeOrganizacao)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.TipoCliente)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.CPFCNPJ)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.Nome)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.Email)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.TelefoneFixo)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.TelefoneCelular)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.WhatsApp)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.Ativo)
              </td>
              <td>
                @if (ContextoUsuario.HasPermission("Cliente", TipoFuncao.Edit))
                {
                 <a class="btn btn-warning btn-sm" asp-action="Edit" asp-route-id="@item.Id"><i class="mw-drip-document-edit"></i></a>
                }
                @if (ContextoUsuario.HasPermission("Cliente", TipoFuncao.Delete))
                {
                  <button class="link link-danger" onclick="DeleteItem('@this.ViewContext.RouteData.Values["controller"].ToString()', @item.Id)">Apagar</button>
                }
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>

  </div>
  <div class="card-footer">
    @Html.PagedListPager((IPagedList)ViewBag.PageItems, page => Url.Action("Index", new { page }),
      new PagedListRenderOptions
      {
        LiElementClasses = new string[] { "page-item" },
        PageClasses = new string[] { "page-link" },
        Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
        MaximumPageNumbersToDisplay = 5
      })


  </div>
</div>
