﻿@model UniContabilEntidades.Models.MovimentacaoBancariaModel
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;


@{
  ViewData["Title"] = @Html.DisplayNameFor(model => model);
  Layout = "~/Views/Shared/_Layout.cshtml";
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();
}
<div class="table-group card">
  <div class="card-header">
    <div class="row">
      @if (ContextoUsuario.HasPermission("Cliente", TipoFuncao.Create))
      {
        <p>
          <a class="btn btn-success" asp-action="Create"><i class="mw-drip-plus"></i></a>
        </p>
      }
    </div>
  </div>

  <div class="table-group-content">
    <div class="table-responsive">
      <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">

        <thead>
          <tr>
            <th>
              Médico/Clinica
            </th>
            <th>
              @Html.DisplayNameFor(model => model.NomeCentroCusto)
            </th>
            <th>
              @Html.DisplayNameFor(model => model.DataInicio)
            </th>
            <th>
              @Html.DisplayNameFor(model => model.ValorLancamento)
            </th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          @foreach (UniContabilEntidades.Models.MovimentacaoBancariaModel item in ViewBag.PageItems)
          {
            <tr>
              <td>
                @Html.DisplayFor(modelItem => item.NomeOrganizacao)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.NomeCentroCusto)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.DataInicio)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.ValorLancamento)
              </td>
              <td>
                @if (ContextoUsuario.HasPermission(controllerName, TipoFuncao.Edit))
                {
                  <a class="btn btn-secondary" asp-action="Edit" asp-route-id="@item.Id">Editar</a>
                }
                @if (ContextoUsuario.HasPermission(controllerName, TipoFuncao.Delete))
                {
                  <button class="btn btn-danger" onclick="DeleteItem('@this.ViewContext.RouteData.Values["controller"].ToString()', @item.Id)">Apagar</button>
                }
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
  <div class="card-footer">

    @Html.PagedListPager((IPagedList)ViewBag.PageItems, page => Url.Action("Index", new { page }),
      new PagedListRenderOptions
      {
        LiElementClasses = new string[] { "page-item" },
        PageClasses = new string[] { "page-link" },
        Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
        MaximumPageNumbersToDisplay = 5
      })
  </div>
</div>

