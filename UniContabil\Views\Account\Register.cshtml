﻿@using UniContabil.Infrastructure.Controls
@model UniContabilEntidades.Models.BasicRegisterModel

@{
  ViewBag.Title = "Registrar";
  Layout = "~/Views/Shared/_LayoutAccount.cshtml";
}

<link rel="stylesheet" href="~/Views/Account/Account.css?aqwa=aer" />

<div class="col-3" style="position: fixed; right: 6%; top: 20%; height: calc(85vh - 50px); max-height: calc(85vh - 50px); overflow-y: auto;">
  <div id="div-form" style="display: none">
    @using (Html.BeginForm("Register", "Account", FormMethod.Post, new { autocomplete = "off" }))
    {
    <div class="form-group">
      @Html.HiddenFor(m => m.TipoCadastro)
      @Html.LabelFor(m => m.Nome)
      @Html.TextBoxFor(m => m.Nome, new { type = "text", @class = "form-control", @autocomplete="false" })
      @Html.ValidationMessageFor(m => m.Nome)
      @Html.LabelFor(m => m.Email)
      @Html.TextBoxFor(m => m.Email, new { type = "text", @class = "EMAIL form-control", @autocomplete = "false" })
      @Html.ValidationMessageFor(m => m.Email)
      @Html.LabelFor(m => m.ConfirmarEmail)
      @Html.TextBoxFor(m => m.ConfirmarEmail, new { type = "text", @class = "EMAIL form-control", @autocomplete = "false" })
      @Html.ValidationMessageFor(m => m.ConfirmarEmail)
      @Html.LabelFor(m => m.CPF)
      @Html.TextBoxFor(m => m.CPF, new { type = "text", @class = "CPF form-control", @autocomplete = "false" })
      @Html.ValidationMessageFor(m => m.CPF)
      @Html.LabelFor(m => m.TelefoneCelular)
      @Html.TextBoxFor(m => m.TelefoneCelular, new { type = "text", @class = "telefone form-control", @autocomplete = "false" })
      @Html.ValidationMessageFor(m => m.TelefoneCelular)
      @Html.LabelFor(m => m.Senha)
      @Html.TextBoxFor(m => m.Senha, new { type = "password", @class = "form-control", @autocomplete = "false" })
      @Html.ValidationMessageFor(m => m.Senha)
      @Html.LabelFor(m => m.ConfirmarSenha)
      @Html.TextBoxFor(m => m.ConfirmarSenha, new { type = "password", @class = "form-control", @autocomplete = "false" })
      @Html.ValidationMessageFor(m => m.ConfirmarSenha)
    </div>
      <div class="row" style="justify-content: space-evenly;">
        <a href="@Url.Action("Login","Account")" class="btn btn-secondary col-5">Voltar</a>
        <button class="btn btn-primary col-5" type="submit">Cadastrar</button>
      </div>
    }
  </div>
  <div class="row" id="row-btns-options" style="justify-content: space-evenly;align-content: center;height: 80%;">
    <button class="btn btn-secondary col-5" onclick="setTipo(1)" type="button">Médico</button>
    <button class="btn btn-primary col-5" onclick="setTipo(2)" type="button">Contabilidade</button>
  </div>
</div>

<script>
  $(document).ready(function () {
    setTipo = function (tipo) {
      $("#TipoCadastro").val(tipo);
      $("#row-btns-options").hide();
      $("#div-form").show();
    }

  })
</script>

<style>
  .div-form{
    display:none;
  }

  body {
    background-image: url('../Content/img/Imagem home unicontabil.png'), url('../Content/img/Logo_Unicontabil.jpeg'), url('../Content/img/fundo.svg') !important;
    background-repeat: no-repeat !important;
    background-size: 40%, 30%, 100% !important;
    background-position-x: 15%, 95% !important;
    background-position-y: 0%, -50% !important;
  }

  .form-group {
    margin-bottom: 1rem !important;
  }

  input {
    background-color: #eef4f3 !important;
    height: calc(2.75rem + 2px) !important;
    color: #818181 !important;
    font-size: 15pt !important;
  }
  label {
    background-color: #eef4f3 !important;
  }

</style>