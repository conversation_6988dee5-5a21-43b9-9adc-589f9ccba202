﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using UniContabil.Infrastructure.Exceptions;

using UniContabilEntidades.Models;
using UniContabilDomain.Services;
using UniContabilEntidades.Models.DataBase;

namespace UniContabil.Controllers
{
  [AllowAnonymous]
  public class EstoqueController : LibController
  {
    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult EntradaEstoque()
    {
      PedidoCompraServices pedidoCompraServices = new PedidoCompraServices();
      EntradaEstoqueModel entradaEstoqueModel = new EntradaEstoqueModel();
      entradaEstoqueModel.ListaPedidoCompraEntradaEstoqueIndex = pedidoCompraServices.GetListPedidoCompraEntradaEstoqueIndex();
      return View(entradaEstoqueModel);
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.CriarEntradaEstoque)]
    public ActionResult EntradaEstoque(EntradaEstoqueModel EntradaEstoqueModel)
    {
      try
      {
        EstoqueServices estoqueServices = new EstoqueServices(ContextoUsuario.UserLogged);
        estoqueServices.MovimentoEstoqueCompraCreate(EntradaEstoqueModel);
        return RedirectToAction("EntradaEstoque");
      }
      catch (CustomException ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        return View(EntradaEstoqueModel);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        return View(EntradaEstoqueModel);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult SaidaEstoque()
    {
      PedidoVendaServices pedidoVendaServices = new PedidoVendaServices();
      SaidaEstoqueModel saidaEstoqueModel = new SaidaEstoqueModel();
      saidaEstoqueModel.ListaPedidoVendaSaidaEstoqueIndex = pedidoVendaServices.GetListPedidoVendaSaidaEstoqueIndex();
      return View(saidaEstoqueModel);
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.CriarSaidaEstoque)]
    public ActionResult SaidaEstoque(SaidaEstoqueModel saidaEstoqueModel)
    {
      try
      {
        EstoqueServices estoqueServices = new EstoqueServices();
        estoqueServices.MovimentoEstoqueVendaCreate(saidaEstoqueModel);
        return RedirectToAction("SaidaEstoque");
      }
      catch (CustomException ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        return View(saidaEstoqueModel);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        return View(saidaEstoqueModel);
      }
    }
  }
}