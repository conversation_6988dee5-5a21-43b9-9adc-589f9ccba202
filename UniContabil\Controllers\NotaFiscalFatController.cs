﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using UniContabil.Infrastructure.Exceptions;

using UniContabilEntidades.Models;
using UniContabilDomain.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace UniContabil.Controllers
{
  public class NotaFiscalFatController : LibController
  {
    private NotaFiscalFatServices NotaFiscalFatServices
    {
      get
      {
        if (_NotaFiscalFatServices == null)
          _NotaFiscalFatServices = new NotaFiscalFatServices(ContextoUsuario.UserLogged);

        return _NotaFiscalFatServices;
      }
    }

    private NotaFiscalFatServices _NotaFiscalFatServices;

    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int? page)
    {
      try
      {
        int pageNumber = page ?? 1;
        List<NotaFiscalFatIndexModel> list = new List<NotaFiscalFatIndexModel>();
        list = NotaFiscalFatServices.GetIndex(pageNumber);
        return View(list);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpGet]
    public ActionResult Create()
    {
      try
      {
        NotaFiscalFatModel NotaFiscalFatModel = new NotaFiscalFatModel();
        return View(NotaFiscalFatModel);
      }
      catch (Exception ex)
      {
        return View(new NotaFiscalFatModel());
      }
    }

    [HttpPost]
    public ActionResult Create(NotaFiscalFatModel model)
    {
      try
      {
        int id = NotaFiscalFatServices.Create(model);

        return RedirectToAction("Edit", new { id = id });
      }
      catch (Exception ex)
      {
        return View(model);
      }
    }

    [HttpGet]
    public ActionResult Edit(int id)
    {
      try
      {
        NotaFiscalFatModel NotaFiscalFatModel = NotaFiscalFatServices.GetModelById(id);

        if (NotaFiscalFatModel == null)
          return RedirectToAction("Index");

        NotaFiscalFatModel.ItensNotaFiscalFat = new ItensNotaFiscalFatVendaServices().GetAllByIdNota(id);

        return View(NotaFiscalFatModel);
      }
      catch (Exception ex)
      {
        return RedirectToAction("Index");
      }
    }

    //[HttpPost]
    //public ActionResult Edit(NotaFiscalFatModel model)
    //{
    //  try
    //  {
    //    NotaFiscalFatServices.Edit(model);
    //    MessageAdd(new Message(MessageType.Success, "Nota fiscal salva com sucesso."));
    //    return RedirectToAction("Index");
    //  }
    //  catch (Exception ex)
    //  {
    //    MessageAdd(new Message(MessageType.Success, ex.Message));
    //    return View(model);
    //  }
    //}

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.GerarNotaFiscalVenda)]
    public JsonResult GeraNotaFiscal(GeraNotaFiscal GeraNotaFiscal)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        NotaFiscalFatServices.GeraNotaFiscalByPedidoVenda(GeraNotaFiscal);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Nota fiscal gerada com sucesso.";
        retornoAjax.Erro = false;

        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
    }

  }
}
