﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using UniContabil.Infrastructure.Exceptions;

using UniContabil.Infrastructure.Hubs;
using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;
using UniContabilDomain.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Estoque.Infrastructure;

namespace UniContabil.Controllers
{
  [AllowAnonymous]
  public class PedidoVendaController : LibController
  {

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index()
    {
      try
      {
        List<PedidoVendaIndex> PedidoVendaIndex = new PedidoVendaServices().GetPedidoVendaIndex();
        return View(PedidoVendaIndex);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }

    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult CreateInicial()
    {
      int idPedidoVenda = new PedidoVendaServices(ContextoUsuario.UserLogged).CreatePedidoVenda();
      SignalHub.Current.Clients.All.SendCoreAsync("painelpedido", new object[1] { idPedidoVenda });

      return RedirectToAction("Edit", new { CodPedidoVenda = idPedidoVenda });
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Visualizar)]
    public ActionResult Edit(int CodPedidoVenda)
    {
      try
      {
        PedidoVendaModel Pedido = new PedidoVendaModel();
        Pedido = new PedidoVendaServices().GetPedidoVendaModelById(CodPedidoVenda);
        Pedido.ListItensPedido = new ItensPedidoVendaServices().GetItensByIdPedido(CodPedidoVenda);

        return View(Pedido);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(PedidoVendaModel Pedido)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new PedidoVendaServices().Edit(Pedido);
          return RedirectToAction("Index", "PedidoVenda");
        }

        List<string> ListaErro = ModelStateHelper.GetErroList(ViewData.ModelState.Values);
        MessageAdd(new Message(MessageType.Error, String.Join(" <br> ", ListaErro.ToArray())));
        Pedido.ListItensPedido = new ItensPedidoVendaServices().GetItensByIdPedido(Pedido.Codigo);
        return View(Pedido);

      }
      catch (CustomException ex)
      {
        MessageAdd(new Message(MessageType.Success, ex.Message));
        Pedido.ListItensPedido = new ItensPedidoVendaServices().GetItensByIdPedido(Pedido.Codigo);
        return View(Pedido);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Success, ex.Message));
        Pedido.ListItensPedido = new ItensPedidoVendaServices().GetItensByIdPedido(Pedido.Codigo);
        return View(Pedido);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.GridItensPedidoVenda)]
    public PartialViewResult GetGridItensPedido(int CodPedidoVenda)
    {
      try
      {
        List<ItensPedidoVendaModel> ItensPedidoVendaModel = new ItensPedidoVendaServices().GetItensByIdPedido(CodPedidoVenda);
        return PartialView("_GridItensPedidos", ItensPedidoVendaModel);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }
  }
}