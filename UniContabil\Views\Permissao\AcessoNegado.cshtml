﻿@using UniContabil.Infrastructure.Controls
@{
  ViewBag.Title = "Acesso Negado";
}

<link rel="stylesheet" href="~/Views/Permissao/Permissao.css" />

<div class="container">
  <i class="mw-drip-lock"></i>
  <p>
    <PERSON><PERSON><PERSON><PERSON>, você não está autorizado a acessar a página que solicitou. <br />
    Se você acha que isso é um engano, entre em contato com o Administrador.
  </p>
  <a href="javascript:history.back()" class="btn  btn-primary">Voltar para onde estava</a>
</div>

