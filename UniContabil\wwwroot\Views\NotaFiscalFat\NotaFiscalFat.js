﻿var NotaFiscalFat = function () {
};

NotaFiscalFat.init = function () {
  $(document).on("click", "#transmitirNota", function () {
    NotaFiscalFat.showAcaoNota("Transmitindo nota fiscal");

    const obj = {
      codigoNota: $("#Codigo").val()
    };

    $.post(GetURLBaseComplete() + "/NotaFiscalFat/TransmitirNotaFiscal", obj, function (data) {
      NotaFiscalFat.hideAcaoNota();

      if (!data.erro)
        Alerta("Sucesso", data.mensagem, "green", 5000);
      else
        Alerta("Erro", data.mensagem, "red", 8000);
    });
  });

  $(document).on("click", ".TransmitirNFSe", function () {
    NotaFiscalFat.showAcaoNota("Transmitindo nota fiscal");
    const id = $(this).data("id");
    const obj = {
      id: id
    };

    $.post(GetURLBaseComplete() + "/NotaFiscalFat/TransmitirNotaFiscalBH", obj, function (data) {
      NotaFiscalFat.hideAcaoNota();

      if (!data.erro)
        Alerta("Sucesso", data.mensagem, "green", 5000);
      else
        Alerta("Erro", data.mensagem, "red", 8000);
    });
  });
};

NotaFiscalFat.showAcaoNota = function (actionText) {
  $("#notaControls").children().hide();
  $("#acaoNota > p > text").text(actionText);
  $("#acaoNota").css("display", "flex");
};

NotaFiscalFat.hideAcaoNota = function () {
  $("#acaoNota").hide();
  $("#notaControls").children().not("#acaoNota").show();
};

$(document).ready(function () {
  NotaFiscalFat.init();
});