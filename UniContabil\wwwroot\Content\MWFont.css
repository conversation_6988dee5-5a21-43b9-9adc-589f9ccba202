@font-face {
  font-family: 'MWFONT';
  src:  url('../fonts/MWFONT.eot?j70oe8');
  src:  url('../fonts/MWFONT.eot?j70oe8#iefix') format('embedded-opentype'),
    url('../fonts/MWFONT.ttf?j70oe8') format('truetype'),
    url('../fonts/MWFONT.woff?j70oe8') format('woff'),
    url('../fonts/MWFONT.svg?j70oe8#MWFONT') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="mw-"], [class*=" mw-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'MWFONT' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.mw-AprovarConsultas:before {
  content: "\e9f5";
}
.mw-MeuPerfil:before {
  content: "\e9f1";
}
.mw-PacienteAtendimento:before {
  content: "\e9f2";
}
.mw-Saida:before {
  content: "\e9f3";
}
.mw-HistoricoPaciente:before {
  content: "\e9f4";
}
.mw-whatsapp:before {
  content: "\e9f0";
}
.mw-Camera3:before {
  content: "\e9ee";
}
.mw-facebook-circle:before {
  content: "\e9ed";
}
.mw-instagram-circle:before {
  content: "\e9ef";
}
.mw-Camera1:before {
  content: "\e9ea";
}
.mw-Camera2:before {
  content: "\e9eb";
}
.mw-CopiarColar:before {
  content: "\e9ec";
}
.mw-information:before {
  content: "\e9e9";
}
.mw-next:before {
  content: "\e9e7";
}
.mw-previous:before {
  content: "\e9e8";
}
.mw-circle-full:before {
  content: "\e9e5";
}
.mw-circle-outside:before {
  content: "\e9e6";
}
.mw-contact:before {
  content: "\e9e1";
}
.mw-medical-record:before {
  content: "\e9e2";
}
.mw-salesman:before {
  content: "\e9e3";
}
.mw-calendar:before {
  content: "\e9e4";
}
.mw-instagram:before {
  content: "\e9dd";
}
.mw-facebook:before {
  content: "\e9de";
}
.mw-app:before {
  content: "\e9df";
}
.mw-google-play:before {
  content: "\e9e0";
}
.mw-apuracao:before {
  content: "\e9dc";
}
.mw-TaxaAdministrativa:before {
  content: "\e9da";
}
.mw-TaxaBancaria:before {
  content: "\e9db";
}
.mw-Cancel:before {
  content: "\e9d8";
}
.mw-Success:before {
  content: "\e9d9";
}
.mw-money:before {
  content: "\e9d7";
}
.mw-ui:before {
  content: "\e9d6";
}
.mw-close:before {
  content: "\e9d5";
}
.mw-drip-alarm:before {
  content: "\e90d";
}
.mw-drip-align-center:before {
  content: "\e90e";
}
.mw-drip-align-justify:before {
  content: "\e90f";
}
.mw-drip-align-left:before {
  content: "\e910";
}
.mw-drip-align-right:before {
  content: "\e911";
}
.mw-drip-anchor:before {
  content: "\e912";
}
.mw-drip-archive:before {
  content: "\e913";
}
.mw-drip-arrow-down:before {
  content: "\e914";
}
.mw-drip-arrow-left:before {
  content: "\e915";
}
.mw-drip-arrow-right:before {
  content: "\e916";
}
.mw-drip-arrow-thin-down:before {
  content: "\e917";
}
.mw-drip-arrow-thin-left:before {
  content: "\e918";
}
.mw-drip-arrow-thin-right:before {
  content: "\e919";
}
.mw-drip-arrow-thin-up:before {
  content: "\e91a";
}
.mw-drip-arrow-up:before {
  content: "\e91b";
}
.mw-drip-article:before {
  content: "\e91c";
}
.mw-drip-backspace:before {
  content: "\e91d";
}
.mw-drip-basket:before {
  content: "\e91e";
}
.mw-drip-basketball:before {
  content: "\e91f";
}
.mw-drip-battery-empty:before {
  content: "\e920";
}
.mw-drip-battery-full:before {
  content: "\e921";
}
.mw-drip-battery-low:before {
  content: "\e922";
}
.mw-drip-battery-medium:before {
  content: "\e923";
}
.mw-drip-bell:before {
  content: "\e924";
}
.mw-drip-blog:before {
  content: "\e925";
}
.mw-drip-bluetooth:before {
  content: "\e926";
}
.mw-drip-bold:before {
  content: "\e927";
}
.mw-drip-bookmark:before {
  content: "\e928";
}
.mw-drip-bookmarks:before {
  content: "\e929";
}
.mw-drip-box:before {
  content: "\e92a";
}
.mw-drip-briefcase:before {
  content: "\e92b";
}
.mw-drip-brightness-low:before {
  content: "\e92c";
}
.mw-drip-brightness-max:before {
  content: "\e92d";
}
.mw-drip-brightness-medium:before {
  content: "\e92e";
}
.mw-drip-broadcast:before {
  content: "\e92f";
}
.mw-drip-browser:before {
  content: "\e930";
}
.mw-drip-browser-upload:before {
  content: "\e931";
}
.mw-drip-brush:before {
  content: "\e932";
}
.mw-drip-calendar:before {
  content: "\e933";
}
.mw-drip-camcorder:before {
  content: "\e934";
}
.mw-drip-camera:before {
  content: "\e935";
}
.mw-drip-card:before {
  content: "\e936";
}
.mw-drip-cart:before {
  content: "\e937";
}
.mw-drip-checklist:before {
  content: "\e938";
}
.mw-drip-checkmark:before {
  content: "\e939";
}
.mw-drip-chevron-down:before {
  content: "\e93a";
}
.mw-drip-chevron-left:before {
  content: "\e93b";
}
.mw-drip-chevron-right:before {
  content: "\e93c";
}
.mw-drip-chevron-up:before {
  content: "\e93d";
}
.mw-drip-clipboard:before {
  content: "\e93e";
}
.mw-drip-clock:before {
  content: "\e93f";
}
.mw-drip-clockwise:before {
  content: "\e940";
}
.mw-drip-cloud:before {
  content: "\e941";
}
.mw-drip-cloud-download:before {
  content: "\e942";
}
.mw-drip-cloud-upload:before {
  content: "\e943";
}
.mw-drip-code:before {
  content: "\e944";
}
.mw-drip-contract:before {
  content: "\e945";
}
.mw-drip-contract-2:before {
  content: "\e946";
}
.mw-drip-conversation:before {
  content: "\e947";
}
.mw-drip-copy:before {
  content: "\e948";
}
.mw-drip-crop:before {
  content: "\e949";
}
.mw-drip-cross:before {
  content: "\e94a";
}
.mw-drip-crosshair:before {
  content: "\e94b";
}
.mw-drip-cutlery:before {
  content: "\e94c";
}
.mw-drip-device-desktop:before {
  content: "\e94d";
}
.mw-drip-device-mobile:before {
  content: "\e94e";
}
.mw-drip-device-tablet:before {
  content: "\e94f";
}
.mw-drip-direction:before {
  content: "\e950";
}
.mw-drip-disc:before {
  content: "\e951";
}
.mw-drip-document:before {
  content: "\e952";
}
.mw-drip-document-delete:before {
  content: "\e953";
}
.mw-drip-document-edit:before {
  content: "\e954";
}
.mw-drip-document-new:before {
  content: "\e955";
}
.mw-drip-document-remove:before {
  content: "\e956";
}
.mw-drip-dot:before {
  content: "\e957";
}
.mw-drip-dots-2:before {
  content: "\e958";
}
.mw-drip-dots-3:before {
  content: "\e959";
}
.mw-drip-download:before {
  content: "\e95a";
}
.mw-drip-duplicate:before {
  content: "\e95b";
}
.mw-drip-enter:before {
  content: "\e95c";
}
.mw-drip-exit:before {
  content: "\e95d";
}
.mw-drip-expand:before {
  content: "\e95e";
}
.mw-drip-expand-2:before {
  content: "\e95f";
}
.mw-drip-experiment:before {
  content: "\e960";
}
.mw-drip-export:before {
  content: "\e961";
}
.mw-drip-feed:before {
  content: "\e962";
}
.mw-drip-flag:before {
  content: "\e963";
}
.mw-drip-flashlight:before {
  content: "\e964";
}
.mw-drip-folder:before {
  content: "\e965";
}
.mw-drip-folder-open:before {
  content: "\e966";
}
.mw-drip-forward:before {
  content: "\e967";
}
.mw-drip-gaming:before {
  content: "\e968";
}
.mw-drip-gear:before {
  content: "\e969";
}
.mw-drip-graduation:before {
  content: "\e96a";
}
.mw-drip-graph-bar:before {
  content: "\e96b";
}
.mw-drip-graph-line:before {
  content: "\e96c";
}
.mw-drip-graph-pie:before {
  content: "\e96d";
}
.mw-drip-headset:before {
  content: "\e96e";
}
.mw-drip-heart:before {
  content: "\e96f";
}
.mw-drip-help:before {
  content: "\e970";
}
.mw-drip-home:before {
  content: "\e971";
}
.mw-drip-hourglass:before {
  content: "\e972";
}
.mw-drip-inbox:before {
  content: "\e973";
}
.mw-drip-information:before {
  content: "\e974";
}
.mw-drip-italic:before {
  content: "\e975";
}
.mw-drip-jewel:before {
  content: "\e976";
}
.mw-drip-lifting:before {
  content: "\e977";
}
.mw-drip-lightbulb:before {
  content: "\e978";
}
.mw-drip-link:before {
  content: "\e979";
}
.mw-drip-link-broken:before {
  content: "\e97a";
}
.mw-drip-list:before {
  content: "\e97b";
}
.mw-drip-loading:before {
  content: "\e97c";
}
.mw-drip-location:before {
  content: "\e97d";
}
.mw-drip-lock:before {
  content: "\e97e";
}
.mw-drip-lock-open:before {
  content: "\e97f";
}
.mw-drip-mail:before {
  content: "\e980";
}
.mw-drip-map:before {
  content: "\e981";
}
.mw-drip-media-loop:before {
  content: "\e982";
}
.mw-drip-media-next:before {
  content: "\e983";
}
.mw-drip-media-pause:before {
  content: "\e984";
}
.mw-drip-media-play:before {
  content: "\e985";
}
.mw-drip-media-previous:before {
  content: "\e986";
}
.mw-drip-media-record:before {
  content: "\e987";
}
.mw-drip-media-shuffle:before {
  content: "\e988";
}
.mw-drip-media-stop:before {
  content: "\e989";
}
.mw-drip-medical:before {
  content: "\e98a";
}
.mw-drip-menu:before {
  content: "\e98b";
}
.mw-drip-message:before {
  content: "\e98c";
}
.mw-drip-meter:before {
  content: "\e98d";
}
.mw-drip-microphone:before {
  content: "\e98e";
}
.mw-drip-minus:before {
  content: "\e98f";
}
.mw-drip-monitor:before {
  content: "\e990";
}
.mw-drip-move:before {
  content: "\e991";
}
.mw-drip-music:before {
  content: "\e992";
}
.mw-drip-network-1:before {
  content: "\e993";
}
.mw-drip-network-2:before {
  content: "\e994";
}
.mw-drip-network-3:before {
  content: "\e995";
}
.mw-drip-network-4:before {
  content: "\e996";
}
.mw-drip-network-5:before {
  content: "\e997";
}
.mw-drip-pamphlet:before {
  content: "\e998";
}
.mw-drip-paperclip:before {
  content: "\e999";
}
.mw-drip-pencil:before {
  content: "\e99a";
}
.mw-drip-phone:before {
  content: "\e99b";
}
.mw-drip-photo:before {
  content: "\e99c";
}
.mw-drip-photo-group:before {
  content: "\e99d";
}
.mw-drip-pill:before {
  content: "\e99e";
}
.mw-drip-pin:before {
  content: "\e99f";
}
.mw-drip-plus:before {
  content: "\e9a0";
}
.mw-drip-power:before {
  content: "\e9a1";
}
.mw-drip-preview:before {
  content: "\e9a2";
}
.mw-drip-print:before {
  content: "\e9a3";
}
.mw-drip-pulse:before {
  content: "\e9a4";
}
.mw-drip-question:before {
  content: "\e9a5";
}
.mw-drip-reply:before {
  content: "\e9a6";
}
.mw-drip-reply-all:before {
  content: "\e9a7";
}
.mw-drip-return:before {
  content: "\e9a8";
}
.mw-drip-retweet:before {
  content: "\e9a9";
}
.mw-drip-rocket:before {
  content: "\e9aa";
}
.mw-drip-scale:before {
  content: "\e9ab";
}
.mw-drip-search:before {
  content: "\e9ac";
}
.mw-drip-shopping-bag:before {
  content: "\e9ad";
}
.mw-drip-skip:before {
  content: "\e9ae";
}
.mw-drip-stack:before {
  content: "\e9af";
}
.mw-drip-star:before {
  content: "\e9b0";
}
.mw-drip-stopwatch:before {
  content: "\e9b1";
}
.mw-drip-store:before {
  content: "\e9b2";
}
.mw-drip-suitcase:before {
  content: "\e9b3";
}
.mw-drip-swap:before {
  content: "\e9b4";
}
.mw-drip-tag:before {
  content: "\e9b5";
}
.mw-drip-tag-delete:before {
  content: "\e9b6";
}
.mw-drip-tags:before {
  content: "\e9b7";
}
.mw-drip-thumbs-down:before {
  content: "\e9b8";
}
.mw-drip-thumbs-up:before {
  content: "\e9b9";
}
.mw-drip-ticket:before {
  content: "\e9ba";
}
.mw-drip-time-reverse:before {
  content: "\e9bb";
}
.mw-drip-to-do:before {
  content: "\e9bc";
}
.mw-drip-toggles:before {
  content: "\e9bd";
}
.mw-drip-trash:before {
  content: "\e9be";
}
.mw-drip-trophy:before {
  content: "\e9bf";
}
.mw-drip-upload:before {
  content: "\e9c0";
}
.mw-drip-user:before {
  content: "\e9c1";
}
.mw-drip-user-group:before {
  content: "\e9c2";
}
.mw-drip-user-id:before {
  content: "\e9c3";
}
.mw-drip-vibrate:before {
  content: "\e9c4";
}
.mw-drip-view-apps:before {
  content: "\e9c5";
}
.mw-drip-view-list:before {
  content: "\e9c6";
}
.mw-drip-view-list-large:before {
  content: "\e9c7";
}
.mw-drip-view-thumb:before {
  content: "\e9c8";
}
.mw-drip-volume-full:before {
  content: "\e9c9";
}
.mw-drip-volume-low:before {
  content: "\e9ca";
}
.mw-drip-volume-medium:before {
  content: "\e9cb";
}
.mw-drip-volume-off:before {
  content: "\e9cc";
}
.mw-drip-wallet:before {
  content: "\e9cd";
}
.mw-drip-warning:before {
  content: "\e9ce";
}
.mw-drip-web:before {
  content: "\e9cf";
}
.mw-drip-weight:before {
  content: "\e9d0";
}
.mw-drip-wifi:before {
  content: "\e9d1";
}
.mw-drip-wrong:before {
  content: "\e9d2";
}
.mw-drip-zoom-in:before {
  content: "\e9d3";
}
.mw-drip-zoom-out:before {
  content: "\e9d4";
}
.mw-continuo:before {
  content: "\e90b";
}
.mw-intermitente:before {
  content: "\e90c";
}
.mw-xls:before {
  content: "\e90a";
}
.mw-pdf:before {
  content: "\e909";
}
.mw-json:before {
  content: "\e907";
}
.mw-zip-file-format:before {
  content: "\e908";
}
.mw-zipper-tool:before {
  content: "\e906";
}
.mw-Download:before {
  content: "\e901";
}
.mw-zip-file:before {
  content: "\e905";
}
.mw-log:before {
  content: "\e904";
}
.mw-attachment:before {
  content: "\e903";
}
.mw-authorization:before {
  content: "\e902";
}
.mw-Chat-Ballon:before {
  content: "\1f327";
}
.mw-Chat-Peaple:before {
  content: "\e900";
}
