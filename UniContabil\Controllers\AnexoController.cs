﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniContabil.Infrastructure;
using UniContabilDomain.Services;
using Microsoft.AspNetCore.Mvc;
using System.Configuration;
using UniContabilEntidades.Models.DataBase;
using Microsoft.Extensions.Configuration;

namespace UniContabil.Controllers
{
  public class AnexoController : LibController
  {
    public AnexoController(IConfiguration configuration)
    {
      _configuration = configuration;
    }

    private IConfiguration _configuration { get; }
    public FileContentResult ShowGuiaTeste()
    {
      try
      {
        AnexosServices anexosServices = new AnexosServices();
        //string NomeArquivo = "GuiaTeste.pdf";
        return new FileContentResult(anexosServices.GerarGuiaTeste(), "application/pdf");
      }
      catch
      {
        return new FileContentResult(new byte[0], "application/pdf");
      }
    }


    public JsonResult GetImageLancamento(int id)
    {
      E_Lancamentos lancamentos = new LancamentosServices().GetById(id).ToDatabase();
      return Json(new { path = new AnexosServices().GetImageLancamento(lancamentos, _configuration["CaminhoDocs"]), Extensao = lancamentos.L_Extensao });
    }


    public FileContentResult GetAnexo(int id)
    {
      try
      {
        E_Lancamentos lancamentos = new LancamentosServices().GetById(id).ToDatabase();
        return new FileContentResult(new AnexosServices().GetPdfLancamento(lancamentos, _configuration["CaminhoDocs"]), "image/jpg");
      }
      catch (Exception ex)
      {
        return new FileContentResult(new byte[0], "application/pdf");
      }
    }

    [HttpGet]
    public FileContentResult Download(string id)
    {
      try
      {
        string path = _configuration["ReportPath"];

        byte[] bt = System.IO.File.ReadAllBytes(path + id);

        return new FileContentResult(bt, "application/pdf");

      }
      catch (Exception ex)
      {
        return new FileContentResult(new byte[0], "application/pdf");
      }
    }

    public FileContentResult GetPDFLancamento(int id)
    {
      try
      {
        E_Lancamentos lancamentos = new LancamentosServices().GetById(id).ToDatabase();
        return new FileContentResult(new AnexosServices().GetPdfLancamento(lancamentos, _configuration["CaminhoDocs"]), "application/pdf");
      }
      catch(Exception ex)
      {
        return new FileContentResult(new byte[0], "application/pdf");
      }
    }
  }
}
