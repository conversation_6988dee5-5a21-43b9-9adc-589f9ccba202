﻿@model PaginaMenuModel
@using System.Configuration
@using UniContabilEntidades.Models.DataBase

<div class="table-group card">
  <div class="card-header">
    <div class="card-title">Menu @Model.Nome</div>
    <div class="card-options">
    </div>
  </div>
  <div style="padding: 15px">
    <div class="form-group form-inline justify-content-between" id="menu-active" data-menu="@Model.Id">
      <div style="display: flex; align-items: center;">
        @Html.LabelFor(a => a.IsVisible)
        @Html.CheckBoxFor(a => a.IsVisible, new { @style = "margin-left: 5pt;" })
      </div>
      <div style=" display: flex" id="pai-select">
        <label>Submenu de </label>
        <select class="custom-select" id="menu-pai" style="margin-left: 10px;">
          <option value="0">Nenhum</option>
          @foreach (Select2Model option in Model.OpcoesMenus)
          {
            if (Model.IdPai.HasValue && Model.IdPai.Value.ToString() == option.id)
            {
              @:<option value="@(option.id)" selected>@(option.text)</option>
            }
            else
            {
              @:<option value="@(option.id)">@(option.text)</option>
            }
          }
        </select>
      </div>
      <div style="@(!Model.IdPai.HasValue ? "display: flex" : "display: none")" id="modulo-select">
        <label>Módulo</label>
        <select class="custom-select" id="menu-modulo" style="margin-left: 10px;">
          <option value="0">Nenhum</option>
          @foreach (Select2Model option in Model.OpcoesModulos)
          {
            if (Model.IdModulo.HasValue && Model.IdModulo.Value.ToString() == option.id)
            {
              @:<option value="@(option.id)" selected>@(option.text)</option>
            }
            else
            {
              @:<option value="@(option.id)">@(option.text)</option>
            }
          }
        </select>
      </div>
    </div>
    <div class="input-group mb-2 form-group">
      <div class="input-group-prepend">
        <div class="input-group-text">@ConfigurationManager.AppSettings["URLBase"]</div>
      </div>
      <input class="form-control" id="menu-id" value="@(Model.URL)" placeholder="Link do menu">
    </div>
    <button class="btn btn-outline-primary btn-sm" style="margin-bottom: .5rem" type="button" data-toggle="modal" data-target="#modalNovaFuncao">+ Nova Função</button>
    <div class="table-group-content">
      <table id="functions-table">
        <thead>
          <tr>
            <th>
              Id
            </th>
            <th>
              Função
            </th>
            <th>
            </th>
          </tr>
        </thead>
        <tbody>
          @foreach (E_MenuFuncao funcao in Model.Funcoes)
          {
            <tr data-funcao="@funcao.MF_Id">
              <td>
                @funcao.MF_Codigo
              </td>
              <td>
                @funcao.MF_Nome
              </td>
              <td>
                <button class="btn btn-outline-danger btn-sm deletarFuncao" type="button">Excluir</button>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
  <div class="card-footer">
    <button class="btn btn-primary" style="margin-top: .5rem" id="salvarEdicaoMenu" type="button">Salvar</button>
  </div>
</div>

<script>
  $(document).ready(function () {
    $(".deletarFuncao").on("click", function () {
      const idFuncao = parseInt($(this).parent().parent().data("funcao"));

      $.post(GetURLBaseComplete() + "/Menu/DeleteFuncao/" + idFuncao, function (data) {
        data = JSON.parse(data);
        if (data.Sucesso) {
          $("#functions-table tbody tr[data-funcao=" + idFuncao + "]").remove();
          Alerta("Sucesso", "Função excluída com sucesso.", "green", 5000);
        }
        else
          Alerta("Erro", data.Mensagem, "red", 5000);
      });
    });

    $("#menu-pai").on("change", function () {
      let idPai = $(this).val();

      if (idPai != "0")
        $("#modulo-select").hide();
      else
        $("#modulo-select").css("display", "flex");
    });

    $("#salvarEdicaoMenu").click(function () {
      let idPai = $("#menu-pai").val();
      let idModulo = $("#menu-modulo").val();

      if (idPai === "0") {
        idPai = null;
      }
      if (idModulo === "0") {
        idModulo = null;
      }

      let obj = {
        Id: @Model.Id,
        URL: $("#menu-id").val(),
        IdMenuPai: idPai,
        IdModulo: idModulo,
        IsVisible: $("#IsVisible").is(":checked")
      };

      $.post(GetURLBaseComplete() + "/Menu/EditDetalhes", obj, function (data) {
        data = JSON.parse(data);
        if (data.Sucesso) {
          Alerta("Sucesso", "Dados alterados com sucesso.", "green", 5000);
          reloadTree(false);
        }
        else
          Alerta("Erro", data.Mensagem, "red", 5000);
      });
    });
  });
</script>
