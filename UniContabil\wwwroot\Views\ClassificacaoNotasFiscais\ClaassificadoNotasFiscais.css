﻿.classif .btn {
  color: black;
  border-radius: 8px;
  border-color: #456b8e !important;
  font-size: 9pt !important;
  height: 22px !important;
  padding-top: 0px !important;
}

.classif :hover {
  background-color: #456b8e;
  color: white;
}

.selecionado .btn {
  background-color: #456b8e !important;
  color: white !important;
}

.txtAno {
  color: #456b8e;
  font-size: 9pt !important;
  margin: 0 !important;
  float: left;
  margin-bottom: -3px !important;
  margin-left: 5px !important;
}

#ano {
  border-radius: 10px;
  font-size: 9pt !important;
  font-weight: bold !important;
  padding-bottom: 0px !important;
  height: 23px !important;
}

.Docs {
  width: 100%;
}

.divTable {
}

#resizebleDiv {
  border-top: 0px #d1ebff solid;
  overflow: auto;
  min-height: 12vh;
  min-width: 100%;
  max-width: 100%;
  height: 12vh;
  margin-bottom: 10px !important;
}

hr {
  background-color: #597384;
  height: 2px;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/*h5 {
  margin: 0;
  border-bottom: 1px #d1ebff solid;
}*/

.table-striped tbody tr:nth-of-type(odd) {
  background-color: #e3e3e3 !important;
}

.table-hover tbody tr :hover {
  background-color: #97989a !important;
  color: white;
}

.table thead th {
  border-bottom: 0 !important;
  border-top: 0 !important;
  font-size: 9pt !important;
}
p {
  margin: 0 !important;
}

td {
  padding: 0 !important;
  padding-left: 5px !important;
  border: 1px solid black;
  font-size: 9pt !important;
}

.subtitle {
  text-align: left;
  background-color: #456b8e !important;
  border-radius: 10px;
  color: white;
  margin-bottom: 10px;
  margin-top: 10px;
}

  .subtitle label {
    font-size: 9pt !important;
    margin: 0 !important;
    padding: 3px;
  }

.jstree-default .jstree-anchor {
  font-size: 10pt !important;
}

