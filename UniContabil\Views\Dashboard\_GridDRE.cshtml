﻿@using UniContabilEntidades.Models
@using System.Globalization
@model List<DashboardGridDRE>
<div class="col-md-12">
  <div class="row">
    <div class="col-md-12">
      <div class="row" style="float:right;">
        <a class="btn btn-info" id="atualiza_dre">Atualizar</a>
      </div>
    </div>
    <table class="table table-borderless table-sm">
      <thead>
        <tr>
          <th>Ano @(DateTime.Now.ToString("yyyy"))</th>
          <th>Jan</th>
          <th>Fev</th>
          <th>Mar</th>
          <th>Abr</th>
          <th>Mai</th>
          <th>Jun</th>
          <th>Jul</th>
          <th>Ago</th>
          <th>Set</th>
          <th>Out</th>
          <th>Nov</th>
          <th>Dez</th>
          <th>Total</th>
        </tr>
      </thead>
      <tbody>
        @foreach (var tipo in Model.OrderBy(a => a.TipoDRE.Ordem))
        {
          <tr class="trtitle rwhide" data-ordem="@tipo.TipoDRE.Ordem" data-show="0" style="cursor: pointer;">

            @if (tipo.TipoDRE.Enum != 4 && tipo.TipoDRE.Enum != 5)
            {
              <td class="tdtitle" id="<EMAIL>" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">
                <span class="colspanplus" id="<EMAIL>" style="margin: 0 !important;display: inline-block;font-size: 12pt;padding: 0px 2px 0px 2px;float: left;">
                  +
                </span>
                <span class="colspanminus colsp" id="<EMAIL>" style="margin: 0 !important;display: inline-block;font-size: 12pt;padding: 0px 2px 0px 2px;float: left;">
                  -
                </span>
                @(tipo.TipoDRE.Ordem + "." + tipo.TipoDRE.Descricao)
              </td>
            }
            else
            {
              <td class="tdtitle" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">
                @tipo.TipoDRE.Descricao
              </td>
            }

            <td class="tdtitle" id="<EMAIL>" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">@(tipo.TotalMes1.HasValue && tipo.TotalMes1 != 0 ? String.Format(CultureInfo.GetCultureInfo("pt-BR"),"{0:C2}", tipo.TotalMes1.Value) : "-")</td>
            <td class="tdtitle" id="<EMAIL>" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">@(tipo.TotalMes2.HasValue && tipo.TotalMes2 != 0 ? String.Format(CultureInfo.GetCultureInfo("pt-BR"),"{0:C2}", tipo.TotalMes2.Value) : "-")</td>
            <td class="tdtitle" id="<EMAIL>" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">@(tipo.TotalMes3.HasValue && tipo.TotalMes3 != 0 ? String.Format(CultureInfo.GetCultureInfo("pt-BR"),"{0:C2}", tipo.TotalMes3.Value) : "-")</td>
            <td class="tdtitle" id="<EMAIL>" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">@(tipo.TotalMes4.HasValue && tipo.TotalMes4 != 0 ? String.Format(CultureInfo.GetCultureInfo("pt-BR"),"{0:C2}", tipo.TotalMes4.Value) : "-")</td>
            <td class="tdtitle" id="<EMAIL>" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">@(tipo.TotalMes5.HasValue && tipo.TotalMes5 != 0 ? String.Format(CultureInfo.GetCultureInfo("pt-BR"),"{0:C2}", tipo.TotalMes5.Value) : "-")</td>
            <td class="tdtitle" id="<EMAIL>" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">@(tipo.TotalMes6.HasValue && tipo.TotalMes6 != 0 ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", tipo.TotalMes6.Value) : "-")</td>
            <td class="tdtitle" id="<EMAIL>" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">@(tipo.TotalMes7.HasValue && tipo.TotalMes7 != 0 ? String.Format(CultureInfo.GetCultureInfo("pt-BR"),"{0:C2}", tipo.TotalMes7.Value) : "-")</td>
            <td class="tdtitle" id="<EMAIL>" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">@(tipo.TotalMes8.HasValue && tipo.TotalMes8 != 0 ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", tipo.TotalMes8.Value): "-")</td>
            <td class="tdtitle" id="<EMAIL>" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">@(tipo.TotalMes9.HasValue && tipo.TotalMes9 != 0 ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", tipo.TotalMes9.Value): "-")</td>
            <td class="tdtitle" id="<EMAIL>" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">@(tipo.TotalMes10.HasValue && tipo.TotalMes10 != 0 ? String.Format(CultureInfo.GetCultureInfo("pt-BR"),"{0:C2}", tipo.TotalMes10.Value) : "-")</td>
            <td class="tdtitle" id="<EMAIL>" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">@(tipo.TotalMes11.HasValue && tipo.TotalMes11 != 0 ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", tipo.TotalMes11.Value) : "-")</td>
            <td class="tdtitle" id="<EMAIL>" data-ordem="@tipo.TipoDRE.Ordem" data-show="0">@(tipo.TotalMes12.HasValue && tipo.TotalMes12 != 0 ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", tipo.TotalMes12.Value) : "-")</td>
            <td class="tdtotal">

              @((tipo.TotalMes1
            + tipo.TotalMes2
            + tipo.TotalMes3
            + tipo.TotalMes4
            + tipo.TotalMes5
            + tipo.TotalMes6
            + tipo.TotalMes7
            + tipo.TotalMes8
            + tipo.TotalMes9
            + tipo.TotalMes10
            + tipo.TotalMes11
            + tipo.TotalMes12) == 0 ? "-"
              : string.Format(CultureInfo.GetCultureInfo("pt-BR"),"{0:C2}", (tipo.TotalMes1
              + tipo.TotalMes2
              + tipo.TotalMes3
              + tipo.TotalMes4
              + tipo.TotalMes5
              + tipo.TotalMes6
              + tipo.TotalMes7
              + tipo.TotalMes8
              + tipo.TotalMes9
              + tipo.TotalMes10
              + tipo.TotalMes11
              + tipo.TotalMes12))
            )
            </td>
          </tr>

          @if (tipo.TipoDRE.Enum != 4 && tipo.TipoDRE.Enum != 5)
          {
            @foreach (var item in tipo.ListdashbordDRE.Where(a => a.IdTipo == tipo.TipoDRE.Id).OrderBy(a => a.DescricaoDRE))
            {
              <tr class="<EMAIL>">
                <td>@(item.DescricaoDRE)</td>
                <td>@(item.Mes1.HasValue ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", item.Mes1.Value) : "-" )</td>
                <td>@(item.Mes2.HasValue ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", item.Mes2.Value) : "-" )</td>
                <td>@(item.Mes3.HasValue ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", item.Mes3.Value) : "-" )</td>
                <td>@(item.Mes4.HasValue ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", item.Mes4.Value) : "-" )</td>
                <td>@(item.Mes5.HasValue ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", item.Mes5.Value) : "-" )</td>
                <td>@(item.Mes6.HasValue ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", item.Mes6.Value) : "-" )</td>
                <td>@(item.Mes7.HasValue ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", item.Mes7.Value) : "-" )</td>
                <td>@(item.Mes8.HasValue ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", item.Mes8.Value) : "-" )</td>
                <td>@(item.Mes9.HasValue ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", item.Mes9.Value) : "-" )</td>
                <td>@(item.Mes10.HasValue ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", item.Mes10.Value) : "-" )</td>
                <td>@(item.Mes11.HasValue ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", item.Mes11.Value) : "-" )</td>
                <td>@(item.Mes12.HasValue ? String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", item.Mes12.Value) : "-" )</td>
                <td class="tdtotal <EMAIL> ">
                  @(String.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C2}", item.TotalMeses))
                </td>
              </tr>
            }
          }

        }
      </tbody>
    </table>
  </div>
</div>
<style>
  td, th {
    text-align: center;
    padding: 1%;
    font-size: 9.0pt;
  }

  td {
    padding-top: 1px !important;
    padding-right: 1px !important;
    padding-left: 1px !important;
    color: black !important;
    font-size: 11.0pt;
    font-weight: 400;
    font-style: normal;
    text-decoration: none;
    font-family: Calibri, sans-serif;
    text-align: right;
    vertical-align: bottom !important;
    border: none;
    white-space: nowrap;
  }

    td:first-child {
      text-align: left !important;
    }

  th {
    background: #EDEDED;
    text-align: right;
  }

    th:first-child {
      text-align: left !important;
    }

  .tdtitle {
    font-size: 9.0pt;
    font-weight: 700;
    text-decoration: underline;
    background: #C5DDDD;
    white-space: normal;
  }

  .tdtotal {
    font-size: 9.0pt;
    font-weight: 700;
    text-decoration: underline;
    background: #D6DCE4;
  }

  .colsp {
    display: none !important;
  }
</style>

<script>
  $(document).ready(function () {
    for (var i = 0; i <= 5; i++) {
      $(".0_" + i).hide();
    }
  });

</script>