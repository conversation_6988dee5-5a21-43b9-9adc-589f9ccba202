﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using UniContabil.Infrastructure.Exceptions;
using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;
using UniContabilDomain.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace UniContabil.Controllers
{
  public class MenuController : Controller
  {
    [HttpGet]
    public string GetMenus()
    {
      try
      {
        List<TreeviewModel> Treeviews = new MenuServices().GetMenus();
        return JsonConvert.SerializeObject(new { Sucesso = true, Menus = Treeviews });
      }
      catch
      {
        return JsonConvert.SerializeObject(new { Sucesso = false });
      }
    }

    [HttpGet]
    public ActionResult Index()
    {
      return View();
    }

    [HttpGet]
    public ActionResult GetDetails(int Id)
    {
      MenuServices Services = new MenuServices();
      E_Menu Menu = Services.GetById(Id);

      if (Menu == null)
        return null;

      List<Select2Model> Menus = Services.GetMenuSelect();
      List<Select2Model> Modulos = new ModuloServices().GetByTerm(null);
      List<E_MenuFuncao> Funcoes = new MenuFuncaoServices().GetByIdMenu(Id);

      PaginaMenuModel Model = new PaginaMenuModel()
      {
        Id = Menu.M_Id,
        Nome = Menu.M_NomeFantasia,
        IdPai = Menu.M_MenuPai,
        IdModulo = Menu.M_IdModulo,
        URL = Menu.M_URL,
        OpcoesMenus = Menus,
        OpcoesModulos = Modulos,
        Funcoes = Funcoes,
        IsVisible = Menu.M_IsVisible
      };

      return PartialView("_MenuDetails", Model);
    }

    [HttpPost]
    public string CreateMenu(NovoMenuModel Model)
    {
      try
      {
        E_Menu Menu = new E_Menu()
        {
          M_NomeFantasia = Model.Nome,
          M_SempreAberto = false,
          M_Agrupador = Model.Agrupador,
          M_IsVisible = Model.isVisible
        };

        new MenuServices().Create(Menu);

        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    public string CreateModulo(NovoModuloModel Model)
    {
      try
      {
        E_Modulo Modulo = new E_Modulo()
        {
          M_Nome = Model.Nome
        };

        new MenuServices().Create(Modulo);

        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    public string CreateSubmenu(NovoMenuModel Model)
    {
      try
      {
        MenuServices Services = new MenuServices();
        int? IdReferencia = int.Parse(Model.MenuReferencia.Replace("Agrupador", "").Replace("Menu", ""));

        if (Model.MenuReferencia.Contains("Menu"))
          IdReferencia = Services.GetById(IdReferencia.Value).M_MenuPai;

        E_Menu Menu = new E_Menu()
        {
          M_NomeFantasia = Model.Nome,
          M_SempreAberto = false,
          M_Agrupador = false,
          M_MenuPai = IdReferencia
        };

        Services.Create(Menu);

        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    public string CreateFuncao(NovaFuncaoModel Model)
    {
      try
      {
        E_Menu Menu = new MenuServices().GetById(Model.IdMenu);

        if (Menu == null)
          return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = "Menu não encontrado. Por favor atualize a página." });

        E_MenuFuncao Funcao = new E_MenuFuncao()
        {
          MF_Codigo = Model.Codigo,
          MF_Nome = Model.Nome,
          MF_IdMenu = Model.IdMenu
        };

        new MenuFuncaoServices().Create(Funcao);

        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    public string DeleteFuncao(int Id)
    {
      try
      {
        MenuFuncaoServices Services = new MenuFuncaoServices();
        Services.Delete(Id);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch(CustomException ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = ex.Message });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    public string EditNome(EditNomeModel Model)
    {
      try
      {
        MenuServices Services = new MenuServices();

        E_Menu Menu = Services.GetById(Model.Id);

        if (Menu == null)
          return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = "Menu não encontrado. Por favor atualize a página." });

        Menu.M_NomeFantasia = Model.Nome;

        Services.Edit(Menu);

        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    public string EditDetalhes(EditDetailsModel Model)
    {
      try
      {
        if (Model.URL == null)
          Model.URL = "";

        MenuServices Services = new MenuServices();

        E_Menu Menu = Services.GetById(Model.Id);

        if (Menu == null)
          return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = "Menu não encontrado. Por favor atualize a página." });
        if (Model.URL.Contains("http"))
          return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = "Favor inserir apenas o caminho final da URL antes de tentar salvar." });

        if (Model.IdMenuPai.HasValue && Model.IdModulo.HasValue)
          Model.IdModulo = null;

        Menu.M_MenuPai = Model.IdMenuPai;
        Menu.M_IdModulo = Model.IdModulo;
        Menu.M_URL = Model.URL;
        Menu.M_IsVisible = Model.IsVisible;

        Services.Edit(Menu);

        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    public string Delete(int Id)
    {
      try
      {
        MenuServices Services = new MenuServices();

        E_Menu Menu = Services.GetById(Id);

        if (Menu == null)
          return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = "Menu não encontrado. Por favor atualize a página." });

        if (Menu.M_Agrupador)
        {
          List<E_Menu> Menus = Services.GetMenusByAgrupador(Id);

          if (Menus.Count > 0)
            return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = "Não é possível excluir menus com submenus cadastrados." });
          else
            Services.Delete(Menu);
        }

        else if (Menu.E_MenuFuncao.Count > 0)
          return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = "Não é possível excluir o menu com funções cadastrados." });

        else
          Services.Delete(Menu);

        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }
  }
}
