﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@using X.PagedList;

@model IPagedList<UsuarioIndex>
@{
  ViewBag.Title = "Usuários";
}

<div class=" table-group card">
  <div class="card-header">
    <div class="card-title">@Html.DisplayNameFor(model => model)</div>
    <div class="card-options">
    </div>
    <div class="col-md-12">
      @if (ContextoUsuario.UserLogged.Permissoes.Where(a => a.NomeGrupo.Equals("Secretária")).Count() == 0)
      {
        <a class="btn btn-info" href="@Url.Action("CreateSecretaria", "Usuarios")">Novo</a>
      }
    </div>
  </div>
  <div class="table-group-content">
    <table>
      <thead>
        <tr>
          <th scope="col">
            Nome
          </th>
          <th scope="col">
            CPF
          </th>
          <th scope="col">
            Email
          </th>
          <th scope="col">
            Telefone
          </th>
          <th scope="col">
          </th>
        </tr>
      </thead>
      <tbody>
        @foreach (UsuarioIndex user in Model)
        {
          <tr>
            <td>
              @user.NomeUsuario
            </td>
            <td>
              @user.CPFUsuario
            </td>
            <td>
              @user.EmailUsuario
            </td>
            <td>
              @user.Telefone
            </td>
            <td>
              <button class="link link-primary" onclick="location.href='@Url.Action("EditSecretaria", "Usuarios", new { coduser = user.CodigoUsuario })'">Editar</button>
            </td>
          </tr>
        }

      </tbody>


    </table>
  </div>

</div>