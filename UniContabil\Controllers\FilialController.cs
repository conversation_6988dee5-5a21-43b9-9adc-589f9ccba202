﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;
using UniContabil.Infrastructure.Exceptions;
using System.Transactions;
using System.Configuration;

namespace UniContabil.Controllers
{
  public class FilialController : LibController
  {
    public FilialController()
    { }

    private ControlePermissaoUserServices ControlePermissaoUserServices
    {
      get
      {
        if (_ControlePermissaoUserServices == null)
          _ControlePermissaoUserServices = new ControlePermissaoUserServices(ContextoUsuario.UserLogged);

        return _ControlePermissaoUserServices;
      }
    }
    private ControlePermissaoUserServices _ControlePermissaoUserServices;

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {

        IPagedList<FilialModel> List = new FilialServices().GetPagedListCA(page, ContextoUsuario.UserLogged.IdUsuario);
        ViewBag.PageItems = List;
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(FilialModel filialModel)
    {
      try
      {
        filialModel.Ativo = true;
        filialModel.TelWhatsApp = false;

        if (ModelState.IsValid)
        {
          if (filialModel.TipoPessoa.Contains("PJ"))
          {
            if (!Infrastructure.Helpers.ValidadorHelper.ValidaCNPJ(filialModel.CPFCNPJ))
              throw new Exception("CNPJ Inválido favor verificar.");

            if (String.IsNullOrEmpty(filialModel.TelefoneCelular))
              throw new Exception("Telefone Celular é obrigatório.");

            //if (string.IsNullOrEmpty(filialModel.InscricaoEstadual))
            //  throw new Exception("O campo inscrição estadual é obrigatório para PJ");

            if (string.IsNullOrEmpty(filialModel.InscricaoMunicipal))
              throw new Exception("O campo inscrição municipal é obrigatório para PJ");

            if (string.IsNullOrEmpty(filialModel.CodigoTributacaoMunicipio))
              throw new Exception("O campo de tributação minicipio é obrigatório para PJ");

            if (filialModel.IdRegimeEspecialTributacao == null)
              throw new Exception("O campo de regime especial tributação é obrigatório para PJ");
          }
          else
          {
            Infrastructure.Helpers.ValidadorHelper.ValidaCPF(filialModel.CPFCNPJ);
          }

          OrganizacaoModel modelOrganiza = new OrganizacaoModel()
          {
            RazaoSocial = filialModel.Nome,
            Ativo = true,
          };

          OrganizacaoServices organizacaoServices = new OrganizacaoServices(ContextoUsuario.UserLogged);
          organizacaoServices.Create(modelOrganiza, filialModel);

          return RedirectToAction(nameof(Index));
        }
        else
        {
          return View(filialModel);
        }
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(filialModel);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(int id)
    {
      try
      {
        FilialModel filialModel = new FilialModel().FromDatabase(new FilialServices().GetById(id));

        if (filialModel == null)
        {
          return NotFound();
        }

        return View(filialModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(FilialModel filialModel)
    {
      try
      {
        filialModel.Ativo = true;
        filialModel.TelWhatsApp = false;

        if (ModelState.IsValid)
        {
          if (filialModel.TipoPessoa.Contains("PJ"))
          {
            if (!Infrastructure.Helpers.ValidadorHelper.ValidaCNPJ(filialModel.CPFCNPJ))
              throw new Exception("CNPJ Inválido favor verificar.");

            //if (string.IsNullOrEmpty(filialModel.InscricaoEstadual))
            //  throw new Exception("O campo inscrição estadual é obrigatório para PJ");

            if (string.IsNullOrEmpty(filialModel.InscricaoMunicipal))
              throw new Exception("O campo inscrição municipal é obrigatório para PJ");

            if (string.IsNullOrEmpty(filialModel.CodigoTributacaoMunicipio))
              throw new Exception("O campo de tributação minicipio é obrigatório para PJ");

            if (filialModel.IdRegimeEspecialTributacao == null)
              throw new Exception("O campo de regime especial tributação é obrigatório para PJ");
          }
          else
          {
            Infrastructure.Helpers.ValidadorHelper.ValidaCPF(filialModel.CPFCNPJ);
          }

          var model = filialModel.ToDatabase();
          new FilialServices().Edit(model);

          E_Organizacao org = new OrganizacaoServices().GetById(filialModel.IdOrganizacao);
          org.O_RazaoSocial = filialModel.Nome;

          new OrganizacaoServices().Edit(org);

          return RedirectToAction(nameof(Index));
        }
        return View(filialModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(filialModel);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string Delete(int id)
    {
      try
      {
        FilialServices Service = new FilialServices();

        Service.Delete(id);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }

    [HttpGet]
    public ActionResult GetOrganizacaoSelect(string term)
    {
      OrganizacaoServices Service = new OrganizacaoServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }

    [HttpGet]
    public ActionResult GetTipoFilialSelect(string term)
    {
      TipoFilialServices Service = new TipoFilialServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }

    [HttpGet]
    public ActionResult GetTipoPessoaSelect(string term)
    {
      TipoPessoaService Service = new TipoPessoaService();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }

    [HttpGet]
    public ActionResult GetRegimeEspecialTributacaoSelect(string term)
    {
      RegimeEspecialTributacaoServices Service = new RegimeEspecialTributacaoServices(ContextoUsuario.UserLogged);
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }

    public PartialViewResult GetControlePermissao(int id)
    {
      try
      {
        List<OrganizacaoUsuarioDetails> controle = new OrganizacaoUsuarioServices().GetAllSociosFilial(id);
        return PartialView("_GridControlePermissao", controle);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }
    public PartialViewResult GetControlePermissaoSecretaria(int id)
    {
      try
      {
        List<OrganizacaoUsuarioDetails> controle = new OrganizacaoUsuarioServices().GetAllSociosFilialSecretaria(id);
        return PartialView("_GridSecretaria", controle);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public PartialViewResult GetVinculos(int id)
    {
      try
      {
        List<OrganizacaoUsuarioDetails> controle = new OrganizacaoUsuarioServices().GetAllSociosFilial(id);
        return PartialView("_GridVinculos", controle);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpGet]
    public PartialViewResult GetSocios(Guid id, string filtro)
    {
      try
      {
        List<UsuarioBasico> lista = new List<UsuarioBasico>();

        if (!string.IsNullOrEmpty(filtro))
        {
          var usuario = new UsuariosServices().GetSocios(filtro);

          if (usuario != null)
            lista.Add(usuario);
        }
        return PartialView("_GridFiltroSocios", lista);
      }
      catch (Exception ex)
      {
        return PartialView("_GridFiltroSocios", new List<FilialPartial>());
        throw new Exception(ex.Message);
      }
    }

    [HttpGet]
    public PartialViewResult GetSecretaria(string filtro)
    {
      try
      {
        List<UsuarioBasico> lista = new List<UsuarioBasico>();

        if (!string.IsNullOrEmpty(filtro))
        {
          lista = new UsuariosServices().GetSecretaria(filtro);

          //if (usuario != null)
          //  lista.Add(usuario);
        }
        return PartialView("_GridFiltroSecretaria", lista);
      }
      catch (Exception ex)
      {
        return PartialView("_GridFiltroSecretaria", new List<FilialPartial>());
        throw new Exception(ex.Message);
      }
    }

    [HttpPost]
    public JsonResult SaveVinculoSecretaria(Guid id, int? codfilial, int codorg)
    {
      try
      {
        bool verificaUsuarioIsCoperado = new AccountServices().VerificaUsuarioIsSecretaria(id);

        if (!verificaUsuarioIsCoperado)
          throw new CustomException("Este usuario não é Secretária.");

        E_OrganizacaoUsuario org = new OrganizacaoUsuarioServices().GetBy(id, codorg, codfilial);

        if (org == null)
        {
          org = new E_OrganizacaoUsuario()
          {
            IdUsuario = id,
            IdOrganizacao = codorg,
            IdFilial = codfilial
          };

          new OrganizacaoServices().Create(org);
          return new JsonResult(new { Error = false, Mensage = "Salvo com Sucesso." });
        }

        return new JsonResult(new { Error = false, Mensage = "Vinculo já existente" });
      }
      catch (CustomException ex)
      {
        return new JsonResult(new { Error = true, Mensage = ex.Message });
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpPost]
    public JsonResult SaveVinculo(Guid id, int? codfilial, int codorg)
    {
      try
      {
        bool verificaUsuarioIsCoperado = new AccountServices().VerificaUsuarioIsCooperado(id);
        if (!verificaUsuarioIsCoperado)
          throw new CustomException("Este usuario não é cooperado.");
        E_OrganizacaoUsuario org = new OrganizacaoUsuarioServices().GetBy(id, codorg, codfilial);

        if (org == null)
        {
          org = new E_OrganizacaoUsuario()
          {
            IdUsuario = id,
            IdOrganizacao = codorg,
            IdFilial = codfilial
          };

          new OrganizacaoServices().Create(org);
          return new JsonResult(new { Error = false, Mensage = "Salvo com Sucesso." });
        }

        return new JsonResult(new { Error = false, Mensage = "Vinculo já existente" });
      }
      catch (CustomException ex)
      {
        return new JsonResult(new { Error = true, Mensage = ex.Message });
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpPost]
    public JsonResult DeleteAssociacao(Guid id, int codfilial, int codorg)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        ControlePermissaoUserServices.DeleteAllAssociacaoToUser(id, codfilial, codorg);

        string msg = "Associações removida com sucesso";

        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = msg;
        retornoAjax.Erro = false;
        return Json(retornoAjax);

      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
    }

    [HttpGet]
    public ActionResult GetContabilidadeSelect(string term)
    {
      ContabilidadeService Service = new ContabilidadeService();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }

    [HttpGet]
    public PartialViewResult _GetPartialEditcontab(int id)
    {
      try
      {
        FilialModel model = new FilialModel().FromDatabase(new FilialServices().GetById(id));
        return PartialView("_PartialAddContab", model);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpGet]
    public JsonResult EditContabilidade(int id, int idcontab)
    {
      try
      {
        ContabilidadeService service = new ContabilidadeService();
        service.DeletePermissoesContabilidadeFilial(id);
        E_Filial model = new FilialServices().GetById(id);

        model.F_IdContabilidade = idcontab;

        new FilialServices().Edit(model);
        service.InserePermissaoContabilidadeFilial(model.F_Id);
        return Json(new { error = false, message = "Alterado com sucesso." });
      }
      catch (Exception ex)
      {
        return Json(new { error = true, message = ex.Message });
      }
    }

    public PartialViewResult _ModalCreateUserSemCpf(int IdFilial)
    {
      BasicRegisterModel model = new BasicRegisterModel();
      model.TipoCadastro = (int)EnumTipoCadastro.Secretaria;
      model.IdFilial = IdFilial;
      return PartialView("_ModalAdcSecretaria", model);
    }

    [HttpPost]
    public JsonResult CrateUserAndVinciular(BasicRegisterModel model)
    {
      try
      {
        model.CPF = model.CPF.Replace(".", "").Replace("-", "");
        model.TelefoneCelular = model.TelefoneCelular.Replace("(", "").Replace(")", "").Replace("-", "");
        Infrastructure.Helpers.ValidadorHelper.ValidaCPF(model.CPF);

        new AccountServices().RegistrarUsuarioSecretaria(model);

        return new JsonResult(new { Error = false, Mensage = "Salvo com Sucesso." });
      }
      catch (CustomException ex)
      {
        return new JsonResult(new { Error = true, Mensage = ex.Message });
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpPost]
    public JsonResult SendEmailContab(ContabConvidadoCreate model)
    {
      try
      {
        string cpfCnpj = model.CPFCNPJ.Replace(".", "").Replace("-", "").Replace(@"/", "");

        if (cpfCnpj.Length > 11)
        {
          if (!Infrastructure.Helpers.ValidadorHelper.ValidaCNPJ(cpfCnpj))
            throw new Exception("CNPJ Inválido");
        }
        else
        {
          Infrastructure.Helpers.ValidadorHelper.ValidaCPF(cpfCnpj);
        }

        new ContabConvidadoService(ContextoUsuario.UserLogged).Create(model);

        string url = ConfigurationManager.AppSettings["URLBase"] + "Account/Register";
        string url2 = ConfigurationManager.AppSettings["URLBase"] + "Account/Login";

        string msg = string.Format(@"Prezado(a) <b>{0}</b>, </br> 
                                     Você esta recebendo um convite para fazer o seu cadastro ao Portal Unicontábil, pedimos que acesse o portal, faça o seu cadastro e faça a solicitação de acesso aos dados dos seus clientes.
                              </br> - Para efetuar o seu cadastro no Portal Uniconábil, Clique <a href='{2}'>Aqui</a>
                              </br> - Caso já tenha o cadastro, Clique  <a href='{3}'>Aqui</a> para acessar.
                                    Este convite foi enviado por {1}"
                               , model.Nome, ContextoUsuario.UserLogged.Nome, url, url2);

        List<string> email = new List<string>();
        email.Add(model.Email);

        new FilialServices(ContextoUsuario.UserLogged).SendEmail(email, msg);

        return Json(new { erro = false, message = "Email enviado com sucesso." });
      }
      catch (Exception ex)
      {
        return Json(new { erro = true, message = ex.Message });
      }
    }

    [HttpPost]
    public JsonResult SendEmailMed(MedConvidadoCreate model)
    {
      try
      {

        string cpfCnpj = model.CPFCNPJ.Replace(".","").Replace("-","").Replace(@"/","");

        if(cpfCnpj.Length > 11)
        {
          if (!Infrastructure.Helpers.ValidadorHelper.ValidaCNPJ(cpfCnpj))
            throw new Exception("CNPJ Inválido");
        }
        else
        {
          Infrastructure.Helpers.ValidadorHelper.ValidaCPF(cpfCnpj);
        }

        new MedConvidadoService(ContextoUsuario.UserLogged).Create(model);

        string url = ConfigurationManager.AppSettings["URLBase"] + "Account/Register";
        string url2 = ConfigurationManager.AppSettings["URLBase"] + "Account/Login";

        string Contabiliade = new ContabilidadeService().GetById(ContextoUsuario.UserLogged.Contabilidade.Id).C_Nome;

        string msg = string.Format(@"Prezado(a) <b>{0}</b>,</br> 
                                      A Contabilidade {1}, esta lhe convidando para o uso do Portal Unicontábil.
                                </br> - Para efetuar o seu cadastro no Portal Uniconábil, Clique <a href='{2}'>Aqui</a>
                                </br> - Caso já tenha o cadastro, Clique  <a href='{3}'>Aqui</a> para acessar."
                               , model.Nome, Contabiliade, url, url2);

        List<string> email = new List<string>();
        email.Add(model.Email);

        new FilialServices(ContextoUsuario.UserLogged).SendEmail(email, msg);

        return Json(new { erro = false, message = "Email enviado com sucesso." });
      }
      catch (Exception ex)
      {
        return Json(new { erro = true, message = ex.Message });
      }
    }

    [HttpGet]
    public PartialViewResult GetMedConvidado()
    {
      try
      {
        return PartialView("_PartialGridMedConvidado", new MedConvidadoService(ContextoUsuario.UserLogged).GetAll());
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpGet]
    public PartialViewResult GetContabConvidado()
    {
      try
      {
        return PartialView("_PartialGridContabConvidado", new ContabConvidadoService(ContextoUsuario.UserLogged).GetAll());
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpGet]
    public JsonResult AceitaMedConvite(int id)
    {
      try
      {
        int? idcontab = new MedConvidadoService(ContextoUsuario.UserLogged).AceitaConvite(id);
        ContabilidadeService service = new ContabilidadeService();

        service.DeletePermissoesContabilidadeFilial(ContextoUsuario.UserLogged.IdFilial.Value);
        E_Filial model = new FilialServices().GetById(ContextoUsuario.UserLogged.IdFilial.Value);

        model.F_IdContabilidade = idcontab;

        new FilialServices().Edit(model);
        service.InserePermissaoContabilidadeFilial(model.F_Id);

        ContextoUsuario.ConviteMed();

        return Json(new { erro = false, message = "convite Aceito." });
      }
      catch (Exception ex)
      {
        return Json(new { erro = true, message = ex.Message });
      }
    }

    [HttpGet]
    public JsonResult RejeitarMedConvite(int id)
    {
      try
      {
        new MedConvidadoService(ContextoUsuario.UserLogged).RejeitarConvite(id);

        ContextoUsuario.ConviteMed();
        return Json(new { erro = false, message = "O convite foi rejeitado." });
      }
      catch (Exception ex)
      {
        return Json(new { erro = true, message = ex.Message });
      }
    }

  }
}
