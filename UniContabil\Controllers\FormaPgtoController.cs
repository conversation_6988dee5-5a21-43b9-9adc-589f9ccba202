﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;


using UniContabilEntidades.Models; 
using UniContabilEntidades.Models.DataBase; 

namespace UniContabil.Controllers
{
    public class FormaPgtoController : LibController
    {
        public FormaPgtoController()
        { }

        [HttpGet]
        [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
        public ActionResult Index(int page = 1)
        {
            try 
            {
                IPagedList<FormaPgtoModel> List = new FormaPgtoServices().GetPagedList(page);
                ViewBag.PageItems = List;
                return View();
            }
            catch(Exception Ex) 
            {
                MessageAdd(new Message(MessageType.Error, Ex.Message));
                return View();
            }
        }

        [HttpGet]
        [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
        public ActionResult Create()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
        public ActionResult Create(FormaPgtoModel formaPgtoModel)
        {
            try 
            {
                if (ModelState.IsValid)
                {
                  var model = formaPgtoModel.ToDatabase();
                  new FormaPgtoServices().Create(model);

                  return RedirectToAction(nameof(Index));
                }
                else {
                    return View(formaPgtoModel);
                }
            }
            catch(Exception Ex) 
            {
                MessageAdd(new Message(MessageType.Error, Ex.Message));
                return View(formaPgtoModel);
            }
        }
  
        [HttpGet]
        [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
        public ActionResult Edit(int id)
        {
            try 
            {
                FormaPgtoModel formaPgtoModel = new FormaPgtoModel().FromDatabase(new FormaPgtoServices().GetById(id));

                if (formaPgtoModel == null)
                {
                    return NotFound();
                }                

                return View(formaPgtoModel);
            }
            catch(Exception Ex) 
            {
                MessageAdd(new Message(MessageType.Error, Ex.Message));
                return View();
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
        public ActionResult Edit(FormaPgtoModel formaPgtoModel)
        {
            try 
            {
                if (ModelState.IsValid)
                {
                    var model = formaPgtoModel.ToDatabase();
                    new FormaPgtoServices().Edit(model);
                    return RedirectToAction(nameof(Index));
                }
                return View(formaPgtoModel);
            }
            catch(Exception Ex) 
            {
                MessageAdd(new Message(MessageType.Error, Ex.Message));
                return View(formaPgtoModel);
            }
        }

        [HttpPost]
        [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
        public string Delete(int id)
        {
            try
            {
                FormaPgtoServices Service = new FormaPgtoServices();
                var model = Service.GetById(id);
                Service.Delete(model);
                return JsonConvert.SerializeObject(new {Sucesso = true});
            }
            catch(Exception Ex) 
            {
                return JsonConvert.SerializeObject(new {Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult)});
            }
        }
  
        [HttpGet]
        public ActionResult GetOrganizacaoSelect(string term)
        {
          OrganizacaoServices Service = new OrganizacaoServices();
          List<Select2Model> List = Service.GetByTerm(term);
          return Json(new { items = List });
        }
        [HttpGet]
        public ActionResult GetFilialSelect(string term)
        {
          FilialServices Service = new FilialServices();
          List<Select2Model> List = Service.GetByTerm(term);
          return Json(new { items = List });
        }
    }
}
