﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;
using UniContabilDomain.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace UniContabil.Controllers
{
  public class GrupoUsuarioController : Controller
  {
    [HttpGet]
    public ActionResult Index()
    {
      return View();
    }

    [HttpGet]
    public ActionResult GetGrupos()
    {
      List<E_Grupo> Grupos = new GrupoUsuarioServices().GetList();
      return PartialView("_ListaGrupos", Grupos);
    }

    [HttpGet]
    public ActionResult GetUsuariosGrupo(int Id)
    {
      List<GrupoUsuarioModel> Usuarios = new GrupoUsuarioServices().GetUsuariosById(Id);
      return PartialView("_Usuarios", Usuarios);
    }

    [HttpGet]
    public ActionResult GetPermissoesGrupo()
    {
      return PartialView("_Permissoes");
    }

    [HttpGet]
    public string ListPermissoesGrupo(int Id)
    {
      try
      {
        List<TreeviewModel> Treeview = new GrupoUsuarioServices().GetPermissoesById(Id);
        return JsonConvert.SerializeObject(new { Sucesso = true, Treeview });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    public string CreatePermissao(AlterarPermissaoModel Model)
    {
      try
      {
        new GrupoUsuarioServices().AdicionarPermissaoGrupo(Model.IdGrupo, Model.IdItem);    
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    public string DeletePermissao(AlterarPermissaoModel Model)
    {
      try
      {
        new GrupoUsuarioServices().RemoverPermissaoGrupo(Model.IdGrupo, Model.IdItem);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    public string Vincular(VincularUsuarioModel Model)
    {
      try
      {
        GrupoUsuarioServices Services = new GrupoUsuarioServices();
        E_Grupo Grupo = Services.GetById(Model.IdGrupo);

        if (Grupo == null)
          return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = "Grupo não encontrado. Por favor atualize a página." });

        Services.VincularUsuario(Model.IdUsuario, Model.IdGrupo);

        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    public string Desvincular(int Id)
    {
      try
      {
        GrupoUsuarioServices Services = new GrupoUsuarioServices();
        E_GrupoUsuario GU = Services.GetById<E_GrupoUsuario>(Id);

        if (GU == null)
          return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = "Associação não encontrada. Por favor atualize a página." });

        Services.Delete(GU);

        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    public string Create(string Nome)
    {
      try
      {
        E_Grupo Grupo = new E_Grupo()
        {
          G_Nome = Nome
        };

        new GrupoUsuarioServices().Create(Grupo);

        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    public string Delete(int Id)
    {
      try
      {
        new GrupoUsuarioServices().DeleteGrupo(Id);

        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = Ex.Message });
      }
    }
  }
}
