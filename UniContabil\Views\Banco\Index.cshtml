﻿@model IPagedList<UniContabilEntidades.Models.BancoModel>
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;

@{
  ViewData["Title"] = @Html.DisplayNameFor(model => model);
  Layout = "~/Views/Shared/_Layout.cshtml";
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();
}
<div class="table-group card">
  <div class="card-header">
    <div class="row">
      @if (ContextoUsuario.HasPermission(controllerName, TipoFuncao.Create))
      {
        <a class="btn btn-success" asp-action="Create"><i class="mw-drip-plus"></i></a>
      }
    </div>
  </div>
  <div class="table-group-content">
    <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
      <thead>
        <tr>
          <th>
            Descrição
          </th>
          <th>
            Banco
          </th>
          <th>
            Agência
          </th>
          <th>
            Conta
          </th>
          <th>
            Digito
          </th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        @if (Model != null)
        {
          @foreach (UniContabilEntidades.Models.BancoModel item in Model)
          {
            <tr>
              <td>
                @Html.DisplayFor(modelItem => item.NomeConta)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.CodBanco)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.CodAgencia)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.CodConta)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.DVConta)
              </td>
              <td>
                @if (ContextoUsuario.HasPermission(controllerName, TipoFuncao.Edit))
                {
                  <a class="btn btn-warning btn-sm" asp-action="Edit" asp-route-id="@item.Id"><i class="mw-drip-document-edit"></i></a>
                }
                @if (ContextoUsuario.HasPermission(controllerName, TipoFuncao.Delete))
                {
                  <a class="btn btn-danger btn-sm" onclick="DeleteItem('@this.ViewContext.RouteData.Values["controller"].ToString()', @item.Id)"><i class="mw-drip-trash"></i></a>
                }
              </td>
            </tr>
          }
        }
      </tbody>
    </table>
  </div>
  <div class="card-footer">
    @Html.PagedListPager((IPagedList)Model, page => Url.Action("Index", new { page }),
    new PagedListRenderOptions
    {
      LiElementClasses = new string[] { "page-item" },
      PageClasses = new string[] { "page-link" },
      Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
      MaximumPageNumbersToDisplay = 5
    })
  </div>
</div>

