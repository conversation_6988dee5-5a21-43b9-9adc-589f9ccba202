﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Exceptions;

using UniContabilEntidades.Models;
using UniContabilDomain.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace UniContabil.Controllers
{
  [AllowAnonymous]
  public class NotaFiscalComprasController : LibController
  {
    private NotaFiscalComprasServices NotaFiscalComprasServices
    {
      get
      {
        if (_NotaFiscalComprasServices == null)
          _NotaFiscalComprasServices = new NotaFiscalComprasServices(ContextoUsuario.UserLogged);

        return _NotaFiscalComprasServices;
      }
    }
    private NotaFiscalComprasServices _NotaFiscalComprasServices;

    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int? page)
    {
      try
      {
        int pageNumber = page ?? 1;
        List<NotaFiscalCompIndexModel> list = new List<NotaFiscalCompIndexModel>();
        list = NotaFiscalComprasServices.GetIndex(pageNumber);
        return View(list);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.GerarNotaFiscalCompra)]
    public JsonResult GeraNotaFiscal(GeraNotaFiscalComp GeraNotaFiscal)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        NotaFiscalComprasServices.GeraNotaFiscalByPedidoCompra(GeraNotaFiscal);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Nota fiscal gerada com sucesso.";
        retornoAjax.Erro = false;

        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
    }

  }
}
