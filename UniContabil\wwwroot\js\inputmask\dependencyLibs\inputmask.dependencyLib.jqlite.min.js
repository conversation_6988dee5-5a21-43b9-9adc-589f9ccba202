/*!
* dependencyLibs/inputmask.dependencyLib.jqlite.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2017 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 3.3.11
*/

!function(n){"function"==typeof define&&define.amd?define(["jqlite","../global/window","../global/document]"],n):"object"==typeof exports?module.exports=n(require("jqlite"),require("../global/window"),require("../global/document")):window.dependencyLib=n(jqlite,window,document)}(function(n,e,t){function r(n,e){for(var t=0,r=n.length;t<r;t++)if(n[t]===e)return t;return-1}function o(n){return null==n?n+"":"object"==typeof n||"function"==typeof n?l[l.toString.call(n)]||"object":typeof n}function i(n){return null!=n&&n===n.window}function u(n){var e="length"in n&&n.length,t=o(n);return"function"!==t&&!i(n)&&(!(1!==n.nodeType||!e)||("array"===t||0===e||"number"==typeof e&&e>0&&e-1 in n))}for(var l={},c="Boolean Number String Function Array Date RegExp Object Error".split(" "),a=0;a<c.length;a++)l["[object "+c[a]+"]"]=c[a].toLowerCase();return n.inArray=function(n,e,t){return null==e?-1:r(e,n)},n.isFunction=function(n){return"function"===o(n)},n.isArray=Array.isArray,n.isPlainObject=function(n){return"object"===o(n)&&!n.nodeType&&!i(n)&&!(n.constructor&&!l.hasOwnProperty.call(n.constructor.prototype,"isPrototypeOf"))},n.extend=function(){var e,t,r,o,i,u,l=arguments[0]||{},c=1,a=arguments.length,f=!1;for("boolean"==typeof l&&(f=l,l=arguments[c]||{},c++),"object"==typeof l||n.isFunction(l)||(l={}),c===a&&(l=this,c--);c<a;c++)if(null!=(e=arguments[c]))for(t in e)r=l[t],l!==(o=e[t])&&(f&&o&&(n.isPlainObject(o)||(i=n.isArray(o)))?(i?(i=!1,u=r&&n.isArray(r)?r:[]):u=r&&n.isPlainObject(r)?r:{},l[t]=n.extend(f,u,o)):void 0!==o&&(l[t]=o));return l},n.each=function(n,e){var t=0;if(u(n))for(var r=n.length;t<r&&!1!==e.call(n[t],t,n[t]);t++);else for(t in n)if(!1===e.call(n[t],t,n[t]))break;return n},n.map=function(n,e){var t,r=0,o=n.length,i=[];if(u(n))for(;r<o;r++)null!=(t=e(n[r],r))&&i.push(t);else for(r in n)null!=(t=e(n[r],r))&&i.push(t);return[].concat(i)},n.data=function(e,t,r){return n(e).data(t,r)},n.Event=n.Event||function(n,e){e=e||{bubbles:!1,cancelable:!1,detail:void 0};var r=t.createEvent("CustomEvent");return r.initCustomEvent(n,e.bubbles,e.cancelable,e.detail),r},n.Event.prototype=e.Event.prototype,n});