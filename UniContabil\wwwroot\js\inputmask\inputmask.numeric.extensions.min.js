/*!
* inputmask.numeric.extensions.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2017 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 3.3.11
*/

!function(e){"function"==typeof define&&define.amd?define(["./dependencyLibs/inputmask.dependencyLib","./inputmask"],e):"object"==typeof exports?module.exports=e(require("./dependencyLibs/inputmask.dependencyLib"),require("./inputmask")):e(window.dependencyLib||jQuery,window.Inputmask)}(function(e,i,t){function r(e,t){for(var r="",a=0;a<e.length;a++)i.prototype.definitions[e.charAt(a)]||t.definitions[e.charAt(a)]||t.optionalmarker.start===e.charAt(a)||t.optionalmarker.end===e.charAt(a)||t.quantifiermarker.start===e.charAt(a)||t.quantifiermarker.end===e.charAt(a)||t.groupmarker.start===e.charAt(a)||t.groupmarker.end===e.charAt(a)||t.alternatormarker===e.charAt(a)?r+="\\"+e.charAt(a):r+=e.charAt(a);return r}return i.extendAliases({numeric:{mask:function(e){if(0!==e.repeat&&isNaN(e.integerDigits)&&(e.integerDigits=e.repeat),e.repeat=0,e.groupSeparator===e.radixPoint&&("."===e.radixPoint?e.groupSeparator=",":","===e.radixPoint?e.groupSeparator=".":e.groupSeparator="")," "===e.groupSeparator&&(e.skipOptionalPartCharacter=t),e.autoGroup=e.autoGroup&&""!==e.groupSeparator,e.autoGroup&&("string"==typeof e.groupSize&&isFinite(e.groupSize)&&(e.groupSize=parseInt(e.groupSize)),isFinite(e.integerDigits))){var i=Math.floor(e.integerDigits/e.groupSize),a=e.integerDigits%e.groupSize;e.integerDigits=parseInt(e.integerDigits)+(0===a?i-1:i),e.integerDigits<1&&(e.integerDigits="*")}e.placeholder.length>1&&(e.placeholder=e.placeholder.charAt(0)),"radixFocus"===e.positionCaretOnClick&&""===e.placeholder&&!1===e.integerOptional&&(e.positionCaretOnClick="lvp"),e.definitions[";"]=e.definitions["~"],e.definitions[";"].definitionSymbol="~",!0===e.numericInput&&(e.positionCaretOnClick="radixFocus"===e.positionCaretOnClick?"lvp":e.positionCaretOnClick,e.digitsOptional=!1,isNaN(e.digits)&&(e.digits=2),e.decimalProtect=!1);var n="[+]";if(n+=r(e.prefix,e),!0===e.integerOptional?n+="~{1,"+e.integerDigits+"}":n+="~{"+e.integerDigits+"}",e.digits!==t){e.radixPointDefinitionSymbol=e.decimalProtect?":":e.radixPoint;var o=e.digits.toString().split(",");isFinite(o[0]&&o[1]&&isFinite(o[1]))?n+=e.radixPointDefinitionSymbol+";{"+e.digits+"}":(isNaN(e.digits)||parseInt(e.digits)>0)&&(e.digitsOptional?n+="["+e.radixPointDefinitionSymbol+";{1,"+e.digits+"}]":n+=e.radixPointDefinitionSymbol+";{"+e.digits+"}")}return n+=r(e.suffix,e),n+="[-]",e.greedy=!1,n},placeholder:"",greedy:!1,digits:"*",digitsOptional:!0,enforceDigitsOnBlur:!1,radixPoint:".",positionCaretOnClick:"radixFocus",groupSize:3,groupSeparator:"",autoGroup:!1,allowMinus:!0,negationSymbol:{front:"-",back:""},integerDigits:"+",integerOptional:!0,prefix:"",suffix:"",rightAlign:!0,decimalProtect:!0,min:null,max:null,step:1,insertMode:!0,autoUnmask:!1,unmaskAsNumber:!1,inputmode:"numeric",preValidation:function(i,r,a,n,o){if("-"===a||a===o.negationSymbol.front)return!0===o.allowMinus&&(o.isNegative=o.isNegative===t||!o.isNegative,""===i.join("")||{caret:r,dopost:!0});if(!1===n&&a===o.radixPoint&&o.digits!==t&&(isNaN(o.digits)||parseInt(o.digits)>0)){var p=e.inArray(o.radixPoint,i);if(-1!==p)return!0===o.numericInput?r===p:{caret:p+1}}return!0},postValidation:function(r,a,n){var o=n.suffix.split(""),p=n.prefix.split("");if(a.pos===t&&a.caret!==t&&!0!==a.dopost)return a;var l=a.caret!==t?a.caret:a.pos,s=r.slice();n.numericInput&&(l=s.length-l-1,s=s.reverse());var g=s[l];if(g===n.groupSeparator&&(g=s[l+=1]),l===s.length-n.suffix.length-1&&g===n.radixPoint)return a;g!==t&&g!==n.radixPoint&&g!==n.negationSymbol.front&&g!==n.negationSymbol.back&&(s[l]="?",n.prefix.length>0&&l>=(!1===n.isNegative?1:0)&&l<n.prefix.length-1+(!1===n.isNegative?1:0)?p[l-(!1===n.isNegative?1:0)]="?":n.suffix.length>0&&l>=s.length-n.suffix.length-(!1===n.isNegative?1:0)&&(o[l-(s.length-n.suffix.length-(!1===n.isNegative?1:0))]="?")),p=p.join(""),o=o.join("");var c=s.join("").replace(p,"");if(c=c.replace(o,""),c=c.replace(new RegExp(i.escapeRegex(n.groupSeparator),"g"),""),c=c.replace(new RegExp("[-"+i.escapeRegex(n.negationSymbol.front)+"]","g"),""),c=c.replace(new RegExp(i.escapeRegex(n.negationSymbol.back)+"$"),""),isNaN(n.placeholder)&&(c=c.replace(new RegExp(i.escapeRegex(n.placeholder),"g"),"")),c.length>1&&1!==c.indexOf(n.radixPoint)&&("0"===g&&(c=c.replace(/^\?/g,"")),c=c.replace(/^0/g,"")),c.charAt(0)===n.radixPoint&&""!==n.radixPoint&&!0!==n.numericInput&&(c="0"+c),""!==c){if(c=c.split(""),(!n.digitsOptional||n.enforceDigitsOnBlur&&"blur"===a.event)&&isFinite(n.digits)){var d=e.inArray(n.radixPoint,c),u=e.inArray(n.radixPoint,s);-1===d&&(c.push(n.radixPoint),d=c.length-1);for(var x=1;x<=n.digits;x++)n.digitsOptional&&(!n.enforceDigitsOnBlur||"blur"!==a.event)||c[d+x]!==t&&c[d+x]!==n.placeholder.charAt(0)?-1!==u&&s[u+x]!==t&&(c[d+x]=c[d+x]||s[u+x]):c[d+x]=a.placeholder||n.placeholder.charAt(0)}if(!0!==n.autoGroup||""===n.groupSeparator||g===n.radixPoint&&a.pos===t&&!a.dopost)c=c.join("");else{var f=c[c.length-1]===n.radixPoint&&a.c===n.radixPoint;c=i(function(e,i){var t="";if(t+="("+i.groupSeparator+"*{"+i.groupSize+"}){*}",""!==i.radixPoint){var r=e.join("").split(i.radixPoint);r[1]&&(t+=i.radixPoint+"*{"+r[1].match(/^\d*\??\d*/)[0].length+"}")}return t}(c,n),{numericInput:!0,jitMasking:!0,definitions:{"*":{validator:"[0-9?]",cardinality:1}}}).format(c.join("")),f&&(c+=n.radixPoint),c.charAt(0)===n.groupSeparator&&c.substr(1)}}if(n.isNegative&&"blur"===a.event&&(n.isNegative="0"!==c),c=p+c,c+=o,n.isNegative&&(c=n.negationSymbol.front+c,c+=n.negationSymbol.back),c=c.split(""),g!==t)if(g!==n.radixPoint&&g!==n.negationSymbol.front&&g!==n.negationSymbol.back)(l=e.inArray("?",c))>-1?c[l]=g:l=a.caret||0;else if(g===n.radixPoint||g===n.negationSymbol.front||g===n.negationSymbol.back){var m=e.inArray(g,c);-1!==m&&(l=m)}n.numericInput&&(l=c.length-l-1,c=c.reverse());var h={caret:g===t||a.pos!==t?l+(n.numericInput?-1:1):l,buffer:c,refreshFromBuffer:a.dopost||r.join("")!==c.join("")};return h.refreshFromBuffer?h:a},onBeforeWrite:function(r,a,n,o){if(r)switch(r.type){case"keydown":return o.postValidation(a,{caret:n,dopost:!0},o);case"blur":case"checkval":var p;if(function(e){e.parseMinMaxOptions===t&&(null!==e.min&&(e.min=e.min.toString().replace(new RegExp(i.escapeRegex(e.groupSeparator),"g"),""),","===e.radixPoint&&(e.min=e.min.replace(e.radixPoint,".")),e.min=isFinite(e.min)?parseFloat(e.min):NaN,isNaN(e.min)&&(e.min=Number.MIN_VALUE)),null!==e.max&&(e.max=e.max.toString().replace(new RegExp(i.escapeRegex(e.groupSeparator),"g"),""),","===e.radixPoint&&(e.max=e.max.replace(e.radixPoint,".")),e.max=isFinite(e.max)?parseFloat(e.max):NaN,isNaN(e.max)&&(e.max=Number.MAX_VALUE)),e.parseMinMaxOptions="done")}(o),null!==o.min||null!==o.max){if(p=o.onUnMask(a.join(""),t,e.extend({},o,{unmaskAsNumber:!0})),null!==o.min&&p<o.min)return o.isNegative=o.min<0,o.postValidation(o.min.toString().replace(".",o.radixPoint).split(""),{caret:n,dopost:!0,placeholder:"0"},o);if(null!==o.max&&p>o.max)return o.isNegative=o.max<0,o.postValidation(o.max.toString().replace(".",o.radixPoint).split(""),{caret:n,dopost:!0,placeholder:"0"},o)}return o.postValidation(a,{caret:n,placeholder:"0",event:"blur"},o);case"_checkval":return{caret:n}}},regex:{integerPart:function(e,t){return t?new RegExp("["+i.escapeRegex(e.negationSymbol.front)+"+]?"):new RegExp("["+i.escapeRegex(e.negationSymbol.front)+"+]?\\d+")},integerNPart:function(e){return new RegExp("[\\d"+i.escapeRegex(e.groupSeparator)+i.escapeRegex(e.placeholder.charAt(0))+"]+")}},definitions:{"~":{validator:function(e,r,a,n,o,p){var l=n?new RegExp("[0-9"+i.escapeRegex(o.groupSeparator)+"]").test(e):new RegExp("[0-9]").test(e);if(!0===l){if(!0!==o.numericInput&&r.validPositions[a]!==t&&"~"===r.validPositions[a].match.def&&!p){var s=r.buffer.join(""),g=(s=(s=s.replace(new RegExp("[-"+i.escapeRegex(o.negationSymbol.front)+"]","g"),"")).replace(new RegExp(i.escapeRegex(o.negationSymbol.back)+"$"),"")).split(o.radixPoint);g.length>1&&(g[1]=g[1].replace(/0/g,o.placeholder.charAt(0))),"0"===g[0]&&(g[0]=g[0].replace(/0/g,o.placeholder.charAt(0))),s=g[0]+o.radixPoint+g[1]||"";var c=r._buffer.join("");for(s===o.radixPoint&&(s=c);null===s.match(i.escapeRegex(c)+"$");)c=c.slice(1);l=(s=(s=s.replace(c,"")).split(""))[a]===t?{pos:a,remove:a}:{pos:a}}}else n||e!==o.radixPoint||r.validPositions[a-1]!==t||(r.buffer[a]="0",l={pos:a+1});return l},cardinality:1},"+":{validator:function(e,i,t,r,a){return a.allowMinus&&("-"===e||e===a.negationSymbol.front)},cardinality:1,placeholder:""},"-":{validator:function(e,i,t,r,a){return a.allowMinus&&e===a.negationSymbol.back},cardinality:1,placeholder:""},":":{validator:function(e,t,r,a,n){var o="["+i.escapeRegex(n.radixPoint)+"]",p=new RegExp(o).test(e);return p&&t.validPositions[r]&&t.validPositions[r].match.placeholder===n.radixPoint&&(p={caret:r+1}),p},cardinality:1,placeholder:function(e){return e.radixPoint}}},onUnMask:function(e,t,r){if(""===t&&!0===r.nullable)return t;var a=e.replace(r.prefix,"");return a=a.replace(r.suffix,""),a=a.replace(new RegExp(i.escapeRegex(r.groupSeparator),"g"),""),""!==r.placeholder.charAt(0)&&(a=a.replace(new RegExp(r.placeholder.charAt(0),"g"),"0")),r.unmaskAsNumber?(""!==r.radixPoint&&-1!==a.indexOf(r.radixPoint)&&(a=a.replace(i.escapeRegex.call(this,r.radixPoint),".")),a=a.replace(new RegExp("^"+i.escapeRegex(r.negationSymbol.front)),"-"),a=a.replace(new RegExp(i.escapeRegex(r.negationSymbol.back)+"$"),""),Number(a)):a},isComplete:function(e,t){var r=e.join("");if(e.slice().join("")!==r)return!1;var a=r.replace(t.prefix,"");return a=a.replace(t.suffix,""),a=a.replace(new RegExp(i.escapeRegex(t.groupSeparator),"g"),""),","===t.radixPoint&&(a=a.replace(i.escapeRegex(t.radixPoint),".")),isFinite(a)},onBeforeMask:function(e,r){if(r.isNegative=t,e=e.toString().charAt(e.length-1)===r.radixPoint?e.toString().substr(0,e.length-1):e.toString(),""!==r.radixPoint&&isFinite(e)){var a=e.split("."),n=""!==r.groupSeparator?parseInt(r.groupSize):0;2===a.length&&(a[0].length>n||a[1].length>n||a[0].length<=n&&a[1].length<n)&&(e=e.replace(".",r.radixPoint))}var o=e.match(/,/g),p=e.match(/\./g);if(e=p&&o?p.length>o.length?(e=e.replace(/\./g,"")).replace(",",r.radixPoint):o.length>p.length?(e=e.replace(/,/g,"")).replace(".",r.radixPoint):e.indexOf(".")<e.indexOf(",")?e.replace(/\./g,""):e.replace(/,/g,""):e.replace(new RegExp(i.escapeRegex(r.groupSeparator),"g"),""),0===r.digits&&(-1!==e.indexOf(".")?e=e.substring(0,e.indexOf(".")):-1!==e.indexOf(",")&&(e=e.substring(0,e.indexOf(",")))),""!==r.radixPoint&&isFinite(r.digits)&&-1!==e.indexOf(r.radixPoint)){var l=e.split(r.radixPoint)[1].match(new RegExp("\\d*"))[0];if(parseInt(r.digits)<l.toString().length){var s=Math.pow(10,parseInt(r.digits));e=e.replace(i.escapeRegex(r.radixPoint),"."),e=(e=Math.round(parseFloat(e)*s)/s).toString().replace(".",r.radixPoint)}}return e},canClearPosition:function(e,i,t,r,a){var n=e.validPositions[i],o=n.input!==a.radixPoint||null!==e.validPositions[i].match.fn&&!1===a.decimalProtect||n.input===a.radixPoint&&e.validPositions[i+1]&&null===e.validPositions[i+1].match.fn||isFinite(n.input)||i===t||n.input===a.groupSeparator||n.input===a.negationSymbol.front||n.input===a.negationSymbol.back;return!o||"+"!==n.match.nativeDef&&"-"!==n.match.nativeDef||(a.isNegative=!1),o},onKeyDown:function(t,r,a,n){var o=e(this);if(t.ctrlKey)switch(t.keyCode){case i.keyCode.UP:o.val(parseFloat(this.inputmask.unmaskedvalue())+parseInt(n.step)),o.trigger("setvalue");break;case i.keyCode.DOWN:o.val(parseFloat(this.inputmask.unmaskedvalue())-parseInt(n.step)),o.trigger("setvalue")}}},currency:{prefix:"$ ",groupSeparator:",",alias:"numeric",placeholder:"0",autoGroup:!0,digits:2,digitsOptional:!1,clearMaskOnLostFocus:!1},decimal:{alias:"numeric"},integer:{alias:"numeric",digits:0,radixPoint:""},percentage:{alias:"numeric",digits:2,digitsOptional:!0,radixPoint:".",placeholder:"0",autoGroup:!1,min:0,max:100,suffix:" %",allowMinus:!1}}),i});