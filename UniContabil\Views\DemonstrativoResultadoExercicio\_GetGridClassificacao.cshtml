﻿@using UniContabilEntidades.Models

@model List<ListaClassificacao>

<div class="table-group card">
  <table class=" table table-hover table-striped">
    <thead>
      <tr>
        <th>Código</th>
        <th>Descrição</th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      @foreach (ListaClassificacao item in Model)
      {
        <tr>
          <td>
            @Html.DisplayFor(modelItem => item.Codigo)
          </td>
          <td>
            @Html.DisplayFor(modelItem => item.Descricao)
          </td>
          <td>
            <button class="incluiClassificacao btn btn-primary" data-id="@item.Id">Incluir</button>
          </td>
        </tr>
      }
    </tbody>
  </table>
</div>