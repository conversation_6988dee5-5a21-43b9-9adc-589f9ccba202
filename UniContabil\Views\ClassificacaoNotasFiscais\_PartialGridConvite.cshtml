﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure.Controls
@model List<MedConvidadoIndex>

<div class="row">
  <p><b>A Contabilidade(s) listada(s) logo abaixo solicitou permissão de acesso os dados financeiros da sua conta no Unicontábil. Clique em aceitar para liberar o acesso, ou rejeitar a solicitação.</b></p>
</div>
<div class="table-group card">
  <div class="table-group-content">
    <div class="table-responsive">
      <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
        <thead>
          <tr>
            <th>Contabilidade</th>
            <th>CPF / CNPJ</th>
            <th>Email</th>
            <th style="padding: 0px !important;width: 150px;"></th>
          </tr>
        </thead>
        <tbody>
          @foreach (MedConvidadoIndex item in Model)
          {
          <tr>
            <td>@item.Contabilidade</td>
            <td>@item.CPFCNPJ</td>
            <td>@item.Email</td>
            <td>
              <a class="btn btn-sm btn-success respostaAssociacao" data-cod="@item.Id" data-tipoResposta="1">Aceitar</a>
              <a class="btn btn-sm btn-danger respostaAssociacao" data-cod="@item.Id" data-tipoResposta="0">Rejeitar</a>
            </td>
          </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>

<script>
  $(document).on("click", ".respostaAssociacao", function () {
    var tipo = $(this).data("tiporesposta");
    var cod = $(this).data("cod");
    console.log("Tipo Convite : " + tipo)
    if (tipo == "1") {
      $.ajax({
        url: GetURLBaseComplete() + "/Filial/AceitaMedConvite/" + cod,
        type: 'GET',
        dataType: 'json',
        success: function (data) {
          if (!data.error) {
            swal({
              title: "Convite Aceito",
              text: data.message,
              icon: "success",
            });
            $("#modalConvite").modal('hide');
            $("#modalConvite").modal('show');
          }
          else {
            swal({
              title: "Erro",
              text: data.message,
              icon: "error",
            });
          }
        }
      })
    }
    else {
      $.ajax({
        url: GetURLBaseComplete() + "/Filial/RejeitarMedConvite/" + cod,
        type: 'GET',
        dataType: 'json',
        success: function (data) {
          if (!data.error) {
            swal({
              title: "Convite Rejeitado",
              text: data.message,
              icon: "success",
            });
            $("#modalConvite").modal('hide');
            $("#modalConvite").modal('show');
          }
          else {
            swal({
              title: "Erro",
              text: data.message,
              icon: "error",
            });
          }
        }
      })
    }
  });
</script>
