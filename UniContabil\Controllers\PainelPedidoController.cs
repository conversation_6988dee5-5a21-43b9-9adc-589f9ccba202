﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniContabil.Infrastructure;
using UniContabilEntidades.Models;
using UniContabilDomain.Services;
using Microsoft.AspNetCore.Mvc;

namespace UniContabil.Controllers
{
  public class PainelPedidoController : LibController
  {
    public ActionResult Index()
    {
      try
      {
        List<PainelPedidoModel> list = new PainelPedidoServices().GetAll();
        return View(list);
      }
      catch (Exception ex)
      {
        return View();
      }
    }

    public PartialViewResult _GetLinhasPedidos(int id)
    {
      try
      {
        PainelPedidoModel model = new PainelPedidoServices().GetPainel(id);
        return PartialView("_PartialTR", model);

      }
      catch (Exception ex)
      {
        throw;
      }
    }
  }
}
