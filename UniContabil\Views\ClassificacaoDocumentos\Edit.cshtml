﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model UniContabilEntidades.Models.ClassificacaoDocumentosModel

@using (Html.BeginForm("Edit", "ClassificacaoDocumentos", FormMethod.Post, new { id = "EditClassificacao" }))
{
  @Html.AntiForgeryToken()
  @Html.HiddenFor(a => Model.IdCategoria)
  @Html.HiddenFor(a => Model.CategoriaDocumentosSelect)
  <div class="card-body" id="divHeader">
    <div class="card-header">
      <h5>Alterar Classificação</h5>
    </div>
    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
    <input type="hidden" asp-for="Id" />
    <div class="row">
      <div class="col-md-2">
        <div class="form-group">
          <label asp-for="IdTipoPessoa" class="control-label"></label>
          @Html.Select2("TipoPessoaSelect", "GetTipoPessoaSelect", "Selecione o Tipo de Pessoa", "", "", Model.TipoPessoaSelect == null ? "" : Model.TipoPessoaSelect.id, Model.TipoPessoaSelect == null ? "" : Model.TipoPessoaSelect.text, "", true)
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          <label asp-for="IdTipoEntrada" class="control-label"></label>
          @Html.Select2("TipoEntradaSelect", "GetTipoEntradaSelect", "Selecione o Tipo de Entrada", "", "", Model.TipoEntradaSelect == null ? "" : Model.TipoEntradaSelect.id, Model.TipoEntradaSelect == null ? "" : Model.TipoEntradaSelect.text, "", true)
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          <label asp-for="DRE" class="control-label"></label>
          @Html.Select2("DREselect", "GetDemResEXSelect", "Selecione o DRE", "", "", Model.DREselect == null ? "" : Model.DREselect.id, Model.DREselect == null ? "" : Model.DREselect.text)
        </div>
      </div>

      <div class="row">
      </div>




    </div>
    <div class="row">
      <div class="col-md-1">
        <div class="form-group">
          <label asp-for="Codigo" class="control-label"></label>
          <input asp-for="Codigo" maxlength="3" class="form-control" />
          <span asp-validation-for="Codigo" class="text-danger"></span>
        </div>
      </div>
      <div class="col-md-5">
        <div class="form-group">
          <label asp-for="Descricao" class="control-label"></label>
          <input asp-for="Descricao" class="form-control" />
          <span asp-validation-for="Descricao" class="text-danger"></span>
        </div>
      </div>
      <div class="form-group col-md-2 form-check">
        <input class="form-check-input" asp-for="ShowAppAux" style=" margin-top: 30px;" />
        <label class="form-check-label" style="margin-top: 21px !important; margin-left: -10px;">
          Exibe no APP?
        </label>
      </div>
      <div hidden id="reciboDespesaDiv" class="form-group form-check">
        <input class="form-check-input" asp-for="InfReciboAux" style=" margin-top: 30px;" />
        <label class="form-check-label" style="margin-top: 21px !important; margin-left: -10px;">
          @Html.DisplayNameFor(model => model.InfReciboAux)
        </label>
      </div>
    </div>
    <br />
    <br />
    <br />
    <div class="card-footer">
      @if (!ViewBag.IsContab || ViewBag.IsContabAdmin)
      {
        <a class="btn btn-primary" id="saveClassEdit">Salvar</a>
      }
    </div>
  </div>
}
@if (ViewBag.IsContab || ViewBag.IsContabAdmin)
{
  <div class="card" id="vincularContaDebito">
    <div class="card-header">
      <h5>Alterar Plano de contas</h5>
    </div>
    <div class="card-body">

    </div>
    <div class="card-footer">
      <button type="button" class="btn btn-outline-success" onclick="ClassificacaoDocumentos.SalvarPlanoConta()">Salvar</button>
    </div>
  </div>
}
@if (!ViewBag.IsContabAdmin)
{
  if (ViewBag.IsContab)
  {
    <script>
      console.log(@ViewBag.IsContabAdmin);
      $("#divHeader input").attr('disabled', true);
      $("#divHeader select").attr('disabled', true);
      
    </script>
  }
}
else
{
  <script>
    console.log(@ViewBag.IsContabAdmin);
    
    $("#divHeader #TipoPessoaSelectLookup").attr('disabled', true);
    $("#divHeader #TipoEntradaSelectLookup").attr('disabled', true);
  </script>
}

