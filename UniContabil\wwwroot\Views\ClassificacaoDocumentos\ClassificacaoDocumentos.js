﻿var ClassificacaoDocumentos = {};
var Tipo = 2;
let idclassificacao = 0;
let idcategoria = 0;

ClassificacaoDocumentos.SalvarPlanoConta = function () {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + "/ClassificacaoDocumentos/EditVinculo",
    tradicional: true,
    dataType: "json",
    data: $("#formAdc").serialize(),
    success: function (data) {
      if (!data.erro) {
        Alerta("Sucesso", data.message, "green")
      } else {
        Alerta("Sucesso", data.message, "red")
      }
    },
  })
}

ClassificacaoDocumentos.GetTipo = function () {
  var listcheck = $(".custom-radio");

  for (var i = 0; i < listcheck.length; i++) {
    if ($(".custom-radio")[i].checked)
      Tipo = $(".custom-radio")[i].value;

    return Tipo;
  }
}

ClassificacaoDocumentos.makeTreeItem = function (el) {
  return $("<a>", {
    id: $(el).attr("id") + "_anchor",
    class: "jstree-anchor",
    href: "#"
  });
}

//ClassificacaoDocumentos.Save = function (id) {
//  var model = {
//    Id: $("#Id").val(),
//    Descricao: $("#Descricao").val(),
//    DataLancamento: $("#DataLancamento").val(),
//    DataCriacao: $("#DataCriacao").val(),
//    Valor: $("#Valor").val(),
//    IdClassificacao: id,
//    IdFilial: $("#Filial").data("filial"),
//    IdOrganizacao: $("#Organizacao").data("org")
//  };

//  $.ajax({
//    url: GetURLBaseComplete() + "/ClassificacaoDocumentos/Save",
//    dataType: "json",
//    type: "POST",
//    data: {
//      model: model
//    },
//    success: function (data) {
//      if (!data.Erro) {
//        ClassificacaoDocumentos.AtualizaGrid(1, IdReloadGrid);
//        $("#InfoUserPartial").html("");
//        $("#Docs").hide();
//      }
//      else {
//      }
//    }
//  });
//}

ClassificacaoDocumentos.init = function () {

  $(document).on("dnd_stop.vakata", function (e, data) {
    var t = $(data.event.target);
    var targetNode = $('#tree').jstree(true).get_node($(data.event.target));
    if (targetNode) {
      let codClassificacao = $(data.data.obj[0]).children('a').attr('codclassificacao')
      let codCategoria = targetNode.a_attr.codCategoria
      if (codCategoria == 'undefined' || codCategoria == 0) {
        Alerta("Alerta", "Favor selecionar uma categoria válida", "yellow")
      } else {
        $.ajax({
          type: 'get',
          url: GetURLBaseComplete() + "/ClassificacaoDocumentos/TrocaClassificacaoCat?IdClassificacao=" + codClassificacao + "&IdCategoria=" + codCategoria,
          tradicional: true,
          dataType: "json",
          success: function (data) {
            if (!data.erro) {
              Alerta("Sucesso", data.message, "green")
              $('#tree').jstree(true).refresh();
            } else {
              Alerta("Alerta", data.message, "yellow")
            }
          },
          error: function (err) {
            console.log(err);
          }
        });
      }


      //if (targetNode.children) {
      //  if (targetNode.children.length != 0) {
      //    swal({
      //      title: "Atenção",
      //      text: "Não é possível incluir neste local.",
      //      icon: "warning",
      //    });
      //  } else {
      //    ClassificacaoNotasFiscais.Save(targetNode.a_attr.codClassificacao);
      //  }
      //}
    }
  });

  $(document).on("click", ".custom-radio", function () {
    Tipo = $(this).val();
    $("#editpartial").html("");
    $("#tree").jstree(true).refresh()
  });

  $(document).on('click', "#btnRecibo", function () {
    var id = $(this).attr('codigoLancamento');
    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/GeraReport/" + id,
      tradicional: true,
      dataType: "json",
      success: function (data) {
        if (!data.erro) {
          window.open(GetURLBaseComplete() + "/Anexo/Download/" + data.fileName, "_blank");
        } else {
          alert("Não foi possivel gerar o relatório, gentileza tentar novamente.");
        }
      },
      error: function (err) {
        console.log(err);
      }
    });
  });

  $(document).on("dblclick.jstree", function (e) {

    let node = $(e.target).closest("a");

    const attr = node[0].attributes;

    if (attr.codclassificacao.value != "null") {
      idclassificacao = attr.codclassificacao.value;

      $.ajax({
        url: GetURLBaseComplete() + "/ClassificacaoDocumentos/Edit",
        dataType: "html",
        type: "GET",
        data: {
          id: attr.codclassificacao.value
        },
        success: function (data) {
          $("#editpartial").html("");
          $("#editpartial").html(data);
          if ($("input[name='Tipo']:checked").val() == "1") {
            $("#reciboDespesaDiv").removeAttr("hidden")
          }
          $.ajax({
            url: GetURLBaseComplete() + "/ClassificacaoDocumentos/_GetEditPlanoConta?Id=" + attr.codclassificacao.value + "&idtipoPessoa=" + attr.tipopessoa.value,
            dataType: "html",
            type: "GET",
            success: function (data) {
              $("#vincularContaDebito .card-body").html('')
              $("#vincularContaDebito .card-body").html(data)
              
              MWERP.IniciaMascaras();
            }
          })
        }
      });
    }
    else if (attr.codcategoria.value != "null") {
      idcategoria = attr.codcategoria.value;

      $.ajax({
        url: GetURLBaseComplete() + "/CategoriaDocumento/Edit",
        dataType: "html",
        type: "GET",
        data: {
          id: attr.codcategoria.value
        },
        success: function (data) {
          $("#editpartial").html("");
          $("#editpartial").html(data);
          //$.ajax({
          //  url: GetURLBaseComplete() + "/ClassificacaoDocumentos/_GetEditPlanoConta?Id=" + attr.codclassificacao.value,
          //  dataType: "html",
          //  type: "GET",
          //  success: function (data) {
          //    $("#vincularContaDebito .card-body").html('')
          //    $("#vincularContaDebito .card-body").html(data)
          //    MWERP.IniciaMascaras();
          //  }
          //})
          MWERP.IniciaMascaras();
        }
      });
    }
  });

  $(document).on("click", "#saveCatCreate", function () {
    let url = GetURLBaseComplete() + "/CategoriaDocumento/Create";
    let model = $("#CreateCategoria").serialize();

    $.ajax({
      type: 'POST',
      url: url,
      tradicional: true,
      data: model,
      dataType: "json",
      success: function (data) {
        if (!data.erro) {
          //window.location.reload();
          ClassificacaoDocumentos.AtualizarTreeview();
          swal({
            title: "Sucesso",
            text: "Categoria criada com sucesso.",
            icon: "success",
          });

          $.ajax({
            url: GetURLBaseComplete() + "/CategoriaDocumento/Edit",
            dataType: "html",
            type: "GET",
            data: {
              id: data.cod
            },
            success: function (data) {
              $("#editpartial").html("");
              $("#editpartial").html(data);
            }
          });

        }
        else {
          swal({
            title: "Erro",
            text: data.mensage,
            icon: "error",
          });
        }
      },
      error: function (err) {
        console.log(err);
      }
    });

  });

  $(document).on("click", "#saveCatEdit", function () {
    let url = GetURLBaseComplete() + "/CategoriaDocumento/Edit";
    let model = $("#EditCategoria").serialize();
    $("#" + idcategoria).dblclick();
    $.ajax({
      type: 'POST',
      url: url,
      tradicional: true,
      data: model,
      dataType: "json",
      success: function (data) {
        if (!data.erro) {
          ClassificacaoDocumentos.AtualizarTreeview();
          swal({
            title: "Sucesso",
            text: "Categoria alterada com sucesso.",
            icon: "success",
          });
        }
        else {
          swal({
            title: "Erro",
            text: data.mensage,
            icon: "error",
          });
        }
      },
      error: function (err) {
        console.log(err);
      }
    });

  });

  $(document).on("click", "#saveClassCreate", function () {
    let url = GetURLBaseComplete() + "/ClassificacaoDocumentos/Create";
    let model = $("#CreateClassificacao").serialize();
    var id = 0;

    $.ajax({
      type: 'POST',
      url: url,
      tradicional: true,
      data: model,
      dataType: "json",
      success: function (data) {
        if (!data.erro) {
          //window.location.reload();
          ClassificacaoDocumentos.AtualizarTreeview();
          swal({
            title: "Sucesso",
            text: "Classificação criada com sucesso.",
            icon: "success",
          });

          id = data.cod;

          $.ajax({
            url: GetURLBaseComplete() + "/ClassificacaoDocumentos/Edit",
            dataType: "html",
            type: "GET",
            data: {
              id: id
            },
            success: function (data) {
              $("#editpartial").html("");
              $("#editpartial").html(data);
              $.ajax({
                url: GetURLBaseComplete() + "/ClassificacaoDocumentos/_GetEditPlanoConta?Id=" + id,
                dataType: "html",
                type: "GET",
                success: function (data) {
                  $("#vincularContaDebito .card-body").html('')
                  $("#vincularContaDebito .card-body").html(data)
                  MWERP.IniciaMascaras();
                }
              })
            }
          });

        }
        else {
          swal({
            title: "Erro",
            text: data.mensage,
            icon: "error",
          });
        }
      },
      error: function (err) {
        console.log(err);
      }
    });

  });

  $(document).on("click", "#saveClassEdit", function () {
    let url = GetURLBaseComplete() + "/ClassificacaoDocumentos/Edit";
    let model = $("#EditClassificacao").serialize();

    $.ajax({
      type: 'POST',
      url: url,
      tradicional: true,
      data: model,
      dataType: "json",
      success: function (data) {
        if (!data.erro) {
          ClassificacaoDocumentos.AtualizarTreeview();
        }
      },
      error: function (err) {
        console.log(err);
      }
    });

  });
};

ClassificacaoDocumentos.AtualizarTreeview = function () {
  $('#tree').jstree(true).refresh();
}

ClassificacaoDocumentos.LoadTree = function () {
  $("#tree").jstree({
    'core': {
      'check_callback': function (
        operation,
        node,
        node_parent,
        node_position,
        more
      ) {
        if (more.dnd != null && more.dnd == true) {
          if (operation === "move_node") {
            return false;
          } else if (operation === "delete_node") {
            return false;
          }
        }
      },
      'data': {
        'url': function () {
          return GetURLBaseComplete() + '/ClassificacaoDocumentos/_GetTreeview?Tipo=' + ClassificacaoDocumentos.GetTipo();
        },
        'dataType': 'json'
      },
      'themes': {
        'icons': false
      }
    },
    'plugins': ['contextmenu', "dnd", 'state'],
    'contextmenu': {
      items: function ($node) {
        let contextitens = {};
        console.log($node);
        if ($node.children.length > 0) {
          if (!$node.a_attr.codClassificacao && $node.a_attr.codCategoria) {
            contextitens = {
              "insert_item": {
                "label": "Criar Classificação",
                "action": function (obj) {
                  $.ajax({
                    url: GetURLBaseComplete() + "/ClassificacaoDocumentos/Create",
                    dataType: "html",
                    type: "GET",
                    success: function (data) {
                      $("#editpartial").html("");
                      $("#editpartial").html(data);
                      $("#IdCategoria").val($node.a_attr.codCategoria);
                      $("#IdTipoEntrada").val($node.a_attr.tipoEntrada);
                      $("#IdTipoPessoa").val($node.a_attr.tipoPessoa);
                      MWERP.IniciaMascaras();
                    }
                  });
                }
              },
              "delete_item": {
                "label": "Excluír Categoria",
                "action": function (obj) {
                  $.ajax({
                    url: GetURLBaseComplete() + "/CategoriaDocumento/DeleteCategoria/" + $node.a_attr.codCategoria,
                    dataType: "json",
                    type: "GET",
                    success: function (data) {
                      if (data.sucesso) {
                        $("#editpartial").html("");
                        swal({
                          title: "Sucesso",
                          text: "Categoria removida com sucesso.",
                          icon: "success",
                        });
                        ClassificacaoDocumentos.AtualizarTreeview();
                      } else {
                        swal({
                          title: "Erro",
                          text: data.mensagem,
                          icon: "error",
                        });
                      }
                    }
                  });
                }
              }
            };
          }
          else {
            contextitens = {
              "insert_item": {
                "label": "Criar Categoria",
                "action": function (obj) {
                  $.ajax({
                    url: GetURLBaseComplete() + "/CategoriaDocumento/Create",
                    dataType: "html",
                    type: "GET",
                    success: function (data) {
                      $("#editpartial").html("");
                      $("#editpartial").html(data);
                      $("#TipoEntrada").val(Tipo)
                      MWERP.IniciaMascaras();
                    }
                  });
                }
              }
            };
          }
        }
        else if ($node.a_attr.codClassificacao){
          contextitens = {
            "delete_item": {
              "label": "Excluír Classificação",
              "action": function (obj) {
                var id = $node.a_attr.codClassificacao;
                $.ajax({
                  url: GetURLBaseComplete() + "/ClassificacaoDocumentos/VerificaVinculoClassificacao/" + id,
                  dataType: "json",
                  type: "GET",
                  success: function (data) {
                    if (!data.erro) {
                      if (data.vinculo) {
                        swal({
                          title: "Atenção",
                          text: "Existem vínculos a esta classificação, eles serão removidos e os Documentos ficarão como NÃO CLASSIFICADOS. Deseja prosseguir?",
                          icon: "warning",
                          buttons: ["Não", "Sim"],
                          dangerMode: true,
                        })
                          .then((continuar) => {
                            if (continuar) {
                              $.ajax({
                                url: GetURLBaseComplete() + "/ClassificacaoDocumentos/Delete/" + id,
                                dataType: "json",
                                type: "GET",
                                success: function (data) {
                                  if (data.sucesso) {
                                    $("#editpartial").html("");
                                    swal({
                                      title: "Sucesso",
                                      text: "Classificação removida com sucesso.",
                                      icon: "success",
                                    });
                                    ClassificacaoDocumentos.AtualizarTreeview();
                                  } else {
                                    swal({
                                      title: "Erro",
                                      text: data.mensagem,
                                      icon: "error",
                                    });
                                  }
                                }
                              });
                            }
                          });
                      }
                      else {
                        $.ajax({
                          url: GetURLBaseComplete() + "/ClassificacaoDocumentos/Delete/" + id,
                          dataType: "json",
                          type: "GET",
                          success: function (data) {
                            if (data.sucesso) {
                              $("#editpartial").html("");
                              swal({
                                title: "Sucesso",
                                text: "Classificação removida com sucesso.",
                                icon: "success",
                              });
                              ClassificacaoDocumentos.AtualizarTreeview();
                            } else {
                              swal({
                                title: "Erro",
                                text: data.mensagem,
                                icon: "error",
                              });
                            }
                          }
                        });
                      }

                      ClassificacaoDocumentos.AtualizarTreeview();
                    } else {
                      swal({
                        title: "Erro",
                        text: data.mensagem,
                        icon: "error",
                      });
                    }
                  }
                });
                

                
              }
            }
          };
        }

        return contextitens;
      }
    }
  }).on("select_node.jstree", function (e, data) {
    IdReloadGrid = data.node.a_attr.codClassificacao;
  });
}

$(document).ready(function () {
  ClassificacaoDocumentos.init();
  ClassificacaoDocumentos.LoadTree();
});
