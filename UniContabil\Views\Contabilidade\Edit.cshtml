﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure
@using UniContabil.Infrastructure.Controls
@model ContabilidadeEditModel
@{
  var UrlBase = System.Configuration.ConfigurationManager.AppSettings["URLBase"];
  Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
  .hide-body {
    display: none !important;
  }

  .card-children {
    box-shadow: none !important;
  }
</style>
<script src="~/Views/Contabilidade/Contabilidade.js?ada=aa"></script>

<div class="card">

  <ul class="nav nav-tabs">
    <li class="nav-item">
      <a class="nav-link active" id="aba-users">Configurar Contabilidade</a>
    </li>
    <li class="nav-item">
      <a class="nav-link" id="aba-organizacoes">Adicionar Usuário</a>
    </li>
  </ul>

  <div class="card card-children" id="body-aba-users">
    <div class="row body" id="fundo">
      @if (Model.EnumStatus != null)
      {
        <div class="row">
          <div class="col-md-12">
            <h5>@(UniContabil.Infrastructure.EnumHelper.Description((EnumStatusContabilidade)Model.EnumStatus))</h5>
          </div>
        </div>
      }
      <div style="width: 100%; padding: 3%; padding-top: 0; padding-bottom: 0;">
        @using (Html.BeginForm("Edit", "Contabilidade", FormMethod.Post, new { @style = "text-align: end;", @id="formEditContabilidade" }))
        {
          <div class="row">
            @Html.HiddenFor(m => m.Id, new { id = "IdContab" })
            @Html.HiddenFor(m => m.EnumStatus)
            @Html.HiddenFor(m => m.IsAtivo)
            <div class="col-md-4" style="display: flex; align-items: flex-end; justify-content: space-between;">
              <div style="display:flex">
                <input type="radio" name="@Html.NameFor(x => x.IsPJ)" value="0" class="IsPJ" id="IsPJ_0" />
                <label style="margin-top: 3px; margin-left: 5px;">Pessoa Física</label>
              </div>
              <div style="display:flex">
                <input type="radio" name="@Html.NameFor(x => x.IsPJ)" class="IsPJ" value="1" id="IsPJ_1" />
                <label style="margin-top: 3px; margin-left: 5px;">Pessoa Jurídica</label>
              </div>
            </div>
            <div class="form-group col-md-2">
              <label id="lblCPF">CPF:</label>
              <input type="text" class="form-control " value="@Model.Cpf" name="@Html.NameFor(x => x.Cpf)" id="cpf" />
            </div>

            <div class="form-group col-3">
              <label>Nome:</label>
              <input type="text" class="form-control" value="@Model.Nome" name="@Html.NameFor(x => x.Nome)" />
            </div>

            <div class="form-group col-md-3">
              <label>Número CRC:</label>
              <input type="text" class="form-control" value="@Model.NrCRC" name="@Html.NameFor(x => x.NrCRC)" />
            </div>

          </div>
          <div class="row">
            <div class="form-group col-md-2">
              <label>CEP:</label>
              <input type="text" value="@Model.Cep" id="campoCEP" onkeyup="Contabilidade.completaCampos()" class="form-control CEP" name="@Html.NameFor(x => x.Cep)" />
            </div>
            @*<div class="form-group col-md-2">
      <label>Prefixo Logradouro:</label>
      <input type="text" value="@Model.PrefixLogradouro" class="form-control" name="@Html.NameFor(x => x.PrefixLogradouro)" />
    </div>*@
            <div class="form-group col-4">
              <label>Logradouro:</label>
              <input type="text" class="form-control" value="@Model.Logradouro" id="Logradouro" name="@Html.NameFor(x => x.Logradouro)" />
            </div>
            <div class="form-group col-4">
              <label>Bairro:</label>
              <input type="text" class="form-control" value="@Model.Bairro" id="Complemento" name="@Html.NameFor(x => x.Bairro)" />
            </div>
            <div class="form-group col-md-1">
              <label>Número:</label>
              <input type="text" value="@Model.NmrEndereco" class="form-control" name="@Html.NameFor(x => x.NmrEndereco)" />
            </div>
            <div class="form-group col-md-1">
              <label>Complemento:</label>
              <input type="text" class="form-control" value="@Model.Complemento" id="Complemento" name="@Html.NameFor(x => x.Complemento)" />
            </div>
          </div>

          <div class="row">
            <div class="form-group col-3" style="text-align: left;">
              <label for="EstadoSelectLookup">Estado:</label>
              @Html.Select2("UFSelect", "GetUFSelect", "Selecione o Estado", valor: Model.UFSelect == null ? "0" : Model.UFSelect.id, text: Model.UFSelect.text, classLabel: "UFSelect")
              @*<label for="EstadoSelectLookup">Estado:</label>
                <input data-val="true" id="EstadoSelect_id" name="UFSelect.id" type="hidden" value="@Model.UFSelect.id">
                <input data-val="true" id="EstadoSelect_text" name="UFSelect.text" type="hidden" value="@Model.UFSelect.text">
                <select id="EstadoSelectLookup" name="EstadoSelectLookup" tabindex="-1" class="SelectDisabled select2-hidden-accessible" aria-hidden="true"></select>
                <script>
                  MakeDropDown('EstadoSelect', GetURLBaseComplete() + '/Select2/GetUFSelect', 'Selecione o Estado:');
                </script>*@
            </div>
            <div class="form-group col-3" style="text-align: left;">
              <label for="MunicipioSelectLookup">Município:</label>
              @Html.Select2("MunicipioSelect", "GetMunicipioSelect", "Selecione a cidade", valor: Model.MunicipioSelect == null ? "0" : Model.MunicipioSelect.id, text: Model.MunicipioSelect.text, classLabel: "MunicipioSelect", afterSelect: "UFSelect")
              @*<input data-val="true" id="MunicipioSelect_id" name="MunicipioSelect.id" type="hidden" value="@Model.MunicipioSelect.id">
                <input data-val="true" id="MunicipioSelect_text" name="MunicipioSelect.text" type="hidden" value="@Model.MunicipioSelect.text">
                <select id="MunicipioSelectLookup" name="MunicipioSelectLookup" tabindex="-1" class="SelectDisabled select2-hidden-accessible" aria-hidden="true"></select>
                <script>
                  MakeDropDown('MunicipioSelect', GetURLBaseComplete() + '/Select2/GetMunicipioSelect', 'Selecione o Municipio:');
                </script>*@
            </div>
            <div class="form-group col-3">
              <label>Telefone Fixo:</label>
              <input type="text" value="@Model.TelefoneFixo" class="telefone form-control" name="@Html.NameFor(x => x.TelefoneFixo)" />
            </div>
            <div class="form-group col-3">
              <label>Email:</label>
              <input type="text" value="@Model.Email" class="EMAIL form-control" name="@Html.NameFor(x => x.Email)" />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-3">
              <label>Telefone Celular:</label>
              <input type="text" value="@Model.TelefoneCelular" class="telefone form-control" name="@Html.NameFor(x => x.TelefoneCelular)" />
            </div>
            @*<div style="display:flex; align-items:flex-end;">
              <div style="display:flex">
                @Html.CheckBoxFor(x => x.IsAtivo, new { @disabled = "disabled" })
                <label style="margin-top: 3px; margin-left: 5px;">Usuário Ativo</label>
              </div>
            </div>*@
          </div>
          <div class="row" style="display: flex; justify-content: flex-end; ">
            @if (ContextoUsuario.UserLogged.Contabilidade != null && ContextoUsuario.UserLogged.Contabilidade.adm)
            {
              @if (Model.EnumStatus != (int)EnumStatusContabilidade.Aprovado && Model.EnumStatus != (int)EnumStatusContabilidade.EmAprovacao)
              {
                <button class="btn btn-outline-dark" onclick="Contabilidade.EnviarAprovacao()" type="button" style="margin-right:10px !important;">Enviar para aprovação</button>
              }
              <button type="submit" value="Salvar" class="btn btn-outline-success" style="margin-right: 10px !important;">Salvar</button>
            }
          </div>
        }
      </div>
    </div>
    <div class="row col-md-12" id="anexos">
      <iframe name="dummyframe" id="dummyframe" style="display: none;"></iframe>
      @if (ContextoUsuario.UserLogged.Contabilidade != null && ContextoUsuario.UserLogged.Contabilidade.adm)
      {
        using (Html.BeginForm("SalvaAnexo", "Contabilidade", FormMethod.Post, new { id = "FormAnexo", enctype = "multipart/form-data" }))
        {
          <input type="file" hidden id="fileAnexo" name="File" />
          <input type="hidden" name="IdContabilidade" id="IdContabilidade" value="@(Model.Id)" />
        }
      }

      <div class="card col-md-12">
        <div class="card-header">
          @if (ContextoUsuario.UserLogged.Contabilidade != null && ContextoUsuario.UserLogged.Contabilidade.adm)
          {
            <div class="col-md-3">
              <button class="btn btn-outline-primary" onclick="$('#fileAnexo').trigger('click')">Adicionar Anexo</button>
            </div>
          }
        </div>
        <div class="card-body" id="tabelaAnexos">

        </div>
      </div>
    </div>
  </div>
  <div class="card card-children hide-body" id="body-aba-organizacoes">
    <div class="row body" id="fundo">
      <div class="row">
        <div class="col-md-12">
          <h5>@(UniContabil.Infrastructure.EnumHelper.Description((EnumStatusContabilidade)Model.EnumStatus))</h5>
        </div>
      </div>
      <div style="width: 100%; padding: 3%; padding-top: 0; padding-bottom: 0;">
        @*<div class="row">
            <div class="form-group col-md-3">
              <label>CPF:</label>
              <input type="text" class="form-control CPF" id="cpfAdicionar" placeholder="Digite o CPF" />
            </div>
            <div class="form-group col-md-3">
              <label style=" margin-left: 13px;">Administrador</label>
              <input type="checkbox" style=" margin-top: -10px;" id="isAdm" />
            </div>
          </div>*@
        <div class="row">
          <div class="form-group" style=" margin-top: 10px; margin-left: 8px;">
            @if (ContextoUsuario.UserLogged.Contabilidade != null && ContextoUsuario.UserLogged.Contabilidade.adm)
            {
              <button class="btn btn-outline-success btn-sm" onclick="Contabilidade.AdicionarUsuarioContabilidade()">Adicionar</button>
            }
          </div>
        </div>
        <div class="row">
          <div class="card col-md-12">
            <div class="card-body" id="tabelaUsers">

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="ModalVerAnexo" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document" style="max-width: 1000px !important;">
    <div class="modal-content" style="min-height:calc(100vh - 80px);">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Ver Anexo:</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="col-md-12">
          <iframe id="iframeVerAnexo" style="min-height: calc(100vh - 145px);" class="col-md-12"></iframe>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="ModalCriarUsuario" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document" style="max-width: 1000px !important;">
    <div class="modal-content" style="min-height:calc(100vh - 80px);">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Vincular usuário à contabilidade:</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">

      </div>
    </div>
  </div>
</div>

@if (ContextoUsuario.UserLogged.Contabilidade != null && !ContextoUsuario.UserLogged.Contabilidade.adm
|| Model.EnumStatus == (int)EnumStatusContabilidade.EmAprovacao
)
{
  <script>
    $("#fundo input").attr("disabled", "disabled");
    $("#fundo select").attr("disabled", "disabled");
  </script>
}