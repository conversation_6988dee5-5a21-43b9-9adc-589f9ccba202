﻿<div class="jstree-permissions" style="margin-top: 15px">

</div>

@*<script>
  $(document).ready(function () {
    let idGrupo = $(".list-group li.active").data("grupo");

    if (!idGrupo)
      return;

    $.get(GetURLBaseComplete() + "/GrupoUsuario/ListPermissoesGrupo/" + idGrupo, function (data) {
      data = JSON.parse(data);
      if (data.Sucesso) {
        $('.jstree-permissions')
          .on('check_node.jstree', function (event, element) {
            Alerta("Info", "Salvando alterações...", "blue", 2000);

            const obj = {
              IdItem: element.node.id,
              IdGrupo: idGrupo
            };

            $.post(GetURLBaseComplete() + "/GrupoUsuario/CreatePermissao", obj, function (data) {
              data = JSON.parse(data);
              if (data.Sucesso)
                Alerta("Sucesso", "Permissão adicionada com sucesso.", "green");
              else
                <PERSON>erta("Erro", data.Mensagem, "red");
            });
          })
          .on('uncheck_node.jstree', function (event, element) {
            Alerta("Info", "Salvando alterações...", "blue", 2000);

            const obj = {
              IdItem: element.node.id,
              IdGrupo: idGrupo
            };

            $.post(GetURLBaseComplete() + "/GrupoUsuario/DeletePermissao", obj, function (data) {
              data = JSON.parse(data);
              if (data.Sucesso)
                Alerta("Sucesso", "Permissão removida com sucesso.", "green");
              else
                Alerta("Erro", data.Mensagem, "red");
            });
          })
          .jstree({
            'core': {
              'data': data.Treeview
            },
            "checkbox": {
              'tie_selection': false,
              'whole_node': false,
            },
            "plugins": ["checkbox"]
          });
      }
      else {
        Alerta("Erro", data.Mensagem, "red", 5000);
      }
    });
  });
</script>*@
