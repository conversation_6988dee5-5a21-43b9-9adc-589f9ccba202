﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;
using UniContabil.Infrastructure.Exceptions;


namespace UniContabil.Controllers
{
  public class ProdutoController : LibController
  {
    public ProdutoController()
    { }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        IPagedList<ProdutoModel> List = new ProdutoServices(ContextoUsuario.UserLogged).GetPagedListC(page);
        ViewBag.PageItems = List;
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View(new ProdutoModel());
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(ProdutoModel produtoModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          if (produtoModel.CodigoBarras == null)
          {
            produtoModel.CodigoBarras = "";
          }
          if (produtoModel.UnidadeMedida == null)
          {
            produtoModel.UnidadeMedida = "";
          }
          new ProdutoServices(ContextoUsuario.UserLogged).Create(produtoModel);
          return RedirectToAction("Index");
        }
        else
        {
          return View(produtoModel);
        }
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(produtoModel);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(int id)
    {
      try
      {
        ProdutoModel produtoModel = new ProdutoModel().FromDatabase(new ProdutoServices().GetById(id));

        if (produtoModel == null)
        {
          return NotFound();
        }

        return View(produtoModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(ProdutoModel produtoModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          if (produtoModel.CodigoBarras == null)
          {
            produtoModel.CodigoBarras = "";
          }
          if (produtoModel.UnidadeMedida == null)
          {
            produtoModel.UnidadeMedida = "";
          }
          if (produtoModel.Ncm == null)
          {
            produtoModel.Ncm = "";
          }

          new ProdutoServices(ContextoUsuario.UserLogged).Edit(produtoModel);
          return RedirectToAction(nameof(Index));
        }
        return View(produtoModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(produtoModel);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string Delete(int id)
    {
      try
      {
        ProdutoServices Service = new ProdutoServices();
        var model = Service.GetById(id);
        
        Service.Delete(model);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {

        if(Ex.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint")) { 
          return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Este item já possui historico de movimentação então não é possivel exclui-lo", Ex.HResult) });

        }
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }

    [HttpGet]
    public ActionResult GetOrganizacaoSelect(string term)
    {
      OrganizacaoServices Service = new OrganizacaoServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
    [HttpGet]
    public ActionResult GetFilialSelect(string term)
    {
      FilialServices Service = new FilialServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
    [HttpGet]
    public ActionResult GetGrupoProdutoSelect(string term)
    {
      GrupoProdutoServices Service = new GrupoProdutoServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
    [HttpGet]
    public ActionResult GetSubGrupoProdutoSelect(string term)
    {
      SubGrupoProdutoServices Service = new SubGrupoProdutoServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }

    [HttpGet]
    //[NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.GridDetalhesCliente)]
    public PartialViewResult _GridProduto(string Campo1)
    {
      ProdutoServices produtoServices = new ProdutoServices();
      List<GridProduto> ListaDetalhesProduto = produtoServices.GetGridProduto(Campo1);
      return PartialView("InfoAddProduto/_GridProduto", ListaDetalhesProduto);
    }

    [HttpGet]
    //[NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.GridDetalhesCliente)]
    public PartialViewResult _GridDetalhesProduto(int idProduto)
    {
      ProdutoServices produtoServices = new ProdutoServices();
      GridDetalhesProduto gridDetalhesProduto = produtoServices.GetGridDetalhesProduto(idProduto);
      return PartialView("InfoAddProduto/_GridDetalhesProduto", gridDetalhesProduto);
    }
  }
}
