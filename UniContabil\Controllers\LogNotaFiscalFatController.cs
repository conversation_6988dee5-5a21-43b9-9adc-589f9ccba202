﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using UniContabil.Infrastructure;

using UniContabilEntidades.Models;
using UniContabilDomain.Services;
using Microsoft.AspNetCore.Mvc;
using System;

namespace UniContabil.Controllers
{
  public class LogNotaFiscalFatController : LibController
  {
    private LogNotaFatServices LogNotaFatServices
    {
      get
      {
        if (_LogNotaFatServices == null)
          _LogNotaFatServices = new LogNotaFatServices(ContextoUsuario.UserLogged);

        return _LogNotaFatServices;
      }
    }
    private LogNotaFatServices _LogNotaFatServices;

    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.GridLogNotaFiscalVenda)]
    public ActionResult Index(int id)
    {
      try
      {
        LogNotaFatModelIndex model = LogNotaFatServices.GetByIdNota(id);

        return View(model);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }
  }
}
