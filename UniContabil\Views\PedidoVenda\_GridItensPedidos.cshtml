﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure
@model List<ItensPedidoVendaModel>

@if (Model.Count == 0)
{
  <div class="row" style="justify-content: center;">
    <p style="color: red;">Você ainda não adicionou nenhum Item de Venda</p>
  </div>
}
else
{

  <div class="table-group-content">
    <table>
      <thead>
        <tr>
          <th scope="col">
            Unidade
          </th>
          <th scope="col">
            Quantidade
          </th>
          <th scope="col">
            Valor Unitário
          </th>
          <th scope="col">
            Valor Total Item
          </th>
          <th scope="col">
            Desconto %
          </th>
          <th scope="col">
            Desconto R$
          </th>
          <th scope="col">
          </th>
        </tr>
      </thead>
      <tbody>
        @foreach (var item in Model)
        {
          <tr>
            <td>
              @item.Unid
            </td>
            <td>
              @item.Qtde
            </td>
            <td>
              @item.ValorUnit
            </td>
            <td>
              @item.ValorTotalItem
            </td>
            <td>
              @item.DescontoPerc
            </td>
            <td>
              @item.Desconto
            </td>
            <td >
              @if (ContextoUsuario.HasPermission("ItensPedidoVenda", TipoFuncao.Edit))
              {
                <a class="link link-secondary EditarItemVenda" data-codigo="@item.CodigoModal"> Editar</a>
              }
              @if (ContextoUsuario.HasPermission("ItensPedidoVenda", TipoFuncao.Delete))
              {
                <a class=" link link-danger RemoverItemVenda" data-codigo="@item.CodigoModal">Apagar</a>
              }
            </td>
          </tr>
        }
      </tbody>
    </table>
    </div>
    }
