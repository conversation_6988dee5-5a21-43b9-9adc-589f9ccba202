﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure
@model List<ItensPedidoCompraModel>

@if (Model.Count == 0)
{
  <div class="row" style="justify-content: center;">
    <p style="color: red;">Você ainda não adicionou nenhum Item de Compra</p>
  </div>
}
else
{

  <div class="table-group-content">
    <table >
      <thead>
        <tr>
          <th scope="col">
            Sequencia
          </th>
          <th scope="col">
            Produto
          </th>
          <th scope="col">
            Unidade
          </th>
          <th scope="col">
            Quantidade
          </th>
          <th scope="col">
            Valor Unitário
          </th>
          <th scope="col">
            Valor Total Item
          </th>
          <th scope="col">
            Desconto %
          </th>
          <th scope="col">
            Desconto R$
          </th>
          <th scope="col">
          </th>
        </tr>
      </thead>
      <tbody>
        @foreach (ItensPedidoCompraModel itensPedidoCompraModel in Model)
        {
          <tr>
            <td>
              @itensPedidoCompraModel.SequenciaItem
            </td>
            <td>
              @itensPedidoCompraModel.ProdutoSelect.text
            </td>
            <td>
              @itensPedidoCompraModel.Unidade
            </td>
            <td>
              @itensPedidoCompraModel.Quantidade
            </td>
            <td>
              @itensPedidoCompraModel.ValorUnitario
            </td>
            <td>
              @itensPedidoCompraModel.ValorTotalItem
            </td>
            <td>
              @itensPedidoCompraModel.DescontoPercentual
            </td>
            <td>
              @itensPedidoCompraModel.Desconto
            </td>
            <td>
              @if (ContextoUsuario.HasPermission("ItensPedidoCompra", TipoFuncao.Edit))
              {
                <a class="link link-secondary EditarItemCompra" data-codigo="@itensPedidoCompraModel.CodigoModal">Editar</a>
              }
              @if (ContextoUsuario.HasPermission("ItensPedidoCompra", TipoFuncao.Delete))
              {
                <a class=" link link-danger RemoverItemCompra" data-codigo="@itensPedidoCompraModel.CodigoModal">Apagar</a>
              }
            </td>
          </tr>
        }
      </tbody>
    </table>
    </div>

    }
