﻿@using UniContabil.Infrastructure.Controls
@model UniContabilEntidades.Models.RecuperaAcc
@{
  ViewBag.Title = "Esqueci a senha";
  Layout = "~/Views/Shared/_LayoutAccount.cshtml";
}

<link rel="stylesheet" href="~/Views/Account/Account.css?awa=ads" />
<script src="~/Views/Account/Account.js"></script>
<div>
  <div class="card">
    <div class="col-md-3 divcard">
      <h4>RECUPERE SUA CONTA</h4>
      @using (Html.BeginForm("ForgotPassword", "Account", FormMethod.Post))
      {
        <input type="hidden" value="" name="@Html.NameFor(x => x.UrlBase)" id="Url" />
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              @Html.TextBoxFor(m => m.CPF, new { type = "text", @class = "CPF form-control", placeholder = "CPF" })
              @Html.ValidationMessageFor(m => m.CPF)
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              @Html.TextBoxFor(m => m.Email, new { type = "text", @class = "email form-control", placeholder = "Email" })
              @Html.ValidationMessageFor(m => m.Email)
            </div>
          </div>
        </div>
        @*<div class="row">
            <div class="col-md-12">
              <div class="form-group">
                @Html.PasswordFor(m => m.Senha, new { @class = "form-control", placeholder = "Senha" })
                @Html.ValidationMessageFor(m => m.Senha)

              </div>
            </div>
          </div>*@
        @*<div class="row">
            <div class="col-md-12">
              <div class="form-group" style="float:left !important">
                <input type="checkbox" class="form-control" />
                Continuar Conectado
              </div>
            </div>
          </div>*@
        <div class="row" style="justify-content: center">
          <button class="btn" style="background-color: #2375aa;
                                    font-size: 15pt !important;
                                    color: white;" type="submit">
            Enviar
          </button>
        </div>
      }
      @*<div class="row" style="justify-content: center">
          <a class="" href="#">Esqueceu sua senha?</a>
        </div>*@
      <div class="row" style="justify-content: center">
        <a class="" href="@Url.Action("Register","Account")">Não possuo uma conta.</a>
      </div>
    </div>
  </div>
</div>
<style>
  .divcard {
    position: fixed;
    right: 5%;
    top: 31%;
    width: 100%;
    height: 50vh;
    text-align: center;
  }

  .form-group {
    margin-bottom: 1rem !important;
  }

  input {
    background-color: #eef4f3 !important;
    height: calc(2.75rem + 2px) !important;
    color: #818181 !important;
    font-size: 15pt !important;
  }

  h4 {
    color: #2e7daf;
    font-family: monospace;
  }

  body {
    background-image: url('../Content/img/Imagem home unicontabil.png'), url('../Content/img/Logo_Unicontabil.jpeg'), url('../Content/img/fundo.svg') !important;
    background-repeat: no-repeat !important;
    background-size: 40%, 30%, 100% !important;
    background-position-x: 15%, 95% !important;
    background-position-y: 0%, -50% !important;
  }
</style>