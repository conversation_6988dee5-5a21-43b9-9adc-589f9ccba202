﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using HtmlAgilityPack;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Globalization;
using System.Linq;
using System.IO;
using System.Net;
using System.Threading.Tasks;
using UniContabilDomain.Services;
using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;
using System.Threading;

namespace UniContabil
{
  [ApiController]
  [Route("WSUni/[action]")]
  [Consumes("application/json")]
  public class WSUni : ControllerBase
  {
    private List<string> GetRetornosSiteFazenda()
    {
      return new List<string>()
      {
        "The remote server returned an error: (404) Not Found.",
        "An error occurred while sending the request. Unable to read data from the transport connection: Foi forçado o cancelamento de uma conexão existente pelo host remoto..",
        "The SSL connection could not be established, see inner exception. Authentication failed, see inner exception."
      };
    }

    [HttpPost]
    [AllowAnonymous]
    public ActionResult Login(LoginModel login)
    {
      try
      {
        login.CPF = login.CPF.Replace(".", "").Replace("-", "");
        E_Usuarios user = new AccountServices().Login(login);
        RetonoLogin retorno = new RetonoLogin { Erro = false, Message = "Login realizado com sucesso.", CodUser = user.U_Id.ToString() };

        retorno.ListOrg = new OrganizacaoUsuarioServices().GetOrgByUser(user.U_Id);

        retorno.ListFilial = new List<FilialOrgUser>();

        foreach (var item in retorno.ListOrg)
        {
          List<FilialOrgUser> list = new OrganizacaoUsuarioServices().GetFilialByUser(user.U_Id, item.Cod);

          foreach (var item1 in list)
          {
            List<MedConvidadoIndex> listmed = new MedConvidadoService().GetByCPF(item1.Cod);
            if (listmed.Count > 0)
            {
              retorno.ListConvite.Add(new ListConviteClinica()
              {
                IdFilial = item1.Cod,
                ListConviteMed = listmed
              });
            }

            retorno.ListFilial.Add(item1);
          }
        }

        return Ok(retorno);
      }
      catch (Exception ex)
      {
        RetornoBase retorno = new RetornoBase { Erro = true, Message = ex.Message };

        return Ok(retorno);
      }
    }

    [HttpPost]
    [AllowAnonymous]
    public ActionResult AddLancamento(LancamentoApp lancamento)
    {
      try
      {
        lancamento.Descricao = lancamento.Descricao ?? String.Format("Lançamento em {0}", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));
        int Cod = new LancamentosServices().CreateApp(lancamento, ConfigurationManager.AppSettings["CaminhoDocs"]);

        RetonoLancamento retorno = new RetonoLancamento { Erro = false, Message = "Salvo com sucesso.", CodUser = lancamento.CodUser, CodLancamento = Cod };

        return Ok(retorno);
      }
      catch (Exception ex)
      {
        RetornoBase retorno = new RetornoBase { Erro = true, Message = ex.Message };

        return Ok(retorno);
      }
    }

    [HttpPost]
    public ActionResult EditApp(LancamentoApp lancamento)
    {
      try
      {
        new LancamentosServices().EditApp(lancamento);
        RetornoBase retorno = new RetornoBase();
        retorno.Erro = false;
        retorno.Message = "Lançamento salvo com sucesso.";
        return Ok(retorno);
      }
      catch (Exception ex)
      {
        RetornoBase retorno = new RetornoBase();
        retorno.Erro = true;
        retorno.Message = ex.Message;
        return Ok(retorno);
      }
    }

    [HttpGet]
    public ActionResult GetDocsNClassificados(string User, int filial)
    {
      try
      {
        List<DocsLancamento> lancamentos = new List<DocsLancamento>();
        RetornoWSUniContabil retorno = new RetornoWSUniContabil(false, "", new LancamentosServices().GetDocsNClassificado(Guid.Parse(User), filial));
        return Ok(retorno);
      }
      catch (Exception ex)
      {
        RetornoBase retorno = new RetornoBase { Erro = true, Message = ex.Message };

        return Ok(retorno);
      }
    }

    [HttpGet]
    public ActionResult GetFormaPagamentoSelect()
    {
      FormaPagamentoServices formaPagamentoServices = new FormaPagamentoServices();
      List<Select2Model> ListaFormaPagamento = new List<Select2Model>();
      ListaFormaPagamento = formaPagamentoServices.GetByTerm(null);
      return Ok(ListaFormaPagamento);
    }

    [HttpGet]
    public ActionResult GetCartaoCreditoSelect(int idfilial)
    {
      UsuarioLogado user = new UsuarioLogado();
      user.IdFilial = idfilial;

      List<Select2Model> Lista = new CartaoDeCreditoServices(user).GetByTerm(null);
      return Ok(Lista);
    }

    [HttpGet]
    public ActionResult GetBancoSelect(int idfilial)
    {
      UsuarioLogado user = new UsuarioLogado();
      user.IdFilial = idfilial;

      List<Select2Model> Lista = new BancoServices(user).GetByTerm(null);
      return Ok(Lista);
    }

    [HttpGet]
    public ActionResult GetClassificacao(int filial, int tipo)
    {
      try
      {
        List<CategoriaApp> list = new ClassificacaoDocumentosServices().GetClassificacaoApp(filial, tipo);
        RetornoWSUniContabil retorno = new RetornoWSUniContabil(false, "", list);
        return Ok(retorno);
      }
      catch (Exception ex)
      {
        RetornoWSUniContabil retorno = new RetornoWSUniContabil(true, ex.Message, null);
        return Ok(retorno);
      }
    }

    [HttpPost]
    public ActionResult GetClientes(WSPesquisa WS)
    {
      try
      {
        List<ClienteWS> result = new WSUniContabilServices().GetClienteWS(WS);
        return Ok(new RetornoWSUniContabil(false, "", result));
      }
      catch (Exception Ex)
      {
        return Ok(new RetornoWSUniContabil(true, Ex.Message, null));
      }
    }

    [HttpGet]
    public ActionResult GetProduto(int filial)
    {
      try
      {
        List<ProdutoWS> result = new WSUniContabilServices().GetProdutoWS(filial);
        return Ok(new RetornoWSUniContabil(false, "", result));
      }
      catch (Exception Ex)
      {
        return Ok(new RetornoWSUniContabil(true, Ex.Message, null));
      }
    }

    public ActionResult GetNroDocsNClassificados(string User, int filial)
    {
      try
      {
        List<DocsLancamento> lancamentos = new List<DocsLancamento>();
        int QtdeDocs = new LancamentosServices().GetDocsNClassificado(Guid.Parse(User), filial).Count();

        return Ok(QtdeDocs);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpGet]
    public ActionResult GetDadosQr(string qr)
    {
      try
      {
        CupomFiscal cupomFiscal = new CupomFiscal();

        ServicePointManager.ServerCertificateValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;

        HttpWebRequest Request = (HttpWebRequest)WebRequest.Create(qr);
        Request.Method = "GET";
        Request.ContentType = "text/html; charset=UTF-8";

        HttpWebResponse HttpResponse = new HttpWebResponse();

        try
        {
          HttpResponse = (HttpWebResponse)Request.GetResponse();
        }
        catch (Exception ex)
        {
          if (qr.Contains("https://") && ex.Message.Equals("The SSL connection could not be established, see inner exception. Authentication failed, see inner exception."))
          {
            qr = qr.Replace("https://", "http://");
            Request = (HttpWebRequest)WebRequest.Create(qr);
            Request.Method = "GET";
            Request.ContentType = "text/html; charset=UTF-8";

            HttpResponse = new HttpWebResponse();
            HttpResponse = (HttpWebResponse)Request.GetResponse();
          }
          else
            throw;
        }


        using (StreamReader StreamReader = new StreamReader(HttpResponse.GetResponseStream()))
        {
          string Result = StreamReader.ReadToEnd();

          HtmlDocument doc = new HtmlDocument();
          doc.LoadHtml(Result);

          #region [Cabeçalho]
          HtmlNodeCollection valorTotal = doc.DocumentNode.SelectNodes("//*[@id='formPrincipal:content-template-consulta']/div[1]/div[3]/div[2]/strong");
          cupomFiscal.ValorTotal = valorTotal == null ? 0 : decimal.Parse(valorTotal[0].InnerText, new CultureInfo("en"));

          HtmlNodeCollection valorPago = doc.DocumentNode.SelectNodes("//*[@id='formPrincipal:content-template-consulta']/div[1]/table[3]/tbody/div[1]/div[2]/strong");
          cupomFiscal.ValorPago = valorPago == null ? 0 : decimal.Parse(valorPago[0].InnerText, new CultureInfo("en"));

          HtmlNodeCollection qtdItens = doc.DocumentNode.SelectNodes("//*[@id='formPrincipal:content-template-consulta']/div[1]/div[2]/div[2]/strong");
          cupomFiscal.QuantidadeItens = decimal.Parse(qtdItens[0].InnerText);

          HtmlNodeCollection formaPagamento = doc.DocumentNode.SelectNodes("//*[@id='formPrincipal:content-template-consulta']/div[1]/table[3]/tbody/div[2]/div[2]/strong");
          cupomFiscal.FormaPagamento = formaPagamento == null ? "00" : formaPagamento[0].InnerText.Split('-')[0].Trim();

          HtmlNodeCollection dataEmissao = doc.DocumentNode.SelectNodes("//*[@id='collapse4']/table[3]/tbody/tr/td[4]");
          cupomFiscal.DataEmissao = dataEmissao == null ? (DateTime?)null : Convert.ToDateTime(dataEmissao[0].InnerText.Trim(), new CultureInfo("pt-BR"));
          #endregion [Cabeçalho]

          #region [Consumidor]
          HtmlNodeCollection clientes = doc.DocumentNode.SelectNodes("//*[@id='collapse5']/table/tbody/tr");
          foreach (HtmlNode cliente in clientes)
          {
            HtmlDocument clienteHtml = new HtmlDocument();
            clienteHtml.LoadHtml(cliente.InnerHtml);
            HtmlNodeCollection propriedades = clienteHtml.DocumentNode.SelectNodes("//td");

            if (propriedades.Count == 3)
            {
              cupomFiscal.Clientes.Add(new Cliente()
              {
                Nome = propriedades[0].InnerText.Trim(),
                CPF = propriedades[1].InnerText.Trim(),
                UF = propriedades[2].InnerText.Trim(),
              });
            }
          }
          #endregion [Consumidor]

          #region [Emitente]
          HtmlNodeCollection razaoSocial = doc.DocumentNode.SelectNodes("//*[@id='collapse4']/table[1]/tbody/tr/td[1]");
          cupomFiscal.Emitente.RazaoSocial = razaoSocial == null ? "" : razaoSocial[0].InnerText;

          HtmlNodeCollection cnpj = doc.DocumentNode.SelectNodes("//*[@id='collapse4']/table[1]/tbody/tr/td[2]");
          cupomFiscal.Emitente.CNPJ = cnpj == null ? "" : cnpj[0].InnerText;
          #endregion [Emitente]

          #region [Produtos]
          HtmlNodeCollection produtos = doc.DocumentNode.SelectNodes("//*[@id='myTable']/tr");
          foreach (HtmlNode produto in produtos)
          {
            HtmlDocument produtoHtml = new HtmlDocument();
            produtoHtml.LoadHtml(produto.InnerHtml);
            HtmlNodeCollection propriedades = produtoHtml.DocumentNode.SelectNodes("//td");

            cupomFiscal.Produtos.Add(new Produto()
            {
              Nome = propriedades[0].InnerText.Trim(),
              Quantidade = Decimal.Parse(propriedades[1].InnerText.Replace("Qtde total de ítens:", ""), new CultureInfo("en")),
              Unidade = propriedades[2].InnerText.Replace("UN:", "").Trim(),
              Valor = Decimal.Parse(propriedades[3].InnerText.Replace("Valor total R$: ", "").Replace("R$", ""), new CultureInfo("pt-BR"))
            });
          }
          #endregion [Produtos]

        }

        cupomFiscal.erro = false;

        return Ok(cupomFiscal);
      }
      catch (Exception Ex)
      {
        CupomFiscal cupomFiscal = new CupomFiscal();

        if (GetRetornosSiteFazenda().Contains(Ex.Message))
        {
          cupomFiscal.mensage = "Favor tentar novamente mais tarde. Site da Fazenda está fora do ar.";
          cupomFiscal.erro = true;
          return Ok(cupomFiscal);
        }

        cupomFiscal.mensage = "Algo deu errado, favor contatar a administração.";
        cupomFiscal.erro = true;
        return Ok(cupomFiscal);
      }

    }
  }
}
