﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.************-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.************-23
 */

using System.Collections.Generic;
using System.Linq;
using UniContabil.Infrastructure;
using UniContabilEntidades.Models;
using UniContabilDomain.Services;
using Microsoft.AspNetCore.Mvc;

namespace TeleConsulta.Controllers
{
  public class Select2Controller : LibController
  {
    [HttpGet]
    public ActionResult GetFilialSelect(string term)
    {
      FilialServices filialServices = new FilialServices(ContextoUsuario.UserLogged);
      List<Select2Model> ListaFilial = new List<Select2Model>();
      ListaFilial = filialServices.GetByTerm(term);
      return Json(new { items = ListaFilial });
    }

    [HttpGet]
    public ActionResult GetClienteSelect(string term)
    {
      ClienteServices clienteServices = new ClienteServices(ContextoUsuario.UserLogged);
      List<Select2Model> ListaCliente = new List<Select2Model>();
      ListaCliente = clienteServices.GetByTerm(term);
      return Json(new { items = ListaCliente });
    }

    [HttpGet]
    public ActionResult GetFormaPagamentoSelect(string term)
    {
      FormaPagamentoServices formaPagamentoServices = new FormaPagamentoServices(ContextoUsuario.UserLogged);
      List<Select2Model> ListaFormaPagamento = new List<Select2Model>();
      ListaFormaPagamento = formaPagamentoServices.GetByTerm(term);
      return Json(new { items = ListaFormaPagamento });
    }

    [HttpGet]
    public ActionResult GetCondicaoPagamentoSelect(string term)
    {
      CondicaoPagamentoServices condicaoPagamentoServices = new CondicaoPagamentoServices(ContextoUsuario.UserLogged);
      List<Select2Model> ListaCondicaoPagamento = new List<Select2Model>();
      ListaCondicaoPagamento = condicaoPagamentoServices.GetByTerm(term);
      return Json(new { items = ListaCondicaoPagamento });
    }

    [HttpGet]
    public ActionResult GetOrganizacaoSelect(string term)
    {
      OrganizacaoServices organizacaoServices = new OrganizacaoServices(ContextoUsuario.UserLogged);
      List<Select2Model> ListaFilial = new List<Select2Model>();
      ListaFilial = organizacaoServices.GetByTerm(term);
      return Json(new { items = ListaFilial });
    }

    [HttpGet]
    public ActionResult GetProdutoSelect(string term)
    {
      ProdutoServices produtoServices = new ProdutoServices(ContextoUsuario.UserLogged);
      List<Select2Model> ListaProdutos = new List<Select2Model>();
      ListaProdutos = produtoServices.GetByTerm(term);
      return Json(new { items = ListaProdutos });
    }

    [HttpGet]
    public ActionResult GetSeriesSelect(string term)
    {
      SeriesNotasServices SeriesNotasServices = new SeriesNotasServices(ContextoUsuario.UserLogged);
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = SeriesNotasServices.GetByTerm(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetLocalEstoqueSelect(string term)
    {
      LocalEstoqueServices localEstoqueServices = new LocalEstoqueServices(ContextoUsuario.UserLogged);
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = localEstoqueServices.GetByTerm(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetUsuariosSelect(string term)
    {
      List<Select2Model> Lista = new UsuariosServices(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }


    [HttpGet]
    public ActionResult GetOrganizacaoByUserSelect(string term)
    {
      List<Select2Model> Lista = new ConfigureServices(ContextoUsuario.UserLogged).GetLookupUser(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetUsuarioOrganizacaoSelect(string term)
    {
      List<Select2Model> Lista = new OrganizacaoServices(ContextoUsuario.UserLogged).GetByUsuarioTerm(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetUsuarioFilialSelect(string term, string id)
    {
      if (string.IsNullOrEmpty(id))
        return Json(new { items = new List<Select2Model>() });

      List<Select2Model> Lista = new FilialServices(ContextoUsuario.UserLogged).GetByUsuarioTerm(term, id);

      if (Lista.Count == 0)
        Lista.Add(new Select2Model() { id = "-1", text = "Sem filial definida" });

      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetTipoClienteSelect(string term)
    {
      List<Select2Model> Lista = new OrganizacaoServices(ContextoUsuario.UserLogged).GetByUsuarioTerm(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetCFOPSelect(string term)
    {
      List<Select2Model> Lista = new CFOPServices(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetMunicipioSelect(string term, int id)
    {
      MunicipioServices Service = new MunicipioServices(ContextoUsuario.UserLogged);
      List<Select2Model> List = Service.GetByTerm(term, id);
      return Json(new { items = List });
    }

    [HttpGet]
    public ActionResult GetUFSelect(string term)
    {
      #region "SWITCH PARA SIGLAS"
      switch (term)
      {
        case ("MG"):
          term = "Minas Gerais";
            break;
        case ("SP"):
          term = "São Paulo";
          break;
        case ("AP"):
          term = "Amapá";
          break;
        case ("BA"):
          term = "Bahia";
          break;
        case ("AM"):
          term = "Amazonas";
          break;
        case ("AC"):
          term = "Acre";
          break;
        case ("CE"):
          term = "Ceará";
          break;
        case ("DF"):
          term = "Distrito Federal";
          break;
        case ("GO"):
          term = "Goiás";
          break;
        case ("MA"):
          term = "Maranhão";
          break;
        case ("MT"):
          term = "Mato Grosso";
          break;
        case ("MS"):
          term = "Mato Grosso do Sul";
          break;
        case ("PA"):
          term = "Pará";
          break;
        case ("Paraíba"):
          term = "PB";
          break;
        case ("PR"):
          term = "Paraná";
          break;
        case ("PE"):
          term = "Pernambuco";
          break;
        case ("PI"):
          term = "Piauí";
          break;
        case ("RJ"):
          term = "Rio de Janeiro";
          break;
        case ("RN"):
          term = "Rio Grande do Norte";
          break;
        case ("RS"):
          term = "Rio Grande do Sul";
          break;
        case ("RO"):
          term = "Rondônia";
          break;
        case ("RR"):
          term = "Roraima";
          break;
        case ("SC"):
          term = "Santa Catarina";
          break;
        case ("SE"):
          term = "Sergipe";
          break;
        case ("TO"):
          term = "Tocantins";
          break;
        default:
          break;
      }
      #endregion
      UFServices Service = new UFServices(ContextoUsuario.UserLogged);
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }

    [HttpGet]
    public ActionResult GetTipoFaturamentoSelect(string term)
    {
      List<Select2Model> Lista = new TipoFaturamentoServices(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetTipoNaturezaSelect(string term)
    {
      List<Select2Model> Lista = new TipoNaturezaOperacaoServices(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetTipoPlanoContasCreditoSelectTeste(string term)
    {
      List<Select2Model> Lista = new PlanoContasService(ContextoUsuario.UserLogged).GetByTermFilialPJ(term, 3);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetTipoPlanoContasCreditoSelect(string term, int id)
    {
      List<Select2Model> Lista = new PlanoContasService(ContextoUsuario.UserLogged).GetByTerm(term, id);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetTipoPlanoContasDebitoSelect(string term, int id)
    {
      List<Select2Model> Lista = new PlanoContasService(ContextoUsuario.UserLogged).GetByTerm(term, id);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetTipoPessoaSelect(string term)
    {
      List<Select2Model> Lista = new TipoPessoaService(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetTipoDRESelect(string term)
    {
      List<Select2Model> Lista = new TipoDREService(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }
    [HttpGet]
    public ActionResult GetTipoEntradaSelect(string term)
    {
      List<Select2Model> Lista = new TipoEntradaService(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }
    [HttpGet]
    public ActionResult GetCategoriaDocumentosSelect(string term)
    {
      List<Select2Model> Lista = new CategoriaDocumentoServices(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }
    [HttpGet]
    public ActionResult GetDemResEXSelect(string term)
    {
      List<Select2Model> Lista = new DemonstrativoResultadoExercicioService(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }
    [HttpGet]
    public ActionResult GetTipoPlanoContaSelect(string term)
    {
      List<Select2Model> Lista = new TipoPlanoContasService(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetContabilidadeSelect(string term)
    {
      List<Select2Model> Lista = new ContabilidadeService(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetCartaoCreditoSelect(string term)
    {
      List<Select2Model> Lista = new CartaoDeCreditoServices(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetBancoSelect(string term)
    {
      List<Select2Model> Lista = new BancoServices(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    public ActionResult GetTipoDocumentoFilialSelect(string term)
    {
      List<Select2Model> Lista = new TipoDocumentoFilialServices(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }
  }
}
