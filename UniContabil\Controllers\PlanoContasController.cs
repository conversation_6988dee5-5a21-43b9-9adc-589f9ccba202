﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;

namespace UniContabil.Controllers
{
  public class PlanoContasController : LibController
  {
    public PlanoContasController()
    { }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        if (ContextoUsuario.UserLogged.Contabilidade != null)
        {
          IPagedList<PlanoContasModel> List = new PlanoContasService(ContextoUsuario.UserLogged).GetPagedList(page);
          ViewBag.PageItems = List;
          return View();
        }
        else
        {
          return RedirectToAction("Index", "ClassificacaoDocumentos");
        }
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View(new PlanoContasModel());
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(PlanoContasModel planocontasmodel)
    {
      try
      {
        planocontasmodel.IdContabilidade = ContextoUsuario.UserLogged.Contabilidade.Id;
        E_PlanoContas model = planocontasmodel.ToDatabase();
        new PlanoContasService().Create(model);

        return RedirectToAction(nameof(Index));
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(planocontasmodel);
      }
    }
    [HttpGet]
    public ActionResult GetTipoPlanoContaSelect(string term)
    {
      List<Select2Model> Lista = new TipoPlanoContasService(ContextoUsuario.UserLogged).GetByTerm(term);
      return Json(new { items = Lista });
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(int id)
    {
      try
      {
        PlanoContasModel PlanoContasModel = new PlanoContasModel().FromDatabase(new PlanoContasService().GetById(id));
        
        if (PlanoContasModel == null)
        {
          return NotFound();
        }

        PlanoContasModel.IdContabilidade = ContextoUsuario.UserLogged.Contabilidade.Id;
        return View(PlanoContasModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(PlanoContasModel planoContasModel)
    {
      try
      {
        planoContasModel.IdContabilidade = ContextoUsuario.UserLogged.Contabilidade.Id;
        new PlanoContasService().EditPlano(planoContasModel);
        return RedirectToAction(nameof(Index));

      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(planoContasModel);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string Delete(int id)
    {
      try
      {
        PlanoContasService Service = new PlanoContasService();
        var model = Service.GetById(id);
        Service.Delete(model);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        string msg = "Não foi possível deletar. Este item está sendo utilizado!";

        if (Ex.InnerException.Message.Contains("conflicted with the REFERENCE constraint"))
        {
          msg = "Não é possível deletar Plano que possuí vínculo";
        }

        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = msg });
      }
    }



  }
}




//    [HttpGet]
//    public ActionResult GetOrganizacaoSelect(string term)
//    {
//      OrganizacaoServices Service = new OrganizacaoServices();
//      List<Select2Model> List = Service.GetByTerm(term);
//      return Json(new { items = List });
//    }
//  }

//}