﻿@model UniContabilEntidades.Models.ContasPagarModel
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Edit";
}

<script src="~/Views/ContasPagar/ContasPagar.js"></script>

<h1>Editar @Html.DisplayNameFor(model => model)</h1>
<div class="row">
  <div class="col-md-4">
    <form asp-action="Edit">
      @Html.AntiForgeryToken()
      <div asp-validation-summary="ModelOnly" class="text-danger"></div>
      <div class="form-group">
        @Html.LabelFor(a => a.CodigoIdent)
        @Html.TextBoxFor(a => a.CodigoIdent, new { @class = "form-control", disabled = "true" })
        @Html.ValidationMessageFor(a => a.CodigoIdent)
      </div>

      <div class="form-group">
        <label asp-for="IdFornecedor" class="control-label"></label>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div style="width: 85%">
            @Html.Select2("FornecedorSelect", "GetFornecedorSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdFornecedor), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.FornecedorSelect == null ? "" : Model.FornecedorSelect.id, Model.FornecedorSelect == null ? "" : Model.FornecedorSelect.text)
          </div>
          <div style="width: 10%; display: flex;">
            <i class="mw-drip-search" id="DetalhesFornecedor"></i>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label asp-for="IdHistoricoContas" class="control-label"></label>
        @Html.Select2("HistoricoContasSelect", "GetHistoricoContasSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdHistoricoContas), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.HistoricoContasSelect == null ? "" : Model.HistoricoContasSelect.id, Model.HistoricoContasSelect == null ? "" : Model.HistoricoContasSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="IdCentroCusto" class="control-label"></label>
        @Html.Select2("CentroCustoSelect", "GetCentroCustoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdCentroCusto), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.CentroCustoSelect == null ? "" : Model.CentroCustoSelect.id, Model.CentroCustoSelect == null ? "" : Model.CentroCustoSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="IdContaContabel" class="control-label"></label>
        @Html.Select2("ContaContabelSelect", "GetContaContabelSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdContaContabel), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.ContaContabelSelect == null ? "" : Model.ContaContabelSelect.id, Model.ContaContabelSelect == null ? "" : Model.ContaContabelSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="IdTipoTitulo" class="control-label"></label>
        @Html.Select2("TipoTituloSelect", "GetTipoTituloSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdTipoTitulo), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.TipoTituloSelect == null ? "" : Model.TipoTituloSelect.id, Model.TipoTituloSelect == null ? "" : Model.TipoTituloSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="NroDocumento" class="control-label"></label>
        <input asp-for="NroDocumento" class="form-control" />
        <span asp-validation-for="NroDocumento" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="Serie" class="control-label"></label>
        <input asp-for="Serie" class="form-control" />
        <span asp-validation-for="Serie" class="text-danger"></span>
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.DtEmissao)
        @Html.TextBoxFor(a => a.DtEmissao, new { @class = "form-control Data" })
        @Html.ValidationMessageFor(a => a.DtEmissao)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.ValorBruto)
        @Html.TextBoxFor(a => a.ValorBruto, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.ValorBruto)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.DtVencimento)
        @Html.TextBoxFor(a => a.DtVencimento, new { @class = "form-control Data" })
        @Html.ValidationMessageFor(a => a.DtVencimento)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.DtVencimentoReal)
        @Html.TextBoxFor(a => a.DtVencimentoReal, new { @class = "form-control Data" })
        @Html.ValidationMessageFor(a => a.DtVencimentoReal)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.Parcelas)
        @Html.TextBoxFor(a => a.Parcelas, new { @class = "form-control numerosOnly" })
        @Html.ValidationMessageFor(a => a.Parcelas)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.NroBaixa)
        @Html.TextBoxFor(a => a.NroBaixa, new { @class = "form-control numerosOnly" })
        @Html.ValidationMessageFor(a => a.NroBaixa)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.DtBaixa)
        @Html.TextBoxFor(a => a.DtBaixa, new { @class = "form-control Data" })
        @Html.ValidationMessageFor(a => a.DtBaixa)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.ValorTotalPago)
        @Html.TextBoxFor(a => a.ValorTotalPago, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.ValorTotalPago)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.Juros)
        @Html.TextBoxFor(a => a.Juros, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.Juros)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.Descontos)
        @Html.TextBoxFor(a => a.Descontos, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.Descontos)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.JurosDia)
        @Html.TextBoxFor(a => a.JurosDia, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.JurosDia)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.DescontoDia)
        @Html.TextBoxFor(a => a.DescontoDia, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.DescontoDia)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.MultaMes)
        @Html.TextBoxFor(a => a.MultaMes, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.MultaMes)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.JurosDiaMonetario)
        @Html.TextBoxFor(a => a.JurosDiaMonetario, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.JurosDiaMonetario)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.DescontoDiaMonetario)
        @Html.TextBoxFor(a => a.DescontoDiaMonetario, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.DescontoDiaMonetario)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.MultaMesMonetario)
        @Html.TextBoxFor(a => a.MultaMesMonetario, new { @class = "form-control number" })
        @Html.ValidationMessageFor(a => a.MultaMesMonetario)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.ValorLiquido)
        @Html.TextBoxFor(a => a.ValorLiquido, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.ValorLiquido)
      </div>
      <div class="form-group">
        <input type="submit" value="Salvar" class="btn btn-primary" />
      </div>
    </form>
  </div>
</div>

<div>
  <a asp-action="Index">Voltar</a>
</div>

@await Html.PartialAsync("InfoAddFornecedor/_ModalPesquisaFornecedor", new ModalPesquisaFornecedor())
@await Html.PartialAsync("InfoAddFornecedor/_ModalDetalhesFornecedor")
