﻿function reloadTree(needClean = true) {
  $.get(GetURLBaseComplete() + "/Menu/GetMenus", function (data) {
    data = JSON.parse(data);

    if (data.Sucesso) {
      $('.jstree-menu').jstree(true).settings.core.data = data.Menus;
      $('.jstree-menu').jstree(true).refresh();

      if (needClean)
        $(".edit-menu").html("");
    }
    else {
      Alerta("Erro", "Houve um problema ao carregar os menus.", "red");
    }
  });
}

function DeleteMenu(id) {
  swal({
    title: "Apagar",
    text: "Tem certeza que deseja apagar este menu?",
    icon: "warning",
    buttons: ["Cancelar", "Apagar"],
    dangerMode: true,
  })
    .then((continuar) => {
      if (continuar) {
        $.post(GetURLBaseComplete() + "/Menu/Delete/" + id, function (data) {
          data = JSON.parse(data);
          if (data.Sucesso) {
            reloadTree();
          }
          else {
            Alerta("Erro", data.Mensagem, "red");
          }
        });
      }
    });
}

$(document).ready(function () {
  let menuReferencia = null;

  $.get(GetURLBaseComplete() + "/Menu/GetMenus", function (data) {
    data = JSON.parse(data);

    if (data.Sucesso) {
      $('.jstree-menu')
        .on("select_node.jstree", function (e, data) {
          let id = data.node.id;

          $(".edit-menu").html("");
          $(".spinner-menu").attr("style", "display: flex !important");

          $.get(GetURLBaseComplete() + "/Menu/GetDetails?Id=" + id.replace("Menu", "").replace("Agrupador", "").replace("_anchor", ""), function (html) {
            $(".spinner-menu").attr("style", "display: none !important");
            $(".edit-menu").html(html);
          });
        })
        .jstree({
          'core': {
            'data': data.Menus
          },
          "plugins": ["contextmenu"],
          "contextmenu": {
            "items": function ($node) {
              return {
                "create": {
                  "separator_before": true,
                  "separator_after": true,
                  "label": "Criar submenu",
                  "action": function (obj) {
                    menuReferencia = obj.reference[0].id.replace("_anchor", "");
                    $('#modalSubmenu').modal('show');
                  }
                },
                "edit": {
                  "separator_before": true,
                  "separator_after": true,
                  "label": "Editar nome",
                  "action": function (obj) {
                    menuReferencia = obj.reference[0].id;
                    let nomeAtual = $("#" + menuReferencia).html().split("</i>")[1];
                    $("#inputEditarNome").val(nomeAtual);
                    $('#modalEditarNome').modal('show');
                  }
                },
                "delete": {
                  "separator_before": true,
                  "separator_after": true,
                  "label": "Excluir",
                  "action": function (obj) {
                    let id = obj.reference[0].id.replace("_anchor", "").replace("Menu", "").replace("Agrupador", "");
                    DeleteMenu(id);
                  }
                }
              };
            }
          }
        });
    }
    else {
      Alerta("Erro", "Houve um problema ao carregar os menus.", "red");
    }
  });

  $("#Agrupador").on("change", function () {
    if (this.checked)
      $("#modalNovoMenuLabel").html("Novo Agrupador")
    else
      $("#modalNovoMenuLabel").html("Nova Página")
  });

  $("#salvarNovoMenu").on("click", function () {
    const nomeMenu = $("#inputNomeMenu").val();
    if (nomeMenu) {
      $("#inputNomeMenu").removeClass("is-invalid");

      const obj = {
        Nome: nomeMenu,
        Agrupador: $("#Agrupador").is(":checked"),
        IsVisible: $("#Visibilidade").is(":checked")
      };

      $.post(GetURLBaseComplete() + "/Menu/CreateMenu", obj, function (data) {
        data = JSON.parse(data);
        if (data.Sucesso) {
          $("#inputNomeMenu").val("");
          $('#modalNovoMenu').modal('hide');
          Alerta("Sucesso", "Novo menu gerado com sucesso.", "green", 5000);
          reloadTree();
        }
        else {
          Alerta("Erro", data.Mensagem, "red", 5000);
        }
      });
    }
    else {
      $("#inputNomeMenu").addClass("is-invalid");
    }
  });

  $("#salvarNovoModulo").on("click", function () {
    const nomeModulo = $("#inputModuloNome").val();
    if (nomeModulo) {
      $("#inputModuloNome").removeClass("is-invalid");

      const obj = {
        Nome: nomeModulo
      };

      $.post(GetURLBaseComplete() + "/Menu/CreateModulo", obj, function (data) {
        data = JSON.parse(data);
        if (data.Sucesso) {
          $("#inputModuloNome").val("");
          $('#modalNovoModulo').modal('hide');
          Alerta("Sucesso", "Novo módulo gerado com sucesso.", "green", 5000);
          reloadTree();
        }
        else {
          Alerta("Erro", data.Mensagem, "red", 5000);
        }
      });
    }
    else {
      $("#inputModuloNome").addClass("is-invalid");
    }
  });

  $("#salvarEditarNome").on("click", function () {
    const nomeMenu = $("#inputEditarNome").val();
    if (nomeMenu) {
      $("#inputEditarNome").removeClass("is-invalid");

      const obj = {
        Nome: nomeMenu,
        Id: parseInt(menuReferencia.replace("Menu", "").replace("Agrupador", "").replace("_anchor", ""))
      };

      $.post(GetURLBaseComplete() + "/Menu/EditNome", obj, function (data) {
        data = JSON.parse(data);
        if (data.Sucesso) {
          $("#inputEditarNome").val("");
          $('#modalEditarNome').modal('hide');
          Alerta("Sucesso", "Nome do menu alterado com sucesso.", "green", 5000);
          reloadTree();
        }
        else {
          Alerta("Erro", data.Mensagem, "red", 5000);
        }
      });
    }
    else {
      $("#inputEditarNome").addClass("is-invalid");
    }
  });

  $("#salvarSubmenu").on("click", function () {
    const nomeMenu = $("#inputSubmenu").val();
    if (nomeMenu) {
      $("#inputSubmenu").removeClass("is-invalid");

      const obj = {
        Nome: nomeMenu,
        Agrupador: false,
        MenuReferencia: menuReferencia
      };

      $.post(GetURLBaseComplete() + "/Menu/CreateSubmenu", obj, function (data) {
        data = JSON.parse(data);
        if (data.Sucesso) {
          $("#inputSubmenu").val("");
          $('#modalSubmenu').modal('hide');
          Alerta("Sucesso", "Novo submenu gerado com sucesso.", "green", 5000);
          reloadTree();
        }
        else {
          Alerta("Erro", data.Mensagem, "red", 5000);
        }
      });
    }
    else {
      $("#inputSubmenu").addClass("is-invalid");
    }
  });

  $("#salvarNovaFuncao").click(function () {
    const nomeFuncao = $("#inputNovaFuncao").val();
    const codFuncao = $("#inputCodNovaFuncao").val();

    if (!nomeFuncao) {
      $("#inputNovaFuncao").addClass("is-invalid");
    }
    else if (!codFuncao) {
      $("#inputCodNovaFuncao").addClass("is-invalid");
    }
    else {
      const obj = {
        Nome: nomeFuncao,
        Codigo: parseInt(codFuncao),
        IdMenu: parseInt($("#menu-active").data("menu"))
      }

      $.post(GetURLBaseComplete() + "/Menu/CreateFuncao", obj, function (data) {
        data = JSON.parse(data);
        if (data.Sucesso) {
          let newRow = '<tr> <td> ' + codFuncao + ' </td> <td> ' + nomeFuncao + ' </td> <td> <button class="btn btn-outline-danger btn-sm deletarFuncao" type="button">Excluir</button> </td> </tr>';
          $("#functions-table tbody").append(newRow);

          $(".deletarFuncao").off();
          $(".deletarFuncao").on("click", function () {
            const idFuncao = parseInt($(this).parent().parent().data("funcao"));

            $.post(GetURLBaseComplete() + "/Menu/DeleteFuncao/" + idFuncao, function (data) {
              data = JSON.parse(data);
              if (data.Sucesso) {
                $("#functions-table tbody tr[data-funcao=" + idFuncao + "]").remove();
                Alerta("Sucesso", "Função excluída com sucesso.", "green", 5000);
              }
              else
                Alerta("Erro", data.Mensagem, "red", 5000);
            });
          });

          $("#inputNovaFuncao").val("");
          $("#inputCodNovaFuncao").val("");
          $('#modalNovaFuncao').modal('hide');

          Alerta("Sucesso", "Nova função gerado com sucesso.", "green", 5000);
        }
        else {
          Alerta("Erro", data.Mensagem, "red", 5000);
        }
      });
    }
  });

  $('#novoMenu').click(function () {
    $("#Agrupador").prop("checked", false);
    $("#Visibilidade").prop("checked", false);
    $('#modalNovoMenu').modal("show");
  });
});