﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure
@model List<GridProduto>

<table class="table table-sm table-striped table-hover">
  <thead>
    <tr>
      <th scope="col">
        Descrição
      </th>
      <th scope="col">
        Código <PERSON>
      </th>
      <th scope="col">
        Unidade Medida
      </th>
      <th scope="col">
        Data Cadastro
      </th>
      <th scope="col">
        Descrição Grupo
      </th>
      <th>

      </th>
    </tr>
  </thead>
  <tbody>
    @foreach (GridProduto gridProduto in Model)
    {
    <tr>
      <td>
        @gridProduto.Descricao
      </td>
      <td>
        @gridProduto.CodigoBarras
      </td>
      <td>
        @gridProduto.UnidadeMedida
      </td>
      <td>
        @gridProduto.DataCadastroFormatada
      </td>
      <td>
        @gridProduto.DescricaoGrupo
      </td>
      <td>
        <i class="mw-drip-information MaisInformacoesProduto" data-CodigoProduto="@gridProduto.CodigoProduto"></i>
      </td>
    </tr>
    }
  </tbody>
</table>

