﻿var GridFilial = function () {

};

GridFilial.init = function () {

  //function remakeChecks(line) {
  //  for (var i = 0; i < $('#filiais-table tbody tr').length; i++) {
  //    if (i != line)
  //      $("#selectfilial_" + i).prop("checked", false);
  //  }
  //}

  function loadGridTipoGrupo(codfilial, codorg) {
    const coduser = $("#Codigo").val();
    $.ajax({
      type: 'GET',
      url: GetURLBaseComplete() + '/Usuarios/SaveVinculo',
      dataType: 'json',
      data: {
        id: coduser,
        codfilial: codfilial,
        codorg: codorg,
      },
      success: function (data) {
        $(".spinner-menu").attr("style", "display: none !important");
        if (!data.Error) {
          SweetAlert("Sucesso", data.mensage, "success");
        }
        else {
          SweetAlert("Erro", data.mensage, "danger");
        }
      }
    });
  }

  function loadGridTipoGrupoSelectAll() {
    const coduser = $("#Codigo").val();
    $.ajax({
      type: 'GET',
      url: GetURLBaseComplete() + '/Usuarios/SaveAllVinculo',
      dataType: 'json',
      data: {
        id: coduser,
      },
      success: function (data) {
        $(".spinner-menu").attr("style", "display: none !important");
        if (!data.Error) {
          SweetAlert("Sucesso", data.Mensage, "success");
        }
        else {
          SweetAlert("Erro", data.Mensage, "danger");
        }

      }
    });
  }

  function DeleteAssociados(codfilial, codorg) {
    const coduser = $("#Codigo").val();
    $.ajax({
      url: GetURLBaseComplete() + '/Usuarios/DeleteAssociacao',
      dataType: "json",
      type: "POST",
      data: {
        id: coduser,
        codfilial: codfilial,
        codorg: codorg,
      },
      success: function (retorno) {
        if (!retorno.erro) {
          loadAbaOrganizacoes();
          SweetAlert("Sucesso", retorno.mensagem, "success");
        }
        else {
          SweetAlert("Erro", retorno.mensagem, "danger");
        }
      }
    });
  }


  function DeleteAssociadosAll() {
    const coduser = $("#Codigo").val();
    $.ajax({
      url: GetURLBaseComplete() + '/Usuarios/DeleteAssociacaoAll',
      dataType: "json",
      type: "POST",
      data: {
        id: coduser,
      },
      success: function (retorno) {
        if (!retorno.erro) {
          loadAbaOrganizacoes();
          SweetAlert("Sucesso", retorno.mensagem, "success");
        }
        else {
          SweetAlert("Erro", retorno.mensagem, "danger");
        }
      }
    });
  }

  $(".select-all").change(function () {
    for (var i = 0; i < $('#filiais-table tbody tr').length; i++) {
      //$("#selectfilial_" + i).iCheck("check");
      $("#selectfilial_" + i).prop("checked", this.checked);
    }

    if (this.checked) {
      loadGridTipoGrupoSelectAll();
    }
    else {
      DeleteAssociadosAll();
    }
  });

  $(".selects").change(function () {
    $(".select-all").prop("checked", false);
    var l = this.id.split('_')[1];
    const linha = $("#h_selectfilial_" + l).data("linha");

    console.log(linha)

    $(".spinner-menu").attr("style", "display: flex !important; margin-top: 80px;");
    const idfilial = $("#h_selectfilial_" + l).data("codigo");
    const idorg = $("#h_selectfilial_" + l).data("codorg");

    if (this.checked) {
      loadGridTipoGrupo(idfilial, idorg);
    }
    else {
      DeleteAssociados(idfilial, idorg);
    }

  });
}


$(document).ready(function () {
  GridFilial.init();
});

