﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure.Controls
@model FilialModel

<div class="col-md-12">
  <div class="row">
    <p><b>Pesquise aqui a contabilidade que terá permissão de acesso aos seus dados, ou faça um convite ao seu contador atráves do botão Enviar Convite p/ Contabilidade</b></p>
  </div>
  <div class="row">
    <div class="col-md-12">
      <div class="form-group">
        <label asp-for="IdContabilidade" class="control-label"></label>
        @Html.Select2("ContabilidadeSelect", "GetContabilidadeSelect", "Selecione " + Html.DisplayNameFor(model => model.IdContabilidade), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.ContabilidadeSelect == null ? "" : Model.ContabilidadeSelect.id, Model.ContabilidadeSelect == null ? "" : Model.ContabilidadeSelect.text)
      </div>
    </div>
  </div>
</div>
