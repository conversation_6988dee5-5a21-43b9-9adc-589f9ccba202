﻿var Cliente = {};


Cliente.init = function () {
  $('#campoCEP').on('keyup', function () {
    console.log('Digitado')
    completaCampos();
  })

  $(document).on("change", "select", function () {
    var optionSelected = $("option:selected", this);

    if (optionSelected.length > 0) {
      var valueSelected = optionSelected[0].innerText;

      if (valueSelected == "Particular") {
        $("#divConv").hide();
        $("#divPart").show();
      }
      else {
        $("#divConv").show();
        $("#divPart").hide();
      }
    }
    else {
      $("#divPart").hide();
      $("#divConv").hide();
    }

    console.log("hit");
  });
};

$(document).ready(function () {
  Cliente.init();

  $("#divPart").hide();
  $("#divConv").hide();

  if ($("#TipoClienteSelect_text").val() == "Particular") {
    $("#divConv").hide();
    $("#divPart").show();
  }
  else if ($("#TipoClienteSelect_text").val() == "Convênio") {
    $("#divConv").show();
    $("#divPart").hide();
  }
 
});


function completaCampos() {
  var val = $('#campoCEP').inputmask('unmaskedvalue');

  if (val.length == 8) {
    $.ajax({
      url: 'https://viacep.com.br/ws/' + val + '/json',
      type: 'GET',
      dataType: 'json',
      success: function (data) {
        if (data.erro) {

        } else {
          console.log(data)
          $('#Logradouro').val(data['logradouro'])
          $('#Complemento').val(data['complemento'])
          $('#Bairro').val(data['bairro'])
          $.ajax({
            type: 'GET',
            url: GetURLBaseComplete() + '/select2/GetUFSelect?term=' + data['uf'],
            success: function (sucessData) {
              console.log(sucessData)
              $("#select2-UFSelectLookup-container").attr('title', sucessData['items'][0]['text'])
              $("#UFSelect_id").val(sucessData['items'][0]['id'])
              $("#Estado").val(sucessData['items'][0]['text'])
              $('#select2-UFSelectLookup-container').html('')
              $('#select2-UFSelectLookup-container').append(sucessData['items'][0]['text'])
              $.ajax({
                type: 'GET',
                url: GetURLBaseComplete() + '/select2/GetMunicipioSelect?term=' + data['localidade'] + "&id=" + sucessData['items'][0]['id'],
                success: function (sucessDataCidade) {
                  console.log(sucessDataCidade)
                  $("#select2-MunicipioSelect-container").attr('title', sucessDataCidade['items'][0]['text'])
                  $("#MunicipioSelect_id").val(sucessDataCidade['items'][0]['id'])
                  $("#Cidade").val(sucessDataCidade['items'][0]['text'])
                  $('#select2-MunicipioSelectLookup-container').html('')
                  $('#select2-MunicipioSelectLookup-container').append(sucessDataCidade['items'][0]['text'])
                }
              })
            }
          })
        }
      }
    })
  }
}