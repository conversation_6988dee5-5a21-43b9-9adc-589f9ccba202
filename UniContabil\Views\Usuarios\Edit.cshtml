﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model UsuarioBasico

@{
  ViewData["Title"] = "Usuario";
}
<script src="~/Views/Usuarios/Usuarios.js?ki=bhj"></script>
<style>
  .hide-body{
    display:none !important;
  }
  .card-children {
    box-shadow: none !important;
  }

</style>
<div class="card">

  @*<ul class="nav nav-tabs">
      <li class="nav-item">
        <a class="nav-link active" id="aba-users">Usuários</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="aba-organizacoes">Médico/Clinica</a>
      </li>
    </ul>*@

  <div class="card card-children" id="body-aba-users">
    @using (Html.BeginForm("Edit", "Usuarios", FormMethod.Post))
    {
      <div class="card-body">
        @Html.HiddenFor(a => a.Codigo)

        <div class="form-group">
          @Html.LabelFor(a => a.Nome)
          @Html.TextBoxFor(a => a.Nome, new { @class = "form-control" })
          @Html.ValidationMessageFor(a => a.Nome)
        </div>
        <div class="form-group">
          @Html.LabelFor(a => a.CPF)
          @Html.TextBoxFor(a => a.CPF, new { @class = "form-control CPF", @readonly="readonly" })
          @Html.ValidationMessageFor(a => a.CPF)
        </div>
        <div class="form-group">
          @Html.LabelFor(a => a.Email)
          @Html.TextBoxFor(a => a.Email, new { @class = "form-control EMAIL" })
          @Html.ValidationMessageFor(a => a.Email)
        </div>

        <div class="form-group">
          @Html.LabelFor(a => a.Telefone)
          @Html.TextBoxFor(a => a.Telefone, new { @class = "form-control telefone" })
          @Html.ValidationMessageFor(a => a.Telefone)
        </div>

        <div class="form-group">
          @Html.LabelFor(a => a.Telefone2)
          @Html.TextBoxFor(a => a.Telefone2, new { @class = "form-control telefone2" })
          @Html.ValidationMessageFor(a => a.Telefone2)
        </div>
      </div>
      <div class="card-footer">
        <a asp-action="Index" class="link link-info">Voltar</a>
        <button type="submit" class="btn btn-success">Salvar</button>
      </div>
    }
  </div>
  @*<div class="card card-children hide-body" id="body-aba-organizacoes">
      @await Html.PartialAsync("_GridControlePermissao", new ControlePermissaoUserModel())
    </div>*@

</div>
