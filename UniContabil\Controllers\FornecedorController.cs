﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;


namespace UniContabil.Controllers
{
  public class FornecedorController : LibController
  {
    public FornecedorController()
    { }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        IPagedList<FornecedorModel> List = new FornecedorServices().GetPagedList(page);
        ViewBag.PageItems = List;
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(FornecedorModel fornecedorModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new FornecedorServices(ContextoUsuario.UserLogged).Create(fornecedorModel);

          return RedirectToAction(nameof(Index));
        }
        else
        {
          return View(fornecedorModel);
        }
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(fornecedorModel);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(int id)
    {
      try
      {
        FornecedorModel fornecedorModel = new FornecedorModel().FromDatabase(new FornecedorServices().GetById(id));

        if (fornecedorModel == null)
        {
          return NotFound();
        }

        return View(fornecedorModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(FornecedorModel fornecedorModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new FornecedorServices(ContextoUsuario.UserLogged).Edit(fornecedorModel);
          return RedirectToAction(nameof(Index));
        }
        return View(fornecedorModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(fornecedorModel);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string Delete(int id)
    {
      try
      {
        FornecedorServices Service = new FornecedorServices();
        var model = Service.GetById(id);
        Service.Delete(model);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }

    [HttpGet]
    public ActionResult GetOrganizacaoSelect(string term)
    {
      OrganizacaoServices Service = new OrganizacaoServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
    [HttpGet]
    public ActionResult GetFilialSelect(string term)
    {
      FilialServices Service = new FilialServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
    [HttpGet]
    public ActionResult GetTipoFornecedorSelect(string term)
    {
      TipoFornecedorServices Service = new TipoFornecedorServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }

    [HttpGet]
    //[NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.GridDetalhesCliente)]
    public PartialViewResult _GridFornecedor(string Campo1)
    {
      FornecedorServices fornecedorServices = new FornecedorServices();
      List<GridFornecedor> ListaGridFornecedor = fornecedorServices.GetGridFornecedor(Campo1);
      return PartialView("InfoAddFornecedor/_GridFornecedor", ListaGridFornecedor);
    }

    [HttpGet]
    //[NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.GridDetalhesCliente)]
    public PartialViewResult _GridDetalhesFornecedor(int idFornecedor)
    {
      FornecedorServices fornecedorServices = new FornecedorServices();
      GridDetalhesFornecedor gridDetalhesFornecedor = fornecedorServices.GetGridDetalhesFornecedor(idFornecedor);
      return PartialView("InfoAddFornecedor/_GridDetalhesFornecedor", gridDetalhesFornecedor);
    }
  }
}
