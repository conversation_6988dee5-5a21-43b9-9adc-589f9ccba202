﻿@{
  ViewData["Title"] = "Menus";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

<script src="~/Views/Menu/Menu.js"></script>

<div style="display: flex">
  <div class="col-4">
    <div class="card">
      <div class="card-header" style="height: unset !important">
        <div class="card-title">Lista de Menus</div>
        <div class="card-options">
          <button class="btn btn-outline-primary btn-sm" style="margin-right: 5px" data-toggle="modal" data-target="#modalNovoModulo" id="novoModulo">+ Novo Módulo</button>
          <button class="btn btn-outline-primary btn-sm" id="novoMenu">+ Novo Menu</button>
        </div>
      </div>
      <div class="card-body">
        <div class="jstree-menu" style="margin-top: 20px;"></div>
      </div>
      <div class="card-footer">
      </div>
    </div>
  </div>
  <div class="col-8">
    <div class="edit-menu">
    </div>
    <div class="justify-content-center spinner-menu" style="display:none !important">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Carregando...</span>
      </div>
    </div>
  </div>
</div>

@{
  await Html.RenderPartialAsync("_ModalNovo");
  await Html.RenderPartialAsync("_ModalNovoSubmenu");
  await Html.RenderPartialAsync("_ModalNovaFuncao");
  await Html.RenderPartialAsync("_ModalNovoModulo");
  await Html.RenderPartialAsync("_ModalEditarNome");
}