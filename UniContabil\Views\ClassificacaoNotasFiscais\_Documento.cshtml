﻿<div class="card">
  <div class="card-header">
    <a class="btn btn-danger" id="deleteRecibo" title="Excluir"><i class="mw-drip-trash"></i></a>
  </div>
  <div class="car-body">
    <input type="file" id="Anexodocumento" accept="image/*,.pdf" class="form-control" hidden="hidden" />
    <input type="hidden" id="codLancamento" value="@ViewBag.CodLancamento" />
    <div id="labelComprovante" class="col-md-12">
      <label style="color: black; font-size: 14pt;">ANEXE SEU COMPROVANTE AQUI:</label>
    </div>
    <div class="row">
      <div class="col-md-12" style=" margin: 20px 0;">
        <img id="AddAnexo" src="~/Content/img/IconePasta.png" width="100" height="100" />

        <iframe style='height: 55vh !important;width: 100% !important;' id="iframeDoc" />
        <img id="imgDoc" style='height: 60vh!important;width: 95% !important;' />
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function () {
    var cod = $("#codLancamento").val();
    console.log(cod);
    if ($("#Extensao").val() == undefined || $("#Extensao").val() == "" || $("#Extensao").val() == null) {
      $("#AddAnexo").show();
      $("#iframeDoc").hide();
      $("#imgDoc").hide();
    }
    else {
      $("#AddAnexo").hide();

      if ($("#Extensao").val() == "pdf") {
        $("#iframeDoc").show();
        $("#iframeDoc").attr("src", GetURLBaseComplete() + "/Anexo/GetPDFLancamento/" + cod);

        $("#imgDoc").hide();
        $("#labelComprovante").hide();
      }
      else {
        $.ajax({
          url: GetURLBaseComplete() + "/Anexo/GetImageLancamento",
          dataType: "json",
          type: "GET",
          data: {
            id: parseInt(cod)
          },
          success: function (image) {
            $("#imgDoc").show();
            $("#imgDoc").attr("src", "data:image/" + image.Extensao + ";base64," + image.path);

            $("#iframeDoc").hide();
            $("#labelComprovante").hide();
            $("#AddAnexo").hide();
          }
        });
      }
    }
  });

  $(document).on("click", "#AddAnexo", function () {
    $("#Anexodocumento").click();
  });

  $(document).on("change", "#Anexodocumento", function () {
    $("#Edit").click();
  });
</script>

