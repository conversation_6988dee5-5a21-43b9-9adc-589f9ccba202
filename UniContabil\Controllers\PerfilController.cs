﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using UniContabil.Infrastructure.Exceptions;

using UniContabilEntidades.Models;
using UniContabilDomain.Services;
using UniContabilEntidades.Models.DataBase;
using X.PagedList;
using Estoque.Infrastructure;
using iTextSharp.text;
using Newtonsoft.Json;

namespace UniContabil.Controllers
{
  [Authorize]
  public class PerfilController : LibController
  {
    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit()
    {
      try
      {
        UsuarioEditModel usuario = new UsuarioEditModel().FromDatabase(new UsuarioEditService().GetAll(ContextoUsuario.UserLogged.IdUsuario));

        if (usuario == null)
        {
          return NotFound();
        }

        return View(usuario);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(UsuarioEditModel Usuario)
    {
      try
      {
        if (ModelState.IsValid)
        {

          Infrastructure.Helpers.ValidadorHelper.ValidaCPF(Usuario.CPF);

          E_Usuarios user = new UsuariosServices().GetById(Usuario.Id);
          Usuario.Senha = user.U_Senha;
          var model = Usuario.ToDatabase();
          new UsuarioEditService().Edit(model);
        }
        MessageAdd(new Message(MessageType.Success, "Editado com sucesso"));
        return View(Usuario);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(Usuario);
      }
    }
    [HttpGet]
    //[NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.GridDetalhesCliente)]
    public PartialViewResult _GridDependentes()
    {
      DependenteService dependenteService = new DependenteService(ContextoUsuario.UserLogged);
      List<DependentesModel> ListaDependentes = dependenteService.GetList();
      return PartialView("_GetGridDependentes", ListaDependentes);
    }

    public PartialViewResult _GridEditDependentes(Guid Id)
    {
      try
      {
        DependentesModel depModep = new DependentesModel().FromDatabase(new DependenteService().GetById(Id));

        return PartialView("_partialEditDependentes", depModep);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public JsonResult CreateDependentes(DependentesModel dependentes)
    {
      try
      {
        if (!new ValidaCPF().IsValid(dependentes.CPFdep))
        {
          throw new CustomException("O CPF é inválido!");
        }
        if (dependentes.Nascimento == null || dependentes.Nascimento == DateTime.Parse("01/01/0001 00:00:00"))
        {
          throw new CustomException("Data de nascimentro inválida!");
        }
        //Infrastructure.Helpers.ValidadorHelper.ValidaCPF(dependentes.CPFdep != null ? dependentes.CPFdep.Replace(".", "").Replace("-", "") : null);

        E_Dependentes model = dependentes.ToDatabase();

        new DependenteService(ContextoUsuario.UserLogged).Create(dependentes);
        return Json(new { Erro = false, Mensagem = "Criado com Sucesso!" });

      }
      catch (Exception Ex)
      {
        return Json(new { Erro = true, Mensagem = Ex.Message });
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public JsonResult EditDependentes(DependentesModel dependentes)
    {
      try
      {
        E_Dependentes model = dependentes.ToDatabase();

        new DependenteService(ContextoUsuario.UserLogged).Edit(dependentes);
        return Json(new { Erro = false, Mensage = "Editado com Sucesso!" });

      }
      catch (Exception Ex)
      {
        return Json(new { Erro = true, Mensage = Ex.Message });
      }
    }


    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string DeleteDependente(Guid id)
    {
      try
      {
        DependenteService Service = new DependenteService();
        DependentesModel model = new DependentesModel();
        Service.Delete(id);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }

    [HttpGet]
    public ActionResult GetTipoDependenteSelect(string term)
    {
      TipoDependenteService Service = new TipoDependenteService();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public JsonResult ResetPassword(ResetPassword model)
    {
      try
      {

        if (!ModelState.IsValid)
          throw new Exception("Gentileza preencher todos os campos.");

        new AccountServices(ContextoUsuario.UserLogged).PassReset(model);

        return Json(new { Erro = false, Mensagem = "Senha alterada com Sucesso!" });
      }
      catch (Exception Ex)
      {
        return Json(new { Erro = true, Mensagem = Ex.Message });
      }
    }

  }
}