﻿@model UniContabilEntidades.Models.TipoLancamentoModel
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;

    @{
    ViewData["Title"] = @Html.DisplayNameFor(model => model);
    }
  <div class="table-group card">
    <div class="card-header">
      <div class="row">
        @if (ContextoUsuario.HasPermission("TipoLancamento", TipoFuncao.Create))
        {
        <a class="btn btn-success" asp-action="Create"><i class="mw-drip-plus"></i></a>
        }
      </div>
    </div>
    <div class="table-group-content">
      <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
        <thead>
          <tr>
                <th>
                  @Html.DisplayNameFor(model => model.RazaoSocialOrganizacao)
                </th>
                <th>
                  @Html.DisplayNameFor(model => model.RazaoSocialFilial)
                </th>
                <th>
                  @Html.DisplayNameFor(model => model.Codigo)
                </th>
                <th>
                  @Html.DisplayNameFor(model => model.Descricao)
                </th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          @foreach (UniContabilEntidades.Models.TipoLancamentoModel item in ViewBag.PageItems) {
          <tr>
                <td>
                  @Html.DisplayFor(modelItem => item.RazaoSocialOrganizacao)
                </td>
                <td>
                  @Html.DisplayFor(modelItem => item.RazaoSocialFilial)
                </td>
                <td>
                  @Html.DisplayFor(modelItem => item.Codigo)
                </td>
                <td>
                  @Html.DisplayFor(modelItem => item.Descricao)
                </td>
              <td>
                @if (ContextoUsuario.HasPermission("TipoLancamento", TipoFuncao.Edit))
                {
               <a class="btn btn-warning btn-sm" asp-action="Edit" asp-route-id="@item.Id"><i class="mw-drip-document-edit"></i></a>
                }
                @if (ContextoUsuario.HasPermission("TipoLancamento", TipoFuncao.Delete))
                {
                <button class="link link-danger" onclick="DeleteItem('@this.ViewContext.RouteData.Values["controller"].ToString()', @item.Id)">Apagar</button>
                }
              </td>
          </tr>
          }
        </tbody>
      </table>
    </div>
    <div class="card-footer">
      @Html.PagedListPager((IPagedList)ViewBag.PageItems, page => Url.Action("Index", new { page }),
      new PagedListRenderOptions {
        LiElementClasses = new string[] { "page-item" },
        PageClasses = new string[] { "page-link" },
        Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
        MaximumPageNumbersToDisplay = 5
      })
    </div>
  </div>

