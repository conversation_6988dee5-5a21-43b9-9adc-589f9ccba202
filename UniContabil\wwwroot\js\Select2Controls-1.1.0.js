﻿function formatRepo(repo) {
  return repo.text;
}

function formatRepoSelection(repo) {
  return repo.full_name || repo.text;
}

function MakeDropDown(FieldId, Url, PlaceHolder) {
  var idpai = $("#" + FieldId + "Lookup")[0].getAttribute("IdPai");

  $("#" + FieldId + "Lookup").select2({
    ajax: {
      url: Url,
      dataType: 'json',
      delay: 250,
      data: function (params) {
        var controlpai = "";
        if (idpai) {
          controlpai = $("#" + idpai).val();
        }

        return {
          term: params.term,
          id: controlpai
        };
      },
      processResults: function (data, params) {
        params.page = params.page || 1;
        return {
          results: data.items,
          pagination: {
            more: (params.page * 30) < data.total_count
          }
        };
      },
      cache: true
    },
    placeholder: PlaceHolder,
    allowClear: true,
    minimumInputLength: 0,
    templateResult: formatRepo,
    templateSelection: formatRepoSelection
  });

  $("#" + FieldId + "Lookup").on('select2:select', function (evt) {
    $('#' + FieldId + "_id").val(evt.params.data.id);
    $('#' + FieldId + "_text").val(evt.params.data.text);

    if (FieldId == "FormaPgtoDespesaSelect") {
      ClassificacaoNotasFiscais.FormaPgtoDespesa(evt.params.data.text, evt.params.data.id);
    }
    
    if (FieldId == "CartaoCreditoSelect") {
      $("#IdCartaoCredito").val(evt.params.data.id);
    }
    
    if (FieldId == "BancoSelect") {
      $("#IdBanco").val(evt.params.data.id);
    }

  });

  $("#" + FieldId + "Lookup").on('select2:clear', function (evt) {
    if ($('.select2-selection__clear').length == 1) {
      $('#' + FieldId + "_id").val(0);
    }
  });

  if (idpai) {
    $("#select2-" + idpai + "-container").on('DOMSubtreeModified', function () {
      $("#" + FieldId + "Lookup").val(null).trigger('change');
      $("#" + FieldId + "_id").val("0");
      $("#" + FieldId + "_text").val("0");
    });
  }
}

function getAllElementsWithAttribute(attribute, fieldName) {
  var matchingElements = [];
  var allElements = document.getElementsByTagName('*');
  for (var i = 0, n = allElements.length; i < n; i++) {
    if (allElements[i].getAttribute(attribute) !== null && allElements[i].getAttribute(attribute) == fieldName) {
      // Element exists with attribute. Add to array.
      matchingElements.push(allElements[i]);
    }
  }
  return matchingElements;
}
