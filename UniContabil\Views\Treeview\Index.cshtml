﻿@{
  ViewData["Title"] = "Configuração TreeView";
  Layout = "~/Views/Shared/_Layout.cshtml";
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();
}
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="~/Views/Treeview/Treeview.js?qwe=qwe"></script>
<link href="~/Views/Treeview/Treeview.css?1dqw=123" rel="stylesheet" />

<div class="card" style="display:flex !important;padding:10px !important">

  <div class="col-md-6" style="overflow:auto;">
    <div class="row">
      <div class="form-group">
        <div class="col-md-12">
          <input type="radio" name="Tipo" class="custom-radio" value="1" id="Tipo_1" />
          <label>Receita</label>
          <input type="radio" name="Tipo" class="custom-radio" value="2" id="Tipo_2" checked="checked" />
          <label>Despesa</label>
        </div>
      </div>
    </div>
    <div id="tree" style=" max-height: calc(100vh - 100px);">
    </div>
  </div>
</div>