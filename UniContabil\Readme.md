﻿# MW ERP

## Atualizando Banco de Dados

### • DEContext

1. Execute o seguinte comando no "Console do Gerenciador de Pacotes" usando o Visual Studio. *(Ferramentas > Gerenciador de Pacotes Nuget)*

```shell
$ Scaffold-DbContext '<<STRING CONEXÃO>>' Microsoft.EntityFrameworkCore.SqlServer -OutputDir Models/DataBase -UseDatabaseNames -DataAnnotations -Force
```

2. Acesse o arquivo *./Models/Database/MWERPContext.cs* e altere o método ***OnConfiguring*** para

```c#
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
      if (!optionsBuilder.IsConfigured)
      {
        optionsBuilder.UseLazyLoadingProxies();
        string connectionString = ConfigurationManager.ConnectionStrings["MWERPConnection"].ConnectionString;
        optionsBuilder.UseSqlServer(connectionString);
      }
    }
```

3. Ainda no arquivo *./Models/Database/MWERPContext.cs* adicione o trecho de código abaixo dentro do método ***OnModelCreating***

```c#
    modelBuilder.Entity<PainelPedidoModel>(entity =>
    {
      entity.HasNoKey();
    });
    
    modelBuilder.Entity<MWEmissaoNFe.Models.NFeDetalhesPagamento>(entity =>
    {
    entity.HasNoKey();
    });

    modelBuilder.Entity<MWEmissaoNFe.Models.NFeResumoTotal>(entity =>
    {
    entity.HasNoKey();
    });

    modelBuilder.Entity<MWEmissaoNFe.Models.NFeEmitente>(entity =>
    {
    entity.HasNoKey();
    });

    modelBuilder.Entity<LogNotaFatModel>(entity =>
    {
    entity.HasNoKey();
    });

    modelBuilder.Entity<StringQuery>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<GridDetalhesFornecedor>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<GridDetalhesProduto>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<GridDetalhesCliente>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<GridProduto>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<GridFornecedor>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<GridCliente>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<TituloReceberModel>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<PermissaoUsuarioModel>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<ItensPedidoVendaEstoqueIndex>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<PedidoVendaSaidaEstoqueIndex>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<Select2Model>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<GrupoUsuarioModel>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<ItensPedidoCompraEstoqueIndex>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<PedidoCompraEntradaEstoqueIndex>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<MaxNotaFiscal>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<PedidoCompraModelIndex>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<PedidoVendaIndex>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<ItensPedidoVendaModel>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<PedidoVendaModel>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<PedidoCompraModel>(entity =>
    {
      entity.HasNoKey();
    });

    modelBuilder.Entity<ItensPedidoCompraModel>(entity =>
    {
      entity.HasNoKey();
    });
```

## Scaffolding

**Siga os passos atentamente nesta ordem para que tudo funcione corretamente, é necessário seguir as regras de nomeclatura para que as referências se correspondam.**

### • Modelo Personalizado

Para que o scaffolding funcione da forma correta é necessário criar um *modelo personalizado* para utilizá-lo no scaffolding dos *controllers*.

Requisitos do modelo personalizado:
1. Conter *DisplayName* na classe. Não apenas nas propriedades da classe mas também nela em si. Exemplo:

```c#
namespace MWERPEntidades.Models
{
  [DisplayName("Histórico de Contas")]
  public class HistoricoContasModel
  {
```

2. Conter métodos ***ToDatabase*** e ***FromDatabase***, responsáveis por fazer a conversão do *modelo personalizado* para o *modelo do banco de dados* e vice-versa.
3. Caso seja necessário uso de tabelas estrangeiras também devemos contar com *Id**TabelaEstrangeira***, *Nome**TabelaEstrangeira*** e também ***TabelaEstrangeira**Select*, objeto responsável por fazer a conversão do *Select2Model*.
4. O scaffolding das views é feito com base no model. Por este motivo caso seja necessário inclusão de campos como valor em reais (este sendo *decimal* ou *double*), cpf e cep basta incluir as palavras **Valor**, **CEP** e **CPF** nos nomes das propriedades que o *scaffolding* já será feito com as devidas máscaras. Campos de data bastam apenas ser do tipo *DateTime* (podendo ser nulo).

Em caso de dúvidas, utilize o ***HistoricoComprasModel*** como referência.

### • Services

1. Clique com botão direito na pasta *Services*.
2. Clique em *Adicionar > Novo item com scaffold...*
3. Selecione *Controlador de API com ações, usando o Entity Framework* .
4. Escolha a classe do modelo, este deve ser o **modelo do banco de dados** e **NÃO** um modelo personalizado.
5. Escolha o nome, por padrão ele fará sugestão de ***E_NomeModelo**Controller*, porém **ALTERE** para ***NomeModelo**Services*.
6. Seu novo *services* foi gerado com sucesso.
7. Abra seu arquivo recém criado e altere os campos necessários.

### • Controllers e Views

Para executar este passo é necessário que já exista o modelo personalizado como descrito acima.

1. Clique com botão direito na pasta *Controllers*.
2. Clique em *Adicionar > Novo item com scaffold...*
3. Selecione *Controlador MVC com exibições, usando o Entity Framework* .
4. Escolha a classe do modelo, este deve ser o **modelo personalizado** e **NÃO** o modelo do banco de dados.
5. Escolha o nome, por padrão ele fará sugestão de ***NomeModel**sController*, porém **ALTERE** para ***Nome**Controller*.
6. Seu novo *controller* com suas respectivas *views* foi gerado com sucesso. Por padrão ele também gera *views* de **Details** e **Delete**, desnecessárias para nosso uso, devendo ser apagadas.
