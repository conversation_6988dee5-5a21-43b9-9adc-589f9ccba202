﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;
using Microsoft.AspNetCore.Authorization;
using System.Configuration;
using System.Globalization;
using Microsoft.Extensions.Configuration;

namespace UniContabil.Controllers
{
  public class ClassificacaoNotasFiscaisController : LibController
  {
    private LancamentosServices LancamentosServices
    {
      get
      {
        if (_LancamentosServices == null)
          _LancamentosServices = new LancamentosServices(ContextoUsuario.UserLogged);

        return _LancamentosServices;
      }
    }
    private LancamentosServices _LancamentosServices;

    public ClassificacaoNotasFiscaisController(IConfiguration configuration)
    {
      _configuration = configuration;
    }

    private IConfiguration _configuration { get; }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index()
    {
      try
      {
        if (ContextoUsuario.UserLogged.IdFilial.HasValue)
        {
          List<LancamentosModel> list = LancamentosServices.Get(0, null);
          ViewBag.HasConvite = 0;

          if (ContextoUsuario.UserLogged.ListConvite.Count > 0)
          {
            ViewBag.HasConvite = 1;
          }

          return View(list);
        }
        else
        {
          MessageAdd(new Message(MessageType.Error, "Não foi possível encontrar a Filial."));
          return RedirectToAction("Index", "HistoricoContas");

        }
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }
    [HttpGet]
    //[NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View();
    }

    [AllowAnonymous]
    public JsonResult Create(AddLancamento addLancamento)
    {
      try
      {
        addLancamento.CodUser = ContextoUsuario.UserLogged.IdUsuario;
        int cod = LancamentosServices.Create(addLancamento);

        E_ClassificacaoDocumentos classifi = new ClassificacaoDocumentosServices().GetById(addLancamento.CodClass);

        //if (classifi.InfRecibo.HasValue && classifi.InfRecibo.Value)
        //{
        //  string path = _configuration["ReportPath"];
        //  byte[] pdf = new ClassificacaoDocumentosServices().GeraNF(cod, path);

        //  E_Lancamentos lanc = new LancamentosServices().GetById(cod).ToDatabase();

        //  lanc.L_NomeAnexo = "Recibo " + classifi.CD_Descricao;
        //  lanc.L_Extensao = "pdf";
        //  new LancamentosServices().Edit(lanc);

        //  System.IO.File.WriteAllBytes(string.Format("{0}{1}_{2}.{3}", _configuration["CaminhoDocs"], lanc.L_Id, lanc.L_DataCriacao.ToString("dd_MM_yyyy"), "pdf"), pdf);

        //  string base64 = Convert.ToBase64String(pdf);

        //}

        return Json(new { CodLancamento = cod, Erro = false, Mensage = "Salvo com Sucesso!" });
      }
      catch (Exception ex)
      {
        return Json(new { Erro = true, Mensage = ex.Message });
      }
    }

    public JsonResult Edit(LancamentosModel lancamentos)
    {
      try
      {
        string path = _configuration["CaminhoDocs"];
        
        if (lancamentos.IdClassificacao.HasValue)
        {
          E_ClassificacaoDocumentos classifi = new ClassificacaoDocumentosServices().GetById(lancamentos.IdClassificacao.Value);

          if ((classifi.InfRecibo.HasValue && classifi.InfRecibo.Value) && (lancamentos.geraRecibo.HasValue && lancamentos.geraRecibo.Value))
          {
            if (string.IsNullOrEmpty(lancamentos.NomeRecibo) && string.IsNullOrEmpty(lancamentos.CPFRecibo))
              throw new Exception("Para a geração de recibo é necessário preencher os campos Nome e CPF.");

            if ((string.IsNullOrEmpty(lancamentos.ValorFormat) || lancamentos.ValorFormat == "") && (!lancamentos.Valor.HasValue || lancamentos.Valor.Value == 0))
              throw new Exception("Para a geração de recibo é necessário preencher o campo Valor.");

            
            byte[] pdf = new ClassificacaoDocumentosServices().GeraNF(lancamentos.Id, _configuration["ReportPath"], lancamentos);
           
            string fileName = string.Format("{0}{1}_{2}.{3}", path, lancamentos.Id, lancamentos.DataCriacao.ToString("dd_MM_yyyy"), lancamentos.Extensao);
            System.IO.File.WriteAllBytes(fileName, pdf);

            string base64 = Convert.ToBase64String(pdf);

            lancamentos.NomeAnexo = "Recibo " + classifi.CD_Descricao;
            lancamentos.ArquivoBase64 = base64;
            lancamentos.Extensao = "pdf";
          }
        }

        if (!string.IsNullOrEmpty(lancamentos.CPF))
        {
          if (lancamentos.CPF.Length <= 11)
            Infrastructure.Helpers.ValidadorHelper.ValidaCPF(lancamentos.CPF);
          else
          {
            if (Infrastructure.Helpers.ValidadorHelper.ValidaCNPJ(lancamentos.CPF))
              throw new Exception("CNPJ Inválido.");
          }
        }

        LancamentosServices.EditLancamento(lancamentos, path);

        return Json(new { Erro = false, Mensage = "Salvo com Sucesso!" });
      }
      catch (Exception ex)
      {
        return Json(new { Erro = true, Mensage = ex.Message });
      }
    }

    public JsonResult _GetTreeview(int Tipo)
    {
      List<TreeviewClassificacaoModel> Treeview = new List<TreeviewClassificacaoModel>();

      Treeview = new TreeviewService(ContextoUsuario.UserLogged).GetTreeviewClassificacao(Tipo);
      JsonResult json = new JsonResult(Treeview);

      return json;
    }

    [HttpGet]
    public JsonResult GeraReport(int Id)
    {
      try
      {
        string path = _configuration["CaminhoDocs"];
        byte[] pdf = new ClassificacaoDocumentosServices().GeraNF(Id, path);
      
        LancamentosModel lancamentos = new LancamentosServices().GetById(Id);
        string fileName = string.Format("{0}{1}_{2}.{3}", path, lancamentos.Id, lancamentos.DataCriacao.ToString("dd_MM_yyyy"), lancamentos.Extensao);

        System.IO.File.WriteAllBytes(fileName, pdf);

        return Json(new { erro = false, message = "Relatorio Gerado", fileName = fileName });
      }
      catch (Exception ex)
      {
        return Json(new { erro = true, message = ex.Message });
      }
    }

    [HttpGet]
    public JsonResult DeleteComprovante(int id)
    {
      try
      {
        string path = _configuration["CaminhoDocs"];
        LancamentosModel lancamentos = new LancamentosServices().GetById(id);
        string fileName = string.Format("{0}{1}_{2}.{3}", path, lancamentos.Id, lancamentos.DataCriacao.ToString("dd_MM_yyyy"), lancamentos.Extensao);

        System.IO.File.Delete(fileName);

        lancamentos.Extensao = null;
        lancamentos.ArquivoBase64 = null;

        E_Lancamentos model = lancamentos.ToDatabase();
        new LancamentosServices().Edit(model);

        return Json(new { erro = false, message = "Anexo removido."});
      }
      catch (Exception ex)
      {
        return Json(new { erro = true, message = ex.Message });
      }
    }

    public PartialViewResult _PartialDocumento(int id)
    {
      try
      {
        ViewBag.CodLancamento = id;
        return PartialView("_Documento");
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }
    public PartialViewResult _GetLancamento(int id)
    {
      try
      {
        LancamentosModel lancamento = new LancamentosModel();
        lancamento = LancamentosServices.GetById(id);
        return PartialView("InfoDocumentos", lancamento);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public PartialViewResult _GetPartialRecibo()
    {
      try
      {
        return PartialView("_PartialRecibo");
      }
      catch (Exception ex)
      {
        throw ex;
      }
    }

    public PartialViewResult _GetGridClassificado(int tipo, string Ano, int? codclass = 0)
    {
      try
      {
        int? ano = string.IsNullOrEmpty(Ano) ? 0 : int.Parse(Ano);

        List<LancamentosModel> list = new List<LancamentosModel>();
        list = LancamentosServices.Get(tipo, ano, codclass);
        return PartialView("_GridClassificados", list);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public PartialViewResult Delete(int id, int tipo, string Ano, int? classif = 0)
    {
      try
      {
        E_Lancamentos lanc = new LancamentosServices().GetById(id).ToDatabase();
        lanc.L_Delete = true;
        LancamentosServices.Edit(lanc);

        int? ano = string.IsNullOrEmpty(Ano) ? 0 : int.Parse(Ano);

        List<LancamentosModel> list = new List<LancamentosModel>();
        list = LancamentosServices.Get(tipo, ano, classif);

        new LogLancamentosService(ContextoUsuario.UserLogged).LogLancamento(id, TipoAcaoLancamento.Geracao, ContextoUsuario.UserLogged.IdUsuario);

        return PartialView("_GridClassificados", list);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }
  }
}
