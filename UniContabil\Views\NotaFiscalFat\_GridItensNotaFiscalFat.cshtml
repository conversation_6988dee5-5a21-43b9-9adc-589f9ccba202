﻿@using UniContabilEntidades.Models
@model List<ItensNotaFiscalFatVendaModel>

<div>

</div>


@if (Model.Count > 0)
{
<div class="table-group-content">
  <table >
    <thead>
      <tr>
        <th scope="col">
          Sequencial
        </th>
        <th scope="col">
          Código
        </th>
        <th scope="col">
          Descrição
        </th>
        <th scope="col">
          Quantidade
        </th>
        <th scope="col">
          Valor Unitário
        </th>
        <th scope="col">
          Valor Total Item
        </th>
        <th scope="col">
          Desconto %
        </th>
        <th scope="col">
          Desconto R$
        </th>
        <th scope="col">
        </th>
      </tr>
    </thead>
    <tbody>
      @foreach (ItensNotaFiscalFatVendaModel item in Model)
      {
        <tr>
          <td>
            @item.SeqItem
          </td>
          <td>
            @item.Codigo
          </td>
          <td>
            @item.Descricao
          </td>
          <td>
            @item.Qtde
          </td>
          <td style="text-align:right">
            @if (item.ValorUnitario.HasValue)
            {
              <text>R$ @item.ValorUnitario.Value</text>
            }
            else
            {
              <text>R$ 0,00</text>
            }
          </td>
          <td style="text-align:right">
            @if (item.ValorItem.HasValue)
            {
              <text>R$ @item.ValorItem.Value</text>
            }
            else
            {
              <text>R$ 0,00</text>
            }
          </td>
          <td style="text-align:center">
            @if (item.DescontoPerc.HasValue)
            {
              <text>@item.DescontoPerc %</text>
            }
            else
            {
              <text> 0% </text>
            }
          </td>
          <td style="text-align:right">
            @if (item.Desconto.HasValue)
            {
              <text>R$ @item.Desconto.Value</text>
            }
            else
            {
              <text>R$ 0,00</text>
            }
          </td>
          <td style="display: flex; justify-content: space-around;">
            <a href="#" class="btn btn-warning btn-sm EditItem" title="Apagar" data-codigo="@item.Codigo">
              <i class="mw-drip-document-edit"></i>
            </a>
            <a href="#" class="btn btn-danger btn-sm DeleteCompra" title="Apagar" data-url="@Url.Action("Delete", "ItensNotaFiscalFatVenda", new {codigo = item.Codigo })">
              <i class="mw-drip-trash"></i>
            </a>
          </td>
        </tr>
      }
    </tbody>
  </table>
</div>
}
