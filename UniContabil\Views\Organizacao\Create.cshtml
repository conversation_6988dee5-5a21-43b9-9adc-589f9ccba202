﻿@model UniContabilEntidades.Models.OrganizacaoModel
@using UniContabil.Infrastructure.Controls

    @{
    ViewData["Title"] = "Nova " + @Html.DisplayNameFor(model => model);
    }
  <div class="card">
    <form asp-action="Create">
      <div class="card-header">
        <div class="card-title">
        </div>
        <div class="card-options">
        </div>
      </div>
      <div class="card-body">
        @Html.AntiForgeryToken()
        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
              <div class="form-group">
                <label asp-for="RazaoSocial" class="control-label"></label>
                <input asp-for="RazaoSocial" class="form-control" />
                <span asp-validation-for="RazaoSocial" class="text-danger"></span>
              </div>
              <div class="form-group form-check">
                <label class="form-check-label">
                  <input class="form-check-input" asp-for="Ativo" /> @Html.DisplayNameFor(model => model.Ativo)
                </label>
              </div>
      </div>
      <div class="card-footer">
        <a asp-action="Index" class="link link-info">Voltar</a>
        <input type="submit" value="Adicionar" class="btn btn-primary" />
      </div>
    </form>
  </div>

