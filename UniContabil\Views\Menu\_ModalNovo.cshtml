﻿<div class="modal fade" id="modalNovoMenu" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="modalNovoMenuLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modalNovoMenuLabel">Nova Página</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="inputNomeMenu">Nome</label>
          <input class="form-control" id="inputNomeMenu" aria-describedby="nomeMenuHelp">
          <div class="invalid-feedback">
            Por favor insira o nome do menu.
          </div>
          <small id="nomeMenuHelp" class="form-text text-muted">O nome do menu que aparecerá para os usuários.</small>
        </div>
        <div class="form-check" data-toggle="tooltip" data-placement="top" title="Marcando este campo você irá criar um agrupador de submenus. Cada submenu representa uma página com suas respectivas permissões.">
          <input class="form-check-input" type="checkbox" id="Agrupador">
          <label class="form-check-label" for="Agrupador">
            É agrupador
          </label>
        </div>
        <div class="form-check" data-toggle="tooltip" data-placement="top" title="Marcando este campo será definido se este menu será visivel ou não.">
          <input class="form-check-input" type="checkbox" id="Visibilidade">
          <label class="form-check-label" for="Visibilidade">
            Menu Visível
          </label>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="salvarNovoMenu">Salvar</button>
      </div>
    </div>
  </div>
</div>