﻿<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>

<div class="modal fade" id="modalclassificacao" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="modalclassificacao" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Classificação Movimentação Recorrente</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" style="max-height: 75vh; overflow: auto;">
        <div class="row">
          <div class="form-group">
            <div class="col-md-12">
              <input type="radio" name="Tipo" class="custom-radio" value="1" id="Tipo_1" />
              <label>Receita</label>
              <input type="radio" name="Tipo" class="custom-radio" value="2" id="Tipo_2" checked="checked" />
              <label>Despesa</label>
            </div>
          </div>
        </div>
        <div id="tree" style=" max-height: calc(100vh - 100px);">
        </div>
      </div>
      <div class="modal-footer">
      </div>
    </div>
  </div>
</div>


<script>
  $(document).ready(function () {
    MovimentacaoRecorrente.LoadTree();
  });
</script>