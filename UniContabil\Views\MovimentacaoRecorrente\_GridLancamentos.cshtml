﻿@using UniContabilEntidades.Models.DataBase
@using System.Globalization
@model List<IndexLancamentos>
<div class="table-group card">
  <div class="table-group card-header">
    <div class="row">
      <div class="btn-group">
        <button id="EditItens" type="button" style="border-bottom-left-radius: 0px; border-top-left-radius: 0px; " class="btn btn-outline-warning">Alterar Valor dos Lançamentos</button>
        <button id="DeleteItens" type="button" class="btn btn-outline-danger">Deletar Lançamentos</button>
      </div>
    </div>
  </div>
  <!--<div class="card-header" id="CabecalhoPedidoCompra">
    <div class="form-group col-md-6">
    </div>
    <div class="card-options">-->
  @*@if (ContextoUsuario.HasPermission("PedidoCompra", TipoFuncao.Create))
    {
      <button type="button" class="btn btn-success" onclick="location.href='@Url.Action("Create", "NotaFiscalFat")'">Adicionar Nota</button>
    }*@
  <!--</div>
  </div>-->
  <div class="table-group-content table-Itens-Compra" style="margin-top: 30px;">
    <table>
      <thead>
        <tr>
          <th>
            <div class="row">
              <input id="selectAll" type="checkbox" />
            </div>
          </th>
          <th>
            Data
          </th>
          <th>
            Valor
          </th>
        </tr>
      </thead>
      <tbody>
        @for (int i = 0; i < Model.Count; i++)
        {
          var Movimentacao = Model[i];
          <tr id="TableRow">
            <td>
              <input type="checkbox" name="select_@i" data-index="@i" Codigo="@Model[i].Id" class="check" id="select_@i" />
            </td>
            <td>
              @Movimentacao.DataLancamento
            </td>
            <td>
              @Movimentacao.Valor
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
  <div class="card-footer">
  </div>
</div>