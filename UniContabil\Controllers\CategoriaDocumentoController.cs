﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;

namespace UniContabil.Controllers
{
  public class CategoriaDocumentoController : LibController
  {
    public CategoriaDocumentoController()
    { }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        IPagedList<CategoriaDocumentoModel> List = new CategoriaDocumentoServices().GetPagedList(page);
        ViewBag.PageItems = List;
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public PartialViewResult Create()
    {
      return PartialView("Create");
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public JsonResult Create(CategoriaDocumentoModel categoriadocumentomodel)
    {
      try
      {
        if (ModelState.IsValid)
        {
         int id = new CategoriaDocumentoServices().Create(categoriadocumentomodel);

          return Json(new { erro = false, cod = id });
        }
        else
        {
          throw new Exception("Favor verificar os campos.");
        }
      }
      catch (Exception Ex)
      {
        return Json(new { erro = true, message = Ex.Message});
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public PartialViewResult Edit(int id)
    {
      try
      {
        CategoriaDocumentoModel categoriadocumentomodel = new CategoriaDocumentoModel().FromDatabase(new CategoriaDocumentoServices().GetById(id));

        if (categoriadocumentomodel == null)
          return PartialView(null);

        return PartialView("Edit", categoriadocumentomodel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return PartialView(null);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public JsonResult Edit(CategoriaDocumentoModel categoriadocumentomodel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          var model = categoriadocumentomodel.ToDatabase();
          new CategoriaDocumentoServices().Edit(model);
          return Json(new { erro = false });
        }
        return Json(new { erro = false });
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return Json(new { erro = true, message = Ex.Message });
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public JsonResult DeleteCategoria(int id)
    {
      try
      {
        CategoriaDocumentoServices Service = new CategoriaDocumentoServices();
        var model = Service.GetById(id);

        if (model.E_ClassificacaoDocumentos.Count > 0)
          throw new Exception("Existem classificações para essa categora.");

        Service.Delete(model);
        return Json(new { sucesso = true });
      }
      catch (Exception Ex)
      {
        return Json(new { sucesso = false, mensagem = Ex.Message });
      }
    }
    
    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string Delete(int id)
    {
      try
      {
        CategoriaDocumentoServices Service = new CategoriaDocumentoServices();
        var model = Service.GetById(id);
        Service.Delete(model);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }

    [HttpGet]
    public ActionResult GetOrganizacaoSelect(string term)
    {
      OrganizacaoServices Service = new OrganizacaoServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
  }

}