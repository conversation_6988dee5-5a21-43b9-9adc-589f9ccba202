﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MWHelper.Exceptions;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniContabilDomain.Services;
using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;

namespace UniContabil.Controllers
{
  [Authorize]
  public class ContabilidadeController : LibController
  {

    private ContabilidadeService ContabilidadeService
    {
      get
      {
        if (_ContabilidadeService == null)
          _ContabilidadeService = new ContabilidadeService(ContextoUsuario.UserLogged);

        return _ContabilidadeService;
      }
    }
    private ContabilidadeService _ContabilidadeService;

    public ActionResult Index(int Id)
    {
      return RedirectToAction("Edit", new { Id = Id });
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult IndexAprovacao(string cpfcnpj, string nome, int? status, int page = 1)
    {
      try
      {
        ViewBag.nome = nome;
        ViewBag.cpfcnpj = cpfcnpj;
        ViewBag.status = status;

        IPagedList<ContabilidadeIndexModel> List = new ContabilidadeService().GetIndexAll(page, nome, cpfcnpj, status);
        return View(List);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Visualizar)]
    public ActionResult Details(int Id)
    {
      E_Contabilidade entity = new ContabilidadeService().GetById(Id);
      ContabilidadeEditModel model = new ContabilidadeEditModel();

      model.Id = entity.C_Id;
      model.Cep = entity.C_Cep;
      model.Complemento = entity.C_Complemento;
      model.Bairro = entity.C_Bairro;
      model.IdMunicipio = entity.C_IdMunicipio;
      model.IdUF = entity.C_IdUF;
      model.IsAtivo = entity.C_Ativo;
      model.Logradouro = entity.C_Logradouro;
      model.PrefixLogradouro = entity.C_PrefixoLogradouro;
      model.NrCRC = entity.C_NrCRC;
      model.NmrEndereco = entity.C_NumeroEndereco;
      model.Nome = entity.C_Nome;
      model.TelefoneCelular = entity.C_TelefoneCelular;
      model.TelefoneFixo = entity.C_TelefoneFixo;
      model.Cpf = entity.C_CPFCNPJ;
      model.Email = entity.C_Email;
      model.IsPJ = entity.C_CPFCNPJ.Length > 11 ? 1 : 0;
      model.EnumStatus = entity.C_IdStatusNavigation != null ? entity.C_IdStatusNavigation.SC_Enum : (int?)null;

      return View(model);
    }

    public ActionResult Edit(int? Id)
    {
      if (!Id.HasValue && ContextoUsuario.UserLogged.Contabilidade != null)
      {
        Id = ContextoUsuario.UserLogged.Contabilidade.Id;
      }

      if (!Id.HasValue)
        return RedirectToAction("Index", "ClassificacaoDocumentos");

      E_Contabilidade entity = new ContabilidadeService().GetById(Id.Value);
      E_StatusContabilidade entityStatus = new ContabilidadeService().GetStatusById(entity.C_IdStatus.Value);
      ContabilidadeEditModel model = new ContabilidadeEditModel()
      {
        Id = entity.C_Id,
        Cep = entity.C_Cep,
        Complemento = entity.C_Complemento,
        Bairro = entity.C_Bairro,
        IdMunicipio = entity.C_IdMunicipio,
        IdUF = entity.C_IdUF,
        IsAtivo = entity.C_Ativo,
        Logradouro = entity.C_Logradouro,
        PrefixLogradouro = entity.C_PrefixoLogradouro,
        NrCRC = entity.C_NrCRC,
        NmrEndereco = entity.C_NumeroEndereco,
        Nome = entity.C_Nome,
        TelefoneCelular = entity.C_TelefoneCelular,
        TelefoneFixo = entity.C_TelefoneFixo,
        Cpf = entity.C_CPFCNPJ,
        IsPJ = entity.C_CPFCNPJ.Length > 11 ? 1 : 0,
        Email = entity.C_Email,
        EnumStatus = entityStatus.SC_Enum
      };

      return View(model);
    }

    public PartialViewResult _GetGridAnexos(int IdContab)
    {
      List<E_AnexoContabilidade> model = new ContabilidadeService().GetAllAnexos(IdContab);
      return PartialView("_GridAnexos", model);
    }

    public JsonResult EnviarParaAprovacao(int IdContabilidade)
    {
      try
      {
        new ContabilidadeService().EnviaAprovacao(IdContabilidade);
        return Json(new { error = false, message = "Enviado para aprovação com sucesso!" });
      }
      catch (Exception ex)
      {
        return Json(new { error = true, message = "Ocorreu um erro, tente novamente mais tarde!" });
      }
    }

    public PartialViewResult _GetGridUsers(int IdContab)
    {
      List<UsuarioContabilidadeModel> model = new ContabilidadeService().GetAllUsuarioContabilidadeByIdContab(IdContab);
      return PartialView("_GridUsuariosContabilidade", model);
    }

    public FileContentResult VerAnexo(int Id)
    {
      ByteAnexoContabilidade model = new ContabilidadeService().GetAnexo(Id);
      return File(model.Bytes, model.MimmeType);
    }

    public JsonResult DeleteAnexo(int Id)
    {
      try
      {
        new ContabilidadeService().DeletarAnexo(Id);
        return Json(new { error = false, message = "Deletado com sucesso!" });
      }
      catch (Exception ex)
      {
        return Json(new { error = true, message = "Ocorreu um erro tente novamente mais tarde!" });
      }
    }

    public JsonResult DeleteUser(int id)
    {
      try
      {
        new ContabilidadeService().DeletarVinculoContabilidade(id);
        return Json(new { error = false, message = "Deletado com sucesso!" });
      }
      catch (Exception ex)
      {
        return Json(new { error = true, message = "Ocorreu um erro tente novamente mais tarde!" });
      }
    }

    public PartialViewResult _ModalCreateUserSemCpf(int IdContab)
    {
      BasicRegisterModel model = new BasicRegisterModel();
      model.IdContab = IdContab;
      model.TipoCadastro = (int)EnumTipoCadastro.Contabilidade;
      return PartialView("_ModalAdcUser", model);
    }

    public JsonResult VerificaCpf(string cpf)
    {
      try
      {
        E_Usuarios e_Usuarios = new ContabilidadeService().GetUserByCPF(cpf);
        if (e_Usuarios != null)
        {
          return Json(new
          {
            hasCpf = true
            ,
            nome = e_Usuarios.U_Nome
            ,
            idUser = e_Usuarios.U_Id
            ,
            email = e_Usuarios.U_Email
            ,
            telefone = e_Usuarios.U_TelefoneCelular
            ,
            error = false
          });
        }
        else
        {
          return Json(new { hasCpf = false, error = false });
        }
      }
      catch (Exception ex)
      {
        return Json(new { error = true });
      }
    }

    public PartialViewResult _ModalCreateUser(string CPF, bool isAdm, int IdContab)
    {
      ContabilidadeService contabilidadeService = new ContabilidadeService();
      BasicRegisterModel model = new BasicRegisterModel();
      E_Usuarios e_Usuarios = contabilidadeService.GetUserByCPF(CPF);
      if (e_Usuarios != null)
      {
        model.Email = e_Usuarios.U_Email;
        model.Nome = e_Usuarios.U_Nome;
        model.TelefoneCelular = e_Usuarios.U_TelefoneCelular;
        model.ConfirmarEmail = e_Usuarios.U_Email;
      }
      model.CPF = CPF;
      model.TipoCadastro = (int)EnumTipoCadastro.Contabilidade;
      //model.IsADMContab = isAdm;
      model.IdContab = IdContab;
      return PartialView("_ModalAdcUser", model);
    }

    [HttpPost]
    public JsonResult CreateContabilidadeUser(BasicRegisterModel model)
    {
      model.CPF = model.CPF.Replace("_", "").Replace("-", "").Replace(".", "");
      if(!new ValidaCPF().IsValid(model.CPF))
      {
        return Json(new
        {
          error = true,
          message = "CPF Inválido!"
        });
      }
        
      if (!model.IdUser.HasValue)
      {
        if (ModelState.IsValid)
        {
          try
          {
            model.CPF = model.CPF.Replace(".", "").Replace("-", "");
            model.TelefoneCelular = model.TelefoneCelular.Replace("(", "").Replace("-", "").Replace("_", "").Replace(")", "");
            E_Usuarios retorno = new AccountServices(ContextoUsuario.UserLogged).RegistrarUsuarioContabilidade(model);
            new ContabilidadeService().VincularUserContabilidade(new VincularUsuarioContabilidadeModel()
            {
              CPF = retorno.U_CPF,
              HasAdm = model.IsADMContab.Value,
              IdContabilidade = model.IdContab.Value
            });
            return Json(new
            {
              error = false,
              message = "Usuario criado, e vinculado com sucesso à contabilidade!"
            });
          }
          catch (CustomException ex)
          {
            return Json(new
            {
              error = true,
              message = ex.Message
            });
          }
          catch (Exception ex)
          {
            return Json(new
            {
              error = true,
              message = "Ocorreu um erro deconhecido, tente novamente mais tarde"
            });
          }
        }
        else
        {
          return Json(new
          {
            error = true,
            message = "Verifique os campos!"
          });
        }
      }
      else
      {
        try
        {
          new ContabilidadeService().VincularUserContabilidade(new VincularUsuarioContabilidadeModel()
          {
            CPF = model.CPF,
            HasAdm = model.IsADMContab ?? false,
            IdContabilidade = model.IdContab.Value
          });
          return Json(new
          {
            error = false,
            message = "Vinculado com sucesso à contabilidade!"
          });
        }
        catch(CustomException ex)
        {
          string[] splittedErr = ex.Message.Split(":");
          if (splittedErr[1].Equals("0"))
          {
            return Json(new { error = true, message = "Usuário já cadastrado na contabilildade.", HasCPF = true });
          }
          else if (splittedErr[1].Equals("1"))
          {
            return Json(new { error = true, message = "CPF não está cadastrado."});
          }
          if (splittedErr[1].Equals("2"))
          {
            return Json(new { error = true, message = "Este usuário não é do grupo contabilidade.", HasCPF = true });
          }
          return Json(new { error = true, message = "Erro desconhecido, tente novamente mais tarde.", HasCPF = true });
        }
        catch (Exception ex)
        {
          return Json(new
          {
            error = true,
            message = "Erro desconhecido, tente novamente!"
          });
        }
        
      }
    }

    [HttpPost]
    public JsonResult AdicionaCpfContabilidade(VincularUsuarioContabilidadeModel model)
    {
      try
      {
        new ContabilidadeService().VincularUserContabilidade(model);
        return Json(new { error = false, message = "Usuário vinculado com sucesso!", HasCPF = true });
      }
      catch (CustomException ex)
      {
        string[] splittedErr = ex.Message.Split(":");
        if (splittedErr[1].Equals("0"))
        {
          return Json(new { error = true, message = "Usuário já cadastrado na contabilildade.", HasCPF = true });
        }
        else if (splittedErr[1].Equals("1"))
        {
          return Json(new { error = true, message = "CPF não está cadastrado.", HasCPF = false, CPF = model.CPF, isAdm = model.HasAdm, idContab = model.IdContabilidade });
        }
        if (splittedErr[1].Equals("2"))
        {
          return Json(new { error = true, message = "Este usuário não é do grupo contabilidade.", HasCPF = true });
        }
        return Json(new { error = true, message = "Erro desconhecido, tente novamente mais tarde.", HasCPF = true });
      }
      catch (Exception ex)
      {
        return Json(new { error = true, message = "Erro desconhecido, tente novamente mais tarde.", HasCPF = true });
      }
    }

    [HttpPost]
    public ActionResult SalvaAnexo(AnexoContabilidadeModel model)
    {
      try
      {
        new ContabilidadeService().SalvaAnexo(model);
        MessageAdd(new Infrastructure.Controls.Message(Infrastructure.Controls.MessageType.Success, "Anexo adicionado com sucesso!"));
        return Json(new { erro = false, mensagem = "Anexo incluido"});
      }
      catch (Exception ex)
      {
        MessageAdd(new Infrastructure.Controls.Message(Infrastructure.Controls.MessageType.Error, "Ocorreu um erro, tente novamente mais tarde!"));
        return Json(new { erro = true, mensagem = "Não foi possivel inserir o anexo"});
      }
    }

    [HttpPost]
    public ActionResult Edit(ContabilidadeEditModel model)
    {
      try
      {
        new ContabilidadeService().Edit(model);
        MessageAdd(new Infrastructure.Controls.Message(Infrastructure.Controls.MessageType.Success, "Foi editado com sucesso!"));
        return View(model);
      }
      catch (CustomException ex)
      {
        MessageAdd(new Infrastructure.Controls.Message(Infrastructure.Controls.MessageType.Error, ex.Message));
        return View(model);
      }
      catch (Exception ex)
      {
        MessageAdd(new Infrastructure.Controls.Message(Infrastructure.Controls.MessageType.Error, "Ocorreu um erro, tente novamente mais tarde"));
        return View(model);
      }
    }
    
    [HttpPost]
    public JsonResult SalvarEnviarAprovacao(ContabilidadeEditModel model)
    {
      try
      {
        model.Cpf = model.Cpf.Replace(".", "").Replace("-", "").Replace(@"/", "");
        model.Cep = model.Cep.Replace(".", "").Replace("-", "");
        model.TelefoneCelular = model.TelefoneCelular.Replace("(", "").Replace("-", "").Replace(")", "");
        model.TelefoneFixo = model.TelefoneFixo.Replace("(", "").Replace("-", "").Replace(")", "");

        new ContabilidadeService().Edit(model);
        new ContabilidadeService().EnviaAprovacao(model.Id);
        return new JsonResult(new { Error = false, Mensage = "Dados salvos e Enviado para aprovação." });
      }
      catch (Exception ex)
      {
        return new JsonResult(new { Error = true, Mensage = ex.Message });
      }
    }

    [HttpPost]
    public JsonResult Aprovar(int id, string motivo)
    {
      try
      {
        ContabilidadeService.Aprovar(id, motivo);
        return new JsonResult(new { Error = false, Mensage = "Aprovação salva com sucesso" });
      }
      catch (Exception ex)
      {
        return new JsonResult(new { Error = true, Mensage = ex.Message });
      }
    }

    [HttpPost]
    public JsonResult Reprovar(int id, string motivo)
    {
      try
      {
        ContabilidadeService.Reprovar(id, motivo);
        return new JsonResult(new { Error = false, Mensage = "Reprovação salva com sucesso" });
      }
      catch (Exception ex)
      {
        return new JsonResult(new { Error = true, Mensage = ex.Message });
      }
    }
  }
}
