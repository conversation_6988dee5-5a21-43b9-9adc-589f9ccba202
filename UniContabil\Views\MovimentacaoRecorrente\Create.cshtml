﻿@using UniContabilEntidades.Models.DataBase
@model MovimentacoesReconrrentesCreateModel

<script src="~/Views/MovimentacaoRecorrente/MovimentacaoRecorrente.js?2=12"></script>
<input id="FileName" hidden />
@using (Html.BeginForm("Create", "MovimentacaoRecorrente", FormMethod.Post, new { enctype = "multipart/form-data" }))
{
  <div class="card">
    <div class="card-body">
      <div class="row">
        <div class="form-group col-md-12">
          <label>Descrição:</label>
          @Html.EditorFor(x => x.Descricao, new { htmlAttributes = new { @class = "form-control" } })
          @Html.ValidationMessageFor(x => x.Descricao)
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-3">
          <label>CPF/CNPJ:</label>
          @Html.EditorFor(x => x.CPFCNPJ, new { htmlAttributes = new { @class = "form-control CPFCNPJ", id = "CPFORCNPJ" } })
          @Html.ValidationMessageFor(x => x.CPFCNPJ)
        </div>
        <div class="form-group col-md-3" id="nomeCPF" @{if (!string.IsNullOrEmpty(Model.CPFCNPJ) && Model.CPFCNPJ.Length != 11) { <text> hidden</text> }}>
          <label>Nome Contratado/Fornecedor</label>
          @Html.EditorFor(x => x.Nome, new { htmlAttributes = new { @class = "form-control" } })
          @Html.ValidationMessageFor(x => x.Nome)
        </div>
        <div class="form-group col-md-3" id="dataCPF" @{if (!string.IsNullOrEmpty(Model.CPFCNPJ) && Model.CPFCNPJ.Length != 11) { <text> hidden</text> }}>
          <label>Data Nascimento:</label>
          @Html.EditorFor(x => x.DataNascimento, new { htmlAttributes = new { @class = "form-control", type = "date" } })
          @Html.ValidationMessageFor(x => x.DataNascimento)
        </div>
        <div class="form-group col-md-3">
          <label>Valor:</label>
          @Html.EditorFor(x => x.Valor, new { htmlAttributes = new { @class = "form-control moneyMask" } })
          @Html.ValidationMessageFor(x => x.Valor)
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-3">
          <label>Data Inicial:</label>
          @Html.EditorFor(x => x.DataInicial, new { htmlAttributes = new { @class = "form-control", type = "date" } })
          @Html.ValidationMessageFor(x => x.DataInicial)
        </div>
        <div class="form-group col-md-2">
          <label>Nr Ocorrencias:</label>
          @Html.EditorFor(x => x.NrOcorrencias, new { htmlAttributes = new { @class = "form-control" } })
          @Html.ValidationMessageFor(x => x.NrOcorrencias)
        </div>
        <div class="form-group col-md-3">
          <label>Tipo de Recorrencia:</label>
          <select name="TipoRecorrencia" class="form-control">
            @if (Model.TipoRecorrencia == 1)
            {
              <option selected value="1">Semanal</option>
              <option value="2">Mensal</option>
              <option value="3">Anual</option>
            }
            else if (Model.TipoRecorrencia == 2)
            {
              <option value="1">Semanal</option>
              <option selected value="2">Mensal</option>
              <option value="3">Anual</option>
            }
            else if (Model.TipoRecorrencia == 3)
            {
              <option value="1">Semanal</option>
              <option value="2">Mensal</option>
              <option selected value="3">Anual</option>
            }
            else
            {
              <option value="1">Semanal</option>
              <option value="2">Mensal</option>
              <option value="3">Anual</option>
            }
          </select>
          @Html.ValidationMessageFor(x => x.TipoRecorrencia)
        </div>
        <div class="form-group col-md-4" style=" margin-top: 30px;">
          <input type="file" name="anexo" hidden id="anexo" value="" />
          <span id="textoAnexo" class="btn" style="border-radius: 0; border-top-left-radius: 10px !important; border: black 1px solid; border-bottom-left-radius: 10px; text-overflow: ellipsis; max-width: 75%; white-space: nowrap; overflow: hidden; border-color: #ced4da; ">Nada Anexado</span><span style="margin-left:7px;"><button type="button" class="btn btn-outline-success" style=" border-bottom-left-radius: 0px; border-top-left-radius: 0px; margin-left: -8px;" id="btnAdcAnexo">Anexar</button></span>
        </div>
      </div>
      <div class="row" style="align-items: flex-end;">
        <div class="col-md-4 form-group">
          @Html.HiddenFor(a => a.IdClassificacao)
          <label>Classificação</label>
          <input class="form-control" readonly="readonly" name="Classificacao" id="Classificacao" />
        </div>
        <button type="button" class="btn btn-sm btn-info" id="classificar" style="max-height: 5vh;">
          Classificar
        </button>
      </div>
      <div class="row">
        <div class="btn-group col-md-2">
          <a asp-action="Index" class="btn btn-outline-info" style="border-bottom-left-radius: 0px; border-top-left-radius: 0px; margin-top: 10px;">Voltar</a>
          <button type="submit" class="btn btn-outline-success" style=" margin-top: 10px;">Criar</button>
        </div>
        <div class="form-group col-md-2">
        </div>

      </div>
    </div>
  </div>
}

@Html.Partial("ModalClassificacao")