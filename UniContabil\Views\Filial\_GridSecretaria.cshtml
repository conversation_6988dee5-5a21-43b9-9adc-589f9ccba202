﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model List<OrganizacaoUsuarioDetails>

@{
  ViewData["Title"] = "Usuario";
}
<style>
  .organizacoes {
    display: flex;
  }

  .card-custom {
    box-shadow: 0 7px 14px 0 #4145581a, 0 3px 6px 0 #00000012 !important;
    background-color: #fff;
    background-clip: border-box;
    padding: 5px 2px 5px 10px !important;
  }

  .spinner-menu {
    margin-left: 10pt !important;
  }
</style>

<div class="card-body" style="margin: 0px auto;padding-top: 2px !important;padding-right: 15px !important;padding-bottom: 2px !important;padding-left: 15px !important;">
  <div class="col-6">
    @*<div style="width: 100%;display: flex;">
        @if (ContextoUsuario.UserLogged.Contabilidade == null)
        {
          <input class="form-control" data-val="true" placeholder="Buscar Secretároa por CPF" id="SearchSecretaria" name="Search" type="text" value="">
          <button class="btn btn-outline-primary btn-sm" onclick="loadFiltroSecretaria()" id="BtnSearchSecretaria" style="height: 28px;margin-left: 2px;"> Buscar </button>
        }
      </div>*@

    <div class="row">
      @if (ContextoUsuario.UserLogged.Contabilidade == null)
      {
        <button class="btn btn-outline-success btn-sm" onclick="Filial.AdicionarUsuario()">Adicionar</button>
      }
    </div>
  </div>
</div>
<div class="card-body" style="display: flex;">

  <div class="col-12">
    <div class="body-filtro-secretaria-content">
    </div>
    <div class="body-secretaria-content">
      @await Html.PartialAsync("_GridVinculos", Model)
    </div>
    <div class="justify-content-center spinner-table" style="display: flex !important; margin-top: 10px;">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Carregando...</span>
      </div>
    </div>
  </div>
  <div class="justify-content-center spinner-menu" style="display:none !important;">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Carregando...</span>
    </div>
  </div>
</div>
<div class="card-footer">
  <a class="link link-info" asp-action="Index">Voltar</a>
</div>



<div class="modal fade" id="ModalCriarUsuario" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document" style="max-width: 1000px !important;">
    <div class="modal-content" style="min-height:calc(100vh - 80px);">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Vincular Secretária:</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">

      </div>
    </div>
  </div>
</div>
