﻿function reloadGroups() {
  $.get(GetURLBaseComplete() + "/GrupoUsuario/GetGrupos", function (data) {
    $(".group-list").html(data);

    setTimeout(function () {
      $(".group-list .list-group li")[0].click();
    }, 500);
  });
}

function getUsersGroup() {
  let idGrupo = $(".list-group li.active").data("grupo");

  if (!idGrupo)
    return;

  $(".group-detail").html("");
  $(".group-permissions").html("");
  $(".spinner-menu").attr("style", "display: flex !important; margin-top: 20px;");

  $("#group-permissions").removeClass("active");
  $("#group-users").addClass("active");

  $.get(GetURLBaseComplete() + "/GrupoUsuario/GetUsuariosGrupo/" + idGrupo, function (data) {
    $(".group-detail").html(data);
    $(".spinner-menu").attr("style", "display: none !important");
  });
}

function getPermissionsGroup() {
  $(".group-detail").html("");
  $(".spinner-menu").attr("style", "display: flex !important; margin-top: 20px;");

  $("#group-users").removeClass("active");
  $("#group-permissions").addClass("active");

  let idGrupo = $(".list-group li.active").data("grupo");

  if (!idGrupo)
    return;

  $.get(GetURLBaseComplete() + "/GrupoUsuario/ListPermissoesGrupo/" + idGrupo, function (data) {
    data = JSON.parse(data);
    if (data.Sucesso) {
      $('.group-permissions').html('');
      $('.group-permissions').append('<div class="jstree-permissions" style="margin-top: 15px"> </div >');
      $('.jstree-permissions')
        .on('select_node.jstree', function (event, element) {
          Alerta("Info", "Salvando alterações...", "blue", 2000);
          const obj = {
            IdItem: element.node.id,
            IdGrupo: idGrupo
          };

          $.post(GetURLBaseComplete() + "/GrupoUsuario/CreatePermissao", obj, function (data) {
            data = JSON.parse(data);
            if (data.Sucesso)
              Alerta("Sucesso", "Permissão adicionada com sucesso.", "green");
            else
              Alerta("Erro", data.Mensagem, "red");
          });
        }).on('deselect_node.jstree', function (event, element) {
          Alerta("Info", "Salvando alterações...", "blue", 2000);

          const obj = {
            IdItem: element.node.id,
            IdGrupo: idGrupo
          };

          $.post(GetURLBaseComplete() + "/GrupoUsuario/DeletePermissao", obj, function (data) {
            data = JSON.parse(data);
            if (data.Sucesso)
              Alerta("Sucesso", "Permissão removida com sucesso.", "green");
            else
              Alerta("Erro", data.Mensagem, "red");
          });
        })
        .jstree({
          'core': {
            'data': data.Treeview
          },
          "checkbox": {
            //'tie_selection': false,
            'whole_node': false,
          },
          "plugins": ["checkbox"]
        });
      $(".spinner-menu").attr("style", "display: none !important");
    }
    else {
      Alerta("Erro", data.Mensagem, "red", 5000);
      $(".spinner-menu").attr("style", "display: none !important");
    }
  });


  //$.get(GetURLBaseComplete() + "/GrupoUsuario/GetPermissoesGrupo", function (data) {
  //  $(".group-detail").html(data);
  //  $(".spinner-menu").attr("style", "display: none !important");
  //});
}

$(document).ready(function () {
  reloadGroups();

  $("#group-users").click(function () {
    getUsersGroup();
  });

  $(document).on("click", "#group-permissions", function () {
    getPermissionsGroup();
  });

  $("#group-delete").click(function () {
    let idGrupo = $(".list-group li.active").data("grupo");

    if (!idGrupo)
      return;

    swal({
      title: "Apagar",
      text: "Tem certeza que deseja apagar este grupo? Esta ação é irreversível e todas configurações de menu e de permissões para os usuários deste grupo serão desfeitas.",
      icon: "warning",
      buttons: ["Cancelar", "Apagar"],
      dangerMode: true,
    })
      .then((continuar) => {
        if (continuar) {
          Alerta("Info", "Excluindo grupo...", "blue", 2000);

          $.post(GetURLBaseComplete() + "/GrupoUsuario/Delete/" + idGrupo, function (data) {
            data = JSON.parse(data);
            if (data.Sucesso) {
              reloadGroups();
              Alerta("Sucesso", "Grupo excluído com sucesso.", "green");
            }
            else {
              Alerta("Erro", data.Mensagem, "red");
            }
          });
        }
      });
  });

  $("#salvarNovoGrupo").click(function () {
    const nomeGrupo = $("#inputNovoGrupo").val();
    if (nomeGrupo) {
      $("#inputNovoGrupo").removeClass("is-invalid");

      const obj = {
        Nome: nomeGrupo
      };

      $.post(GetURLBaseComplete() + "/GrupoUsuario/Create", obj, function (data) {
        data = JSON.parse(data);
        if (data.Sucesso) {
          reloadGroups();
          $("#inputNovoGrupo").val("");
          $('#modalNovoGrupo').modal('hide');
          Alerta("Sucesso", "Novo grupo gerado com sucesso.", "green", 5000);
        }
        else {
          Alerta("Erro", data.Mensagem, "red", 5000);
        }
      });
    }
    else {
      $("#inputNovoGrupo").addClass("is-invalid");
    }
  });
});