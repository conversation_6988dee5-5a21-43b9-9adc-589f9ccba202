﻿var documentoFilial = function () {
};

documentoFilial.Init = function () {
  $(document).on("click", ".delete", function () {

    var cod = $(this).data("cod");
    var tipo = $("#idtipo").val();
    $.ajax({
      type: "GET",
      dataType: "json",
      url: GetURLBaseComplete() + "/DocumentoFilial/Delete?id=" + cod,
      success: function (data) {
        if (!data.erro) {

          Alerta("Sucesso", "Documento removido com sucesso.", "success", 5000);
          documentoFilial.GetGridDocs(tipo);
        }
        else {
          Alerta("Atenção", data.message, "warning", 5000);
          documentoFilial.GetGridDocs(tipo);
        }
      },
    });
  });

  $(document).on("click", "#saveDoc", function () {

    $.ajax({
      type: "POST",
      dataType: "json",
      data: $("#formdoc").serialize(),
      url: GetURLBaseComplete() + "/DocumentoFilial/CreateModal",
      success: function (data) {
        if (!data.erro) {
          $("#ModalCriar").modal("hide");
          documentoFilial.GetGridDocs(data.idTipo);
        }
        else {
          Alerta("Atenção", data.message, "warning", 5000);
          documentoFilial.GetGridDocs(data.idTipo);
        }
      },
    });
  });

  $(document).on("click", ".DocumentoSelecionado", function () {
    $(".DocumentoSelecionado").removeClass("LinhaSelecionada");
    $(this).addClass("LinhaSelecionada");

    let idTipoDocumento = $(this).data("idtipo");
    documentoFilial.GetGridDocs(idTipoDocumento);

  });

  $(document).on("click", "#openModalCreatePasta", function () {
    $("#Descricao").val("");
    $("#ModalCriarPasta").modal("show");
  });

  $(document).on("click", "#openModalCreateDoc", function () {
    $("#Nome").val("");
    $("#Extensao").val("");
    $("#ArquivoBase64").val("");
    $("#titledoc").html("");
    $("#IdTipoDocumentoFilial").val($("#idtipo").val());
    $("#ModalCriar").modal("show");
  });

  $(document).on("click", "#AddAnexo", function () {
    $("#Anexodocumento").click();
  });

  $(document).on("change", "#Anexodocumento", function () {
    const file = $("#Anexodocumento")[0].files[0];
    const reader = new FileReader();
    reader.readAsDataURL(file);

    var extensao = file.type.split("/")[1];
    var title = file.name;

    $("#Extensao").val(extensao);

    $("#titledoc").html("");
    $("#titledoc").html(title);

    reader.onload = function () {
      $("#ArquivoBase64").val(reader.result.split('base64,')[1]);
    };
  });

  $(document).on('click', ".showfile", function () {
    var cod = $(this).data("cod");

    $.ajax({
      type: "GET",
      dataType: "json",
      url: GetURLBaseComplete() + "/DocumentoFilial/GetTypeDoc/" + cod,
      success: function (data) {
        if (!data.erro) {
          if (data.model.extensao == "pdf") {
            $("#showimg").attr("hidden", "hidden");

            $("#framepdf").removeAttr("hidden");
            $("#framepdf").attr("src", GetURLBaseComplete() + "/DocumentoFilial/Show/" + cod);
          }
          else {
            $("#showimg").removeAttr("hidden");
            $("#showimg").attr("src", GetURLBaseComplete() + "/DocumentoFilial/Show/" + cod);

            $("#framepdf").attr("hidden", "hidden");
          }

          $("#ModalShow").modal("show");
        }
        else {
          Alerta("Atenção", data.message, "warning", 5000);
        }
      },
    });

  });

  $(document).on('click', "#searchDocs", function () {
    var data = $("#dataSearch").val();
    var desc = $("#descSearch").val();
    var tipo = $("#idtipo").val();
    documentoFilial.GetGridDocsSearch(tipo, desc, data);
  });
};

documentoFilial.GetGridDocs = function (idtipo) {
  $.ajax({
    type: "GET",
    dataType: "html",
    url: GetURLBaseComplete() + "/DocumentoFilial/GetDocumentos?idTipoDocumento=" + idtipo,
    success: function (data) {

      $("#Divsearch").removeAttr("hidden");

      $("#gridDocumentos").html(data);

      $("#idtipo").val("");
      $("#idtipo").val(idtipo);

      $("#openModalCreateDoc").removeAttr("hidden");
    },
  });
}

documentoFilial.GetGridDocsSearch = function (idtipo, desc, data) {
  $.ajax({
    type: "GET",
    dataType: "html",
    url: GetURLBaseComplete() + "/DocumentoFilial/GetDocumentosSearch?idTipoDocumento=" + idtipo + "&desc=" + desc + "&dataDoc=" + data,
    success: function (resultado) {
      $("#Divsearch").removeAttr("hidden");

      $("#gridDocumentos").html("");
      $("#gridDocumentos").html(resultado);

      $("#idtipo").val("");
      $("#idtipo").val(idtipo);

      $("#descSearch").val(desc);
      $("#dataSearch").val(data);

      $("#openModalCreateDoc").removeAttr("hidden");
    },
  });
}

$(document).ready(function () {
  documentoFilial.Init();
});