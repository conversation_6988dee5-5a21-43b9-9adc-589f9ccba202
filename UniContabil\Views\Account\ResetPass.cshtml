﻿@using UniContabil.Infrastructure.Controls
@model UniContabilEntidades.Models.PassToRecovery
@{
  ViewBag.Title = "Esqueci a senha";
  Layout = "~/Views/Shared/_LayoutAccount.cshtml";
}

<link rel="stylesheet" href="~/Views/Account/Account.css?awa=ads" />

<div>
  <div class="col-md-3 divcard">
    <h4>Digite sua nova senha</h4>
    @using (Html.BeginForm("ResetPass", "Account", FormMethod.Post))
    {
      <input type="hidden" value="@ViewBag.Token" name="@Html.NameFor(x => x.token)"/>
      <div class="row">
        <div class="col-md-12">
          <div class="form-group">
            @Html.TextBoxFor(m => m.Pass, new { type = "password", @class = "form-control", placeholder = "Senha" })
            @Html.ValidationMessageFor(m => m.Pass)
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <div class="form-group">
            @Html.TextBoxFor(m => m.ConfirmPass, new { type = "password", @class = "form-control", placeholder = "Confirmar Senha" })
            @Html.ValidationMessageFor(m => m.ConfirmPass)
          </div>
        </div>
      </div>
      @*<div class="row">
        <div class="col-md-12">
          <div class="form-group">
            @Html.PasswordFor(m => m.Senha, new { @class = "form-control", placeholder = "Senha" })
            @Html.ValidationMessageFor(m => m.Senha)

          </div>
        </div>
      </div>*@
      @*<div class="row">
        <div class="col-md-12">
          <div class="form-group" style="float:left !important">
            <input type="checkbox" class="form-control" />
            Continuar Conectado
          </div>
        </div>
      </div>*@
      <div class="row" style="justify-content: center">
        <button class="btn" style="background-color: #2375aa;
                                    font-size: 15pt !important;
                                    color: white;" type="submit">
          Enviar
        </button>
      </div>
    }
    @*<div class="row" style="justify-content: center">
      <a class="" href="#">Esqueceu sua senha?</a>
    </div>*@
    <div class="row" style="justify-content: center">
      <a class="" href="@Url.Action("Register","Account")">Não possuo uma conta.</a>
    </div>
  </div>
</div>
<style>
  .divcard {
    position: fixed;
    right: 5%;
    top: 31%;
    width: 100%;
    height: 50vh;
    text-align: center;
  }

  .form-group {
    margin-bottom: 1rem !important;
  }

  input {
    background-color: #eef4f3 !important;
    height: calc(2.75rem + 2px) !important;
    color: #818181 !important;
    font-size: 15pt !important;
  }

  h4 {
    color: #2e7daf;
    font-family: monospace;
  }

  body {
    background-image: url('../Content/img/Imagem home unicontabil.png'), url('../Content/img/Logo_Unicontabil.jpeg'), url('../Content/img/fundo.svg');
    background-repeat: no-repeat !important;
    background-size: 40%, 30%, 100% !important;
    background-position-x: 15%, 95% !important;
    background-position-y: 0%, -50% !important;
  }
</style>