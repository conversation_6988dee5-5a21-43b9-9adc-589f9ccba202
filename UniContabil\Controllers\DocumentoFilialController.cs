﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using UniContabilDomain.Services;
using UniContabilEntidades.Models;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Microsoft.Extensions.Configuration;
using UniContabilEntidades.Models.DataBase;

namespace UniContabil.Controllers
{
  [Authorize]
  public class DocumentoFilialController : LibController
  {
    private TipoDocumentoFilialServices _tipoDocumentoFilialService;
    private DocumentoFilialServices _documentoFilialService;

    private IConfiguration _configuration { get; }

    public DocumentoFilialController(IConfiguration configuration)
    {
      _configuration = configuration;
    }

    public TipoDocumentoFilialServices TipoDocumentoFilialServices
    {
      get
      {
        _tipoDocumentoFilialService = new TipoDocumentoFilialServices(ContextoUsuario.UserLogged);
        return _tipoDocumentoFilialService;
      }
    }

    public DocumentoFilialServices DocumentoFilialServices
    {
      get
      {
        _documentoFilialService = new DocumentoFilialServices(ContextoUsuario.UserLogged);
        return _documentoFilialService;
      }
    }

    [HttpGet]
    public ActionResult Index()
    {
      try
      {
        TipoDocumentoFilialServices.Create();
        List<TipoDocumentoFilialListModel> list = TipoDocumentoFilialServices.GetListDocumentoFilial();

        return View(list);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        return View();
      }
    }

    [HttpGet]
    public PartialViewResult GetDocumentos(int idTipoDocumento)
    {
      List<DocumentoFilialListModel> list = DocumentoFilialServices.GetListDocumentoFilial(idTipoDocumento);
      return PartialView("_GridDocumentos", list);
    }


    [HttpGet]
    public PartialViewResult GetDocumentosSearch(int idTipoDocumento, string desc, DateTime? dataDoc)
    {
      List<DocumentoFilialListModel> list = DocumentoFilialServices.GetListDocumentoFilialSearch(idTipoDocumento, dataDoc, desc);
      return PartialView("_GridDocumentos", list);
    }


    [HttpPost]
    public JsonResult CreateModal(DocumentoFilialCreateModal model)
    {
      try
      {
        if (model.IdTipoDocumentoFilial == 0 || model.IdTipoDocumentoFilial == null)
          throw new Exception("É necessário seelcionar uma pasta.");

        string path = _configuration["DocFilial"];
        DocumentoFilialServices.CreateAux(model, path);

        return new JsonResult(new { erro = false, idTipo = model.IdTipoDocumentoFilial });
      }
      catch (Exception ex)
      {
        return new JsonResult(new { erro = true, message = ex.Message, idTipo = model.IdTipoDocumentoFilial });
      }
    }

    //[HttpPost]
    //public ActionResult CreateModal(DocumentoFilialCreateModal model)
    //{
    //  try
    //  {
    //    if (model.IdTipoDocumentoFilial == 0 || model.IdTipoDocumentoFilial == null)
    //      throw new Exception("É necessário seelcionar uma pasta.");

    //    string path = _configuration["DocFilial"];
    //    DocumentoFilialServices.CreateAux(model, path);

    //    MessageAdd(new Message(MessageType.Success, "Documento criado com sucesso."));
    //    return RedirectToAction("Index");
    //  }
    //  catch (Exception ex)
    //  {
    //    MessageAdd(new Message(MessageType.Error, ex.Message));
    //    return RedirectToAction("Index");
    //  }
    //}

    [HttpGet]
    public JsonResult Delete(int id)
    {
      try
      {
        string path = _configuration["DocFilial"];
        DocumentoFilialServices.DeleteAux(id, path);
        return new JsonResult(new { erro = false });
      }
      catch (Exception ex)
      {
        return new JsonResult(new { erro = true, message = ex.Message });
      }
    }

    public FileContentResult Download(int id)
    {
      try
      {
        string path = _configuration["DocFilial"];
        byte[] bt = DocumentoFilialServices.GetArquivoById(id, path);

        E_DocumentoFilial doc = DocumentoFilialServices.GetById<E_DocumentoFilial>(id);

        if (doc.DF_Extensao != "pdf")
          return File(bt, "application/" + doc.DF_Extensao, doc.DF_Nome + "." + doc.DF_Extensao);

        return File(bt, "application/pdf", doc.DF_Nome + "." + doc.DF_Extensao);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public JsonResult GetTypeDoc(int id)
    {
      try
      {
        DocumentoVisualizador doc = DocumentoFilialServices.GetById(id);

        return new JsonResult(new { erro = false, model = doc });
      }
      catch (Exception ex)
      {
        return new JsonResult(new { erro = true, message = ex.Message });
      }
    }

    public FileContentResult Show(int id)
    {
      try
      {
        string path = _configuration["DocFilial"];
        byte[] bt = DocumentoFilialServices.GetArquivoById(id, path);

        E_DocumentoFilial doc = DocumentoFilialServices.GetById<E_DocumentoFilial>(id);

        if (doc.DF_Extensao != "pdf")
          return File(bt, "application/" + doc.DF_Extensao);

        return File(bt, "application/pdf");
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }
  }
}
