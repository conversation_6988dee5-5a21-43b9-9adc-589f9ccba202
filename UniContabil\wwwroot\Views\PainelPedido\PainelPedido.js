﻿var PainelPedido = function () {
};

PainelPedido.tryingToReconnect = false;
PainelPedido.connected = false;

PainelPedido.init = function () {
  PainelPedido.Connection = new signalR.HubConnectionBuilder().withUrl(GetURLBaseComplete() + "/signalhub").withAutomaticReconnect().build();;

  PainelPedido.Connection.start().then(function () {
    PainelPedido.connected = true;
  }).catch(function (err) {
  });

  PainelPedido.Connection.on("painelpedido", function (idPedido) {
    PainelPedido.GetGrid(idPedido);
  });

  PainelPedido.Connection.on("updateStatus", function (idPedido) {
    PainelPedido.GetGrid(idPedido);
  });
};

PainelPedido.GetGrid = function (idPedido) {
  var url = GetURLBaseComplete() + '/PainelPedido/_GetLinhasPedidos';

  //var id = ;

  $.ajax({
    url: url,
    cache: false,
    dataType: "html",
    type: "GET",
    data: { id: idPedido },
    success: function (resposta) {
      if (resposta.Erro) {
        Alerta('Erro', resposta.Mensagem, 'red');
      } else {
        $("#" + idPedido).remove();
        $('.tablePainel').prepend(resposta)
      }
    }
  });
}

$(document).ready(function () {
  PainelPedido.init();
});


