﻿@using UniContabilEntidades.Models.DataBase
@using UniContabil.Infrastructure

@model List<UsuarioContabilidadeModel>
<div class="table-group-content">
  <table class="table table-sm table-striped table-bordered table-houver">
    <thead>
      <tr>
        <th>
          Nome
        </th>
        <th>
          CPF
        </th>
      </tr>
    </thead>
    <tbody>
      @for (int z = 0; z < Model.Count; z++)
      {
        <tr>
          <td>@Model[z].Nome</td>
          <td>@Model[z].Cpf</td>
          <td>
            @if (ContextoUsuario.UserLogged.Contabilidade != null && ContextoUsuario.UserLogged.Contabilidade.adm)
            {
              <button class="btn btn-outline-danger" onclick="Contabilidade.DeleteUser(@("'" + Model[z].IdUsuarioContab + "'"))">Excluir</button>
            }
          </td>
        </tr>
      }
    </tbody>
  </table>
</div>
