﻿@using UniContabil.Infrastructure.Controls
@using UniContabilEntidades.Models
@model ModalPesquisaCliente
<!-- Modal -->
<div class="modal fade" id="ModalPesquisaCliente" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document" style="max-width: 1000px !important;">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Detalhes Cliente</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row" style="align-items:center;">
          <div class="col-md-6">
            <div class="form-group">
              @Html.LabelFor(a => a.Campo1<PERSON>liente)
              <div style="display:flex; justify-content: space-between;">
                @Html.TextBoxFor(a => a.Campo1Cliente, new { @class = "form-control", @style = "width: 75%" })
                <button type="button" class="btn btn-info" style="width: 20%;" id="PesquisarModalCliente">Pesquisar</button>
              </div>
              @Html.ValidationMessageFor(a => a.Campo1Cliente)
            </div>
          </div>
       
        </div>
      </div>

      <div class="modal-footer" id="ResultCliente" style="height: 300px; overflow-y: scroll; align-items: flex-start !important;">

      </div>

    </div>
  </div>
</div>

