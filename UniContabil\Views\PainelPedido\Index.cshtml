﻿@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;
@model List<PainelPedidoModel>

@{
  Layout = "~/Views/Shared/_Layout.cshtml";
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();
  ViewBag.Title = "Painel de Pedidos";
}

<script src="~/Views/PainelPedido/PainelPedido.js"></script>
<script src="~/js/signalr/dist/browser/signalr.js"></script>

<div class="table-group card">
  <div class="table-group-content">
    <div class="card-body">
      <table class="table table-hover">
        <thead>
          <tr>
            <th class="cod">
              Código
            </th>
            <th>
              Data Pedido
            </th>
            <th class="cod">
              Cliente
            </th>
            <th>
              Status
            </th>
          </tr>
        </thead>
        <tbody class="tablePainel">
          @foreach (var item in Model)
          {
            <tr id="@item.Id">
              <td class="cod">
                @item.Codigo
              </td>
              <td>
                @item.Data.ToString("dd/MM/yyyy HH:mm")
              </td>
              <td class="cod">
                @item.Cliente
              </td>
              <td>
                @item.Status
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>

<style>
  .cod {
    text-align: left !important;
  }

  .table-group .table-group-content table th, .table-group .table-group-content table td {
    padding: 0px !important;
    text-align: center;
  }

  .table-group .table-group-content table th {
    font-size: 40pt !important;
  }

  .table-group .table-group-content table td {
    font-size: 20pt !important;
  }
</style>

