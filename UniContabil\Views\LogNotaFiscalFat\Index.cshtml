﻿@using UniContabilEntidades.Models
@model LogNotaFatModelIndex

@{
  ViewBag.Title = "Histórico da Nota Fiscal";
}

<script src="~/Views/LogNotaFiscalFat/LogNotaFiscalFat.js"></script>

<div class="table-group card">
  <div class="card-header">
    <div class="card-title">Logs da Nota - Nº de Série @(Model.SerieNota)</div>
    <div class="card-options">
      @*@if (ContextoUsuario.HasPermission("PedidoCompra", TipoFuncao.Create))
        {
          <button type="button" class="btn btn-success" onclick="location.href='@Url.Action("CreateInicial", "PedidoCompra")'">Adicionar Pedido Compra</button>
        }*@
    </div>
  </div>
  <div class="table-group-content">
    <table>
      <thead>
        <tr>
          <th scope="col">
            Código
          </th>
          <th scope="col">
            Mensagem
          </th>
          <th scope="col">
            Data Hora
          </th>
          <th scope="col">
            Status
          </th>
          <th scope="col">
            Ação
          </th>
          <th scope="col">
            Usuário Responsável
          </th>
        </tr>
      </thead>
      <tbody>
        @foreach (LogNotaFatModel log in Model.Logs)
        {
          <tr>
            <td>
              @log.Codigo
            </td>
            <td>
              @log.Mensagem
            </td>
            <td>
              @log.DataHora.ToString("dd/MM/yyyy HH:mm")
            </td>
            <td>
              @UniContabil.Infrastructure.EnumHelper.Description(log.StatusNota)
            </td>
            <td>
              @if (log.TipoAcao.HasValue)
              {
                @UniContabil.Infrastructure.EnumHelper.Description(log.TipoAcao)
              }
              else
              {
                <text> - </text>
              }
            </td>
            <td>
              @if (!string.IsNullOrEmpty(log.NomeUsuarioResponsavel))
              {
                @(log.NomeUsuarioResponsavel)
              }
              else
              {
                <text> - </text>
              }
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
  <div class="card-footer">
    <button type="button" value="Voltar" class="btn btn-outline-dark" onclick="location.href='@Url.Action("Edit", "NotaFiscalFat", new { id = Model.CodigoNota })'">Voltar</button>
  </div>
</div>
