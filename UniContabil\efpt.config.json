﻿{
  "ContextClassName": "UniContabilContext",
  "ContextNamespace": null,
  "DefaultDacpacSchema": null,
  "IncludeConnectionString": false,
  "ModelNamespace": null,
  "OutputContextPath": null,
  "OutputPath": "Models\/Database",
  "ProjectRootNamespace": "UniContabil",
  "SelectedHandlebarsLanguage": 0,
  "SelectedToBeGenerated": 0,
  "Tables": [
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Banco]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_CentroCusto]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Cliente]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_CondPgto]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_ContaContabel]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_ContasAPagar]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_ContasReceber]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_ContatoCliente]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_ContatoFornecedor]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Estoque]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Filial]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_FormaPgto]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Fornecedor]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_FotoProduto]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Grupo]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_GrupoProduto]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_GrupoUsuario]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_HistoricoContas]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_HistoricoPadrao]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Inventario]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_ItensNotaFiscalComp]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_ItensNotaFiscalFatVenda]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_ItensPedido]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_ItensPedidoCompra]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_LocalEstoque]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Menu]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_MenuFuncao]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_MenuFuncaoGrupo]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Modulo]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_MovimentacaoBancaria]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_MovimentoEstoque]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Natureza]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_NotaFiscalCompras]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_NotaFiscalFat]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Organizacao]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_OrganizacaoUsuario]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_PedidoCompra]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_PedidoVendas]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_PgtosTitPagar]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_PgtoTitReceber]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Processados]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Produto]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_SaldoLocalEstoque]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_SeriesNotas]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_SubGrupoProduto]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_TipoCliente]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_TipoFilial]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_TipoFornecedor]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_TipoLancamento]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_TipoTitulo]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_TituloReceber]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_TitulosAPagar]"
    },
    {
      "HasPrimaryKey": true,
      "Name": "[dbo].[E_Usuarios]"
    }
  ],
  "UseDatabaseNames": true,
  "UseDbContextSplitting": false,
  "UseFluentApiOnly": false,
  "UseHandleBars": false,
  "UseInflector": false,
  "UseLegacyPluralizer": false,
  "UseNodaTime": false,
  "UseSpatial": false,
  "UseStoredProcedures": false
}