﻿var ClassificacaoNotasFiscais = {};
var Tipo = 2;
var IdReloadGrid = null;
var Idclassificacao = null;

ClassificacaoNotasFiscais.GetTipo = function () {
  var listcheck = $(".custom-radio");

  for (var i = 0; i < listcheck.length; i++) {
    if ($(".custom-radio")[i].checked)
      Tipo = $(".custom-radio")[i].value;

    return Tipo;
  }
}

ClassificacaoNotasFiscais.makeTreeItem = function (el) {
  return $("<a>", {
    id: $(el).attr("id") + "_anchor",
    class: "jstree-anchor",
    href: "#"
  });
}

ClassificacaoNotasFiscais.Save = function (id) {
  let cpfcnpj = $("#CPFCNPJ").val();

  if (cpfcnpj) {
    cpfcnpj = cpfcnpj.replaceAll(".", "");
    cpfcnpj = cpfcnpj.replaceAll("-", "");
    cpfcnpj = cpfcnpj.replaceAll("/", "");
  }

  var model = {
    Id: $("#Id").val(),
    Descricao: $("#Descricao").val(),
    DataLancamento: $("#DtaLancamentoFormat").val(),
    DataCriacao: $("#DataCriacao").val(),
    Valor: $("#ValorFormat").val(),
    IdClassificacao: id,
    NomePF: $("#NomePF").val(),
    CPF: cpfcnpj,
    DtaNascimento: $("#DtaNascimentoFormat").val(),
    IdFormaPgto: $("#IdFormaPgto").val(),
    IdCartaoCredito: $("#CartaoCreditoSelect_id").val(),
    IdBanco: $("#BancoSelect_id").val(),
    ArquivoBase64: null,
    NomeAnexo: null,
    Extensao: null
  };

  if ($("#Anexodocumento")[0]) {
    const file = $("#Anexodocumento")[0].files[0];

    if (file) {
      const reader = new FileReader();
      reader.readAsDataURL(file);

      reader.onload = function () {
        model.ArquivoBase64 = reader.result.split('base64,')[1];
        model.NomeAnexo = file.name;
        model.Extensao = file.name.split('.')[1];

        $.ajax({
          url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/Edit",
          dataType: "json",
          type: "POST",
          data: model,
          success: function (data) {
            if (!data.erro) {

              ClassificacaoNotasFiscais.AtualizaGrid(1, IdReloadGrid);
              swal({
                title: "Sucesso",
                text: data.mensage,
                icon: "success",
              });
            } else {
              swal({
                title: "Atenção",
                text: data.mensage,
                icon: "warning",
              });
            }
          }
        });

      };
    }
    else {
      $.ajax({
        url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/Edit",
        dataType: "json",
        type: "POST",
        data: model,
        success: function (data) {
          if (!data.erro) {
            ClassificacaoNotasFiscais.AtualizaGrid(1, IdReloadGrid);
            ClassificacaoNotasFiscais.AtualizaInfo();
            swal({
              title: "Sucesso",
              text: data.mensage,
              icon: "success",
            });
          } else {
            swal({
              title: "Atenção",
              text: data.mensage,
              icon: "warning",
            });
          }
        }
      });
    }
  }
  else {
    $.ajax({
      url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/Edit",
      dataType: "json",
      type: "POST",
      data: model,
      success: function (data) {
        if (!data.erro) {
          ClassificacaoNotasFiscais.AtualizaGrid(1, IdReloadGrid);
          ClassificacaoNotasFiscais.AtualizaInfo();
          swal({
            title: "Sucesso",
            text: data.mensage,
            icon: "success",
          });
        } else {
          swal({
            title: "Atenção",
            text: data.mensage,
            icon: "warning",
          });
        }
      }
    });
  }
}

ClassificacaoNotasFiscais.init = function () {
  $(document).on("dnd_stop.vakata", function (e, data) {
    var t = $(data.event.target);
    var targetNode = $('#tree').jstree(true).get_node($(data.event.target));
    if (targetNode) {
      if (targetNode.children) {
        if (targetNode.children.length != 0) {
          swal({
            title: "Atenção",
            text: "Não é possível incluir neste local.",
            icon: "warning",
          });
        } else {
          ClassificacaoNotasFiscais.Save(targetNode.a_attr.codClassificacao);
        }
      }
    }
  });

  $(document).on('click', "#btnRecibo", function () {
    var id = $(this).attr('codigoLancamento');
    $.ajax({
      type: 'GET',
      url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/GeraReport/" + id,
      tradicional: true,
      dataType: "json",
      success: function (data) {
        if (!data.erro) {
          window.open(GetURLBaseComplete() + "/Anexo/Download/" + data.fileName, "_blank");
        } else {
          alert("Não foi possivel gerar o relatório, gentileza tentar novamente.");
        }
      },
      error: function (err) {
        console.log(err);
      }
    });
  });

  $('#Docs').draggable({
    cursor: 'move',
    helper: 'clone',
    start: function (e, ui) {
      var item = $("<div>", {
        id: "jstree-dnd",
        class: "jstree-default"
      });
      $("<i>", {
        class: "jstree-icon jstree-er"
      }).appendTo(item);
      item.append("Documento: " + $("#Descricao").val());
      var idRoot = $(this).attr("id").slice(0, -2);
      var newId = idRoot + "-" + ($("#tree [id|='" + idRoot + "'][class*='jstree-node']").length + 1);
      return $.vakata.dnd.start(e, {
        jstree: true,
        obj: ClassificacaoNotasFiscais.makeTreeItem(this),
        nodes: [{
          id: newId,
          text: $(this).text(),
          icon: "fa fa-flag-o"
        }]
      }, item);
    }
  });

  $(document).on("click", ".custom-radio", function () {
    Tipo = $(this).val();

    $("#tree").jstree(true).refresh()
  });

  $(document).on("click", "td", function () {
    var Cod = $(this).data("cod");
    if (Cod) {
      ClassificacaoNotasFiscais.AtualizaInfo(Cod);
    }
  });

  $(document).on("dblclick.jstree", function (e) {

    if ($("#InfoUserPartial")[0].children.length > 0 && $(".selecionado").data("classificado") == 0) {
      let node = $(e.target).closest("a");

      const attr = node[0].attributes;
      ClassificacaoNotasFiscais.Save(attr.codclassificacao.value);
    }
    else {
      swal({
        title: "Atenção",
        text: "Não é possível classificar. Favor selecione um documento.",
        icon: "warning",
      });
    }
  });

  $(document).on("click", ".classif", function () {

    $(".classif").removeClass("selecionado");
    $(this).addClass("selecionado");

    if ($(".selecionado").data("classificado") == 0) {
      ClassificacaoNotasFiscais.AtualizaGrid(0, null);
      data.node.a_attr.codClassificacao = null;
    }
  });

  $(document).on("change", "#ano", function () {
    ClassificacaoNotasFiscais.AtualizaGrid(1, IdReloadGrid);
  });

  $(document).on("click", ".delete", function () {
    let id = $(this).data("cod");

    $.ajax({
      url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/Delete",
      dataType: "html",
      type: "GET",
      data: {
        id: id,
        Tipo: $(".selecionado").data("classificado"),
        Ano: $("#ano").val(),
        classif: Idclassificacao
      },
      success: function (retorno) {
        $(".divTable").html("");
        $(".divTable").html(retorno);

        $("#InfoUserPartial").html("");
        $("#Docs").html("");
      }
    });
  });

  $(document).on("click", "#Edit", function () {

    var cpfcnpj = $("#CPFCNPJ").val();
    var cpfcnpjRecibo = $("#CPFRecibo").val();
    var id = $("#Id").val();
    var gerarecibo = false;

    if ($("#geraRecibo")[0] != undefined) {
      gerarecibo = $("#geraRecibo")[0].checked;
    }

    if (cpfcnpj) {
      cpfcnpj = cpfcnpj.replaceAll(".", "");
      cpfcnpj = cpfcnpj.replaceAll("-", "");
      cpfcnpj = cpfcnpj.replaceAll("/", "");
    }

    if (cpfcnpjRecibo) {
      cpfcnpjRecibo = cpfcnpjRecibo.replaceAll(".", "");
      cpfcnpjRecibo = cpfcnpjRecibo.replaceAll("-", "");
      cpfcnpjRecibo = cpfcnpjRecibo.replaceAll("/", "");
    }

    if (($("#IdFormaPgto").val() != undefined && $("#IdFormaPgto").val() != 0)) {
      if ($("#IdFormaPgto").val() != '1') {
        if (($("#IdCartaoCredito").val() == undefined || $("#IdCartaoCredito").val() == 0) && ($("#IdCartaoCredito").val() == undefined || $("#IdCartaoCredito").val() == 0)) {
          swal({
            title: "Atenção",
            text: "É necessário informar o Banco / Cartão de Crédito",
            icon: "warning",
          });
          return;
        }
      }
    }

    let obj = {
      Id: id,
      Descricao: $("#Descricao").val(),
      DataLancamento: $("#DtaLancamentoFormat").val(),
      DataCriacao: $("#DataCriacao").val(),
      Valor: $("#ValorFormat").val(),
      IdClassificacao: $("#IdClassificacao").val(),
      NomePF: $("#NomePF").val(),
      CPF: cpfcnpj,
      CPFRecibo: cpfcnpjRecibo,
      NomeRecibo: $("#NomeRecibo").val(),
      geraRecibo: gerarecibo,
      DtaNascimento: $("#DtaNascimentoFormat").val(),
      TipoPessoaDespesa: $("#TipoPessoaDespesa").val(),
      IdFormaPgto: $("#IdFormaPgto").val(),
      IdCartaoCredito: $("#IdCartaoCredito").val(),
      IdBanco: $("#IdBanco").val(),
      ArquivoBase64: null,
      NomeAnexo: null,
      Extensao: null
    }

    if ($("#Anexodocumento")[0]) {
      const file = $("#Anexodocumento")[0].files[0];

      if (obj.Id) {
        if (file) {
          const reader = new FileReader();
          reader.readAsDataURL(file);

          if (gerarecibo) {
            if (file) {
              reader.onload = function () {
                if (file) {
                  obj.ArquivoBase64 = reader.result.split('base64,')[1];
                  obj.NomeAnexo = file.name;
                  obj.Extensao = file.name.split('.')[1];
                }

                $.ajax({
                  url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/Edit",
                  dataType: "json",
                  type: "POST",
                  data: obj,
                  success: function (retorno) {
                    if (!retorno.erro) {
                      if (IdReloadGrid) {
                        ClassificacaoNotasFiscais.AtualizaGrid(1, IdReloadGrid);
                        $(".classif").removeClass("selecionado");
                        $("#Classificados").addClass("selecionado");
                      }
                      else {
                        ClassificacaoNotasFiscais.AtualizaGrid(0, null);
                        $(".classif").removeClass("selecionado");
                        $("#Naoclass").addClass("selecionado");
                      }

                      ClassificacaoNotasFiscais.AtualizaInfo(id);

                      swal({
                        title: "Sucesso",
                        text: retorno.mensage,
                        icon: "success",
                      });
                    } else {
                      swal({
                        title: "Atenção",
                        text: retorno.mensage,
                        icon: "warning",
                      });
                    }
                  }
                });

              };
            }
            else {
              $.ajax({
                url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/Edit",
                dataType: "json",
                type: "POST",
                data: obj,
                success: function (retorno) {
                  if (!retorno.erro) {
                    if (IdReloadGrid) {
                      ClassificacaoNotasFiscais.AtualizaGrid(1, IdReloadGrid);
                      $(".classif").removeClass("selecionado");
                      $("#classificados").addClass("selecionado");
                    }
                    else {
                      ClassificacaoNotasFiscais.AtualizaGrid(0, null);
                      $(".classif").removeClass("selecionado");
                      $("#Naoclass").addClass("selecionado");
                    }
                    ClassificacaoNotasFiscais.AtualizaInfo(id);
                    swal({
                      title: "Sucesso",
                      text: retorno.mensage,
                      icon: "success",
                    });
                  } else {
                    swal({
                      title: "Atenção",
                      text: retorno.mensage,
                      icon: "warning",
                    });
                  }
                }
              });
            }
          }
          else {
            if (file) {
              reader.onload = function () {
                obj.ArquivoBase64 = reader.result.split('base64,')[1];
                obj.NomeAnexo = file.name;
                obj.Extensao = file.name.split('.')[1];

                $.ajax({
                  url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/Edit",
                  dataType: "json",
                  type: "POST",
                  data: obj,
                  success: function (retorno) {
                    if (!retorno.erro) {
                      if (IdReloadGrid) {
                        ClassificacaoNotasFiscais.AtualizaGrid(1, IdReloadGrid);
                        $(".classif").removeClass("selecionado");
                        $("#Classificados").addClass("selecionado");
                      }
                      else {
                        ClassificacaoNotasFiscais.AtualizaGrid(0, null);
                        $(".classif").removeClass("selecionado");
                        $("#Naoclass").addClass("selecionado");
                      }

                      ClassificacaoNotasFiscais.AtualizaInfo(id);

                      swal({
                        title: "Sucesso",
                        text: retorno.mensage,
                        icon: "success",
                      });
                    } else {
                      swal({
                        title: "Atenção",
                        text: retorno.mensage,
                        icon: "warning",
                      });
                    }
                  }
                });

              };
            }
          }

        }
        else {
          $.ajax({
            url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/Edit",
            dataType: "json",
            type: "POST",
            data: obj,
            success: function (retorno) {
              if (!retorno.erro) {
                if (IdReloadGrid) {
                  ClassificacaoNotasFiscais.AtualizaGrid(1, IdReloadGrid);
                  $(".classif").removeClass("selecionado");
                  $("#classificados").addClass("selecionado");
                }
                else {
                  ClassificacaoNotasFiscais.AtualizaGrid(0, null);
                  $(".classif").removeClass("selecionado");
                  $("#Naoclass").addClass("selecionado");
                }
                ClassificacaoNotasFiscais.AtualizaInfo(id);
                swal({
                  title: "Sucesso",
                  text: retorno.mensage,
                  icon: "success",
                });
              } else {
                swal({
                  title: "Atenção",
                  text: retorno.mensage,
                  icon: "warning",
                });
              }
            }
          });
        }
      }
    }
    else {
      $.ajax({
        url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/Edit",
        dataType: "json",
        type: "POST",
        data: obj,
        success: function (retorno) {
          if (!retorno.erro) {
            if (IdReloadGrid) {
              ClassificacaoNotasFiscais.AtualizaGrid(1, IdReloadGrid);
              $(".classif").removeClass("selecionado");
              $("#classificados").addClass("selecionado");
            }
            else {
              ClassificacaoNotasFiscais.AtualizaGrid(0, null);
              $(".classif").removeClass("selecionado");
              $("#Naoclass").addClass("selecionado");
            }

            ClassificacaoNotasFiscais.AtualizaInfo(id);

            swal({
              title: "Sucesso",
              text: retorno.mensage,
              icon: "success",
            });
          } else {
            swal({
              title: "Atenção",
              text: retorno.mensage,
              icon: "warning",
            });
          }
        }
      });
    }
  });

  $(document).on('click', ".TipoPessoaDespesa", function () {
    tipo = $(this).val();

    if (tipo == "1")
      $("#PessoaDespesa_PF").show();
    else {
      $("#PessoaDespesa_PF").hide();
    }

  });

  $(document).on('click', "#geraRecibo", function () {

    if ($(this)[0].checked) {
      $.ajax({
        url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/_GetPartialRecibo",
        dataType: "html",
        type: "GET",
        success: function (retorno) {
          $("#dadosrecibo").show();
          $("#dadosrecibo").html("");
          $("#dadosrecibo").html(retorno);
          MWERP.IniciaMascaras();
        }
      });
    }
    else {
      $("#dadosrecibo").html("");
      $("#dadosrecibo").hide();
    }

  });

  $(document).on('click', "#useLancamento", function () {
    $("#CPFRecibo").val($("#CPFCNPJ").val());
    $("#NomeRecibo").val($("#NomePF").val());
  });

  $(document).on("click", "#deleteRecibo", function () {
    var cod = $("#codLancamento").val();
    $.ajax({
      url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/DeleteComprovante/" + cod,
      dataType: "json",
      type: "GET",
      success: function (data) {
        if (!data.erro) {
          ClassificacaoNotasFiscais.AtualizaInfo(cod);
          swal({
            title: "Sucesso",
            text: data.message,
            icon: "success",
          });
        } else {
          swal({
            title: "Atenção",
            text: data.message,
            icon: "warning",
          });
        }
      }
    });
  })
};

ClassificacaoNotasFiscais.LoadTree = function () {
  $("#tree").jstree({
    'core': {
      'check_callback': function (
        operation,
        node,
        node_parent,
        node_position,
        more
      ) {
        if (more.dnd != null && more.dnd == true) {
          if (operation === "move_node") {
            return false;
          } else if (operation === "delete_node") {
            return false;
          }
        }
      },
      'data': {
        'url': function () {
          return GetURLBaseComplete() + '/ClassificacaoNotasFiscais/_GetTreeview?Tipo=' + ClassificacaoNotasFiscais.GetTipo();
        },
        'dataType': 'json'
      },
      'themes': {
        'icons': false
      }
    },
    'plugins': ["contextmenu", "dnd"],
    'contextmenu': {
      items: function ($node) {

        console.log($node);

        let contextItens = {
          "insert_item": {
            "label": "Incluir Movimentação",
            "action": function (obj) {
              const codclass = $(obj.reference).attr("codclassificacao");
              var addLancamento = {
                Tipo: ClassificacaoNotasFiscais.GetTipo(),
                CodUser: null,
                CodFilial: $("#Filial").data("filial"),
                CodClass: codclass
              }

              $.ajax({
                url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/Create",
                dataType: "json",
                type: "POST",
                data: addLancamento,
                success: function (retorno) {
                  if (!retorno.Erro) {
                    ClassificacaoNotasFiscais.AtualizaGrid(1, codclass);
                    var Cod = retorno.codLancamento;
                    if (Cod) {
                      $.ajax({
                        url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/_GetLancamento",
                        dataType: "html",
                        type: "GET",
                        data: {
                          id: parseInt(Cod)
                        },
                        success: function (retorno) {
                          $("#InfoUserPartial").html("");
                          $("#InfoUserPartial").html(retorno);
                          $.ajax({
                            type: 'GET',
                            url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/_PartialDocumento/" + Cod,
                            tradicional: true,
                            dataType: "html",
                            success: function (data) {
                              $("#Docs").html(data);

                              if ($("#Extensao").val() == undefined || $("#Extensao").val() == "" || $("#Extensao").val() == null) {
                                $("#AddAnexo").show();
                                $("#iframeDoc").hide();
                                $("#imgDoc").hide();
                              }

                              $("#Docs").show();

                              //$("#codLancamento").val(Cod);
                              MWERP.IniciaMascaras();
                            },
                            error: function (err) {
                              console.log(err);
                            }
                          });
                        }
                      });
                    }
                  }
                }
              });

            }
          }
        };
        return contextItens;
      }
    }
  }).on("select_node.jstree", function (e, data) {
    $(".classif").removeClass("selecionado");
    $("#classificados").addClass("selecionado");
    Idclassificacao = data.node.a_attr.codClassificacao;
    ClassificacaoNotasFiscais.AtualizaGrid(1, data.node.a_attr.codClassificacao);
    IdReloadGrid = data.node.a_attr.codClassificacao;
  });
}

ClassificacaoNotasFiscais.AtualizaGrid = function (tipo, codclass) {
  $.ajax({
    url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/_GetGridClassificado",
    dataType: "html",
    type: "GET",
    data: {
      Tipo: tipo,
      Ano: $("#ano").val(),
      codclass: codclass
    },
    success: function (retorno) {
      $(".divTable").html("");
      $(".divTable").html(retorno);
      $("#Docs").html("");
      $("#InfoUserPartial").html("");
    }
  });
};

ClassificacaoNotasFiscais.AtualizaInfo = function (id) {
  $.ajax({
    url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/_GetLancamento",
    dataType: "html",
    type: "GET",
    data: {
      id: parseInt(id)
    },
    success: function (retorno) {
      $("#InfoUserPartial").html("");
      $("#InfoUserPartial").html(retorno);
      $.ajax({
        type: 'GET',
        url: GetURLBaseComplete() + "/ClassificacaoNotasFiscais/_PartialDocumento/" + id,
        tradicional: true,
        dataType: "html",
        success: function (data) {
          $("#Docs").html(data);

          if ($("#Tipo_2")[0].checked && ($("#IdClassificacao").val() != '' && $("#IdClassificacao").val() != undefined)) {
            $("#formapgtoselect").show();
          }
          else {
            $("#formapgtoselect").hide();
          }

          if ($("#Extensao").val() == undefined || $("#Extensao").val() == "" || $("#Extensao").val() == null) {
            $("#AddAnexo").show();
            $("#iframeDoc").hide();
            $("#imgDoc").hide();
          }

          $("#Docs").show();

          $("#codLancamento").val(id);
          MWERP.IniciaMascaras();
        },
        error: function (err) {
          console.log(err);
        }
      });
    }
  });
}

ClassificacaoNotasFiscais.FormaPgtoDespesa = function (forma, idforma) {
  $("#IdFormaPgto").val(idforma);

  if (!forma) {
    $("#formaCredito").attr("hidden", "hidden");
    $("#formabanco").attr("hidden", "hidden");

    $("#IdFormaPgto").val("");
  }
  else if (forma == "Cartão de Crédito") {
    $("#formaCredito").removeAttr("hidden", "hidden");
    $("#formabanco").attr("hidden", "hidden");

  }
  else {
    $("#formaCredito").attr("hidden", "hidden");
    $("#formabanco").removeAttr("hidden", "hidden");
  }
}

$(document).ready(function () {
  ClassificacaoNotasFiscais.init();
  ClassificacaoNotasFiscais.LoadTree();

  $('#resizebleDiv').resizable();
});
