﻿@model UniContabilEntidades.Models.HistoricoContasModel
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;

@{
  ViewData["Title"] = @Html.DisplayNameFor(model => model);
  Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="table-group card"> 
  <div class="card-header">
    <div @*class="card-options"*@>
      @if (ContextoUsuario.HasPermission("HistoricoContas", TipoFuncao.Create))
      {
        <a class="btn btn-success" asp-action="Create"><i class="mw-drip-plus"></i></a>
      }
    </div>
  </div>
  <div class="table-group-content">
    <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
      <thead>
        <tr>
          <th>
            Médico/Clinica
          </th>
          <th>
            @Html.DisplayNameFor(model => model.Descricao)
          </th>
          <th>
            @Html.DisplayNameFor(model => model.Codigo)
          </th>
          <th>
            @Html.DisplayNameFor(model => model.DataEmissao)
          </th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        @foreach (UniContabilEntidades.Models.HistoricoContasModel item in ViewBag.PageItems)
        {
          <tr>
            <td>
              @Html.DisplayFor(modelItem => item.NomeOrganizacao)
            </td>
            <td>
              @Html.DisplayFor(modelItem => item.Descricao)
            </td>
            <td>
              @Html.DisplayFor(modelItem => item.Codigo)
            </td>
            <td>
              @Html.DisplayFor(modelItem => item.DataEmissao)
            </td>
            <td>
              @if (ContextoUsuario.HasPermission("HistoricoContas", TipoFuncao.Edit))
              {
               <a class="btn btn-warning btn-sm" asp-action="Edit" asp-route-id="@item.Id"><i class="mw-drip-document-edit"></i></a>
              }
              @if (ContextoUsuario.HasPermission("HistoricoContas", TipoFuncao.Delete))
              {
                <button class="link link-danger" onclick="DeleteItem('@this.ViewContext.RouteData.Values["controller"].ToString()', @item.Id)">Apagar</button>
              }
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
  <div class="card-footer">
    @Html.PagedListPager((IPagedList)ViewBag.PageItems, page => Url.Action("Index", new { page }),
    new PagedListRenderOptions
         {
      LiElementClasses = new string[] { "page-item" },
      PageClasses = new string[] { "page-link" },
      Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
      MaximumPageNumbersToDisplay = 5
    })
  </div>
</div>

