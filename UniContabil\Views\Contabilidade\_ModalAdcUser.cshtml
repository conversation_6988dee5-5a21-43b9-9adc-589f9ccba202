﻿@model UniContabilEntidades.Models.BasicRegisterModel

<div id="divCadastro">
  <div class="form-group">
    <form id="formCadastroContabilidade">
      @Html.HiddenFor(m => m.TipoCadastro)
      @Html.HiddenFor(m => m.IdContab)
      @Html.HiddenFor(m => m.IdUser, new { id = "IdUser" })
      <div class="row">
        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(m => m.CPF)
            @Html.TextBoxFor(m => m.CPF, new { type = "text", @class = "CPF form-control", id = "CPFUser" })
            @Html.ValidationMessageFor(m => m.CPF)
          </div>
        </div>
        <div class="col-md-10">
          <div class="form-group">
            @Html.LabelFor(m => m.Nome)
            @Html.TextBoxFor(m => m.Nome, new { type = "text", @class = "form-control", id = "NomeUser" })
            @Html.ValidationMessageFor(m => m.Nome)
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-4">
          <div class="form-group">
            @Html.LabelFor(m => m.Email)
            @Html.TextBoxFor(m => m.Email, new { type = "text", @class = "EMAIL form-control", id = "EmailUser" })
            @Html.ValidationMessageFor(m => m.Email)
          </div>
        </div>
        <div class="col-md-4">
          <div class="form-group">
            @Html.LabelFor(m => m.ConfirmarEmail)
            @Html.TextBoxFor(m => m.ConfirmarEmail, new { type = "text", @class = "EMAIL form-control", Id = "ConfirmEmail" })
            @Html.ValidationMessageFor(m => m.ConfirmarEmail)
          </div>
        </div>
        <div class="col-md-4">
          <div class="form-group">
            @Html.LabelFor(m => m.TelefoneCelular)
            @Html.TextBoxFor(m => m.TelefoneCelular, new { type = "text", @class = "telefone form-control", id = "TelefoneUser" })
            @Html.ValidationMessageFor(m => m.TelefoneCelular)
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 PassUser">
          <div class="form-group">
            @Html.LabelFor(m => m.Senha)
            @Html.TextBoxFor(m => m.Senha, new { type = "password", @class = "form-control" })
            @Html.ValidationMessageFor(m => m.Senha)
          </div>
        </div>
        <div class="col-md-6 PassUser">
          <div class="form-group">
            @Html.LabelFor(m => m.ConfirmarSenha)
            @Html.TextBoxFor(m => m.ConfirmarSenha, new { type = "password", @class = "form-control" })
            @Html.ValidationMessageFor(m => m.ConfirmarSenha)
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12" style="display: flex; align-items: flex-end;">
          <div style="display:flex">
            <input type="checkbox" name="@Html.NameFor(x => x.IsADMContabFloat)" />
            <label style="margin-top: 3px; margin-left: 5px;">O usuário terá perfil de administrador junto a esta contabilidade ?</label>
          </div>
        </div>
      </div>

    </form>
  </div>
  <div class="row" style="justify-content: space-evenly;">
    <button class="btn btn-primary col-5" onclick="Contabilidade.EnviarAdicionarContab()" id="BtnCadastrar">Cadastrar e vincular</button>
  </div>
</div>
