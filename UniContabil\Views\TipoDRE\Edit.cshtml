﻿@model UniContabilEntidades.Models.TipoDREModel
@using UniContabil.Infrastructure.Controls

    @{
    ViewData["Title"] = "Editar " + @Html.DisplayNameFor(model => model);
      Layout = "~/Views/Shared/_Layout.cshtml";
    }
  <div class="card">
    <form asp-action="Edit">
      <div class="card-header">
        <div class="card-title">
        </div>
        <div class="card-options">
        </div>
      </div>
      <div class="card-body">
        @Html.AntiForgeryToken()
        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
              <div class="form-group">
                <label asp-for="Descricao" class="control-label"></label>
                <input asp-for="Descricao" class="form-control" />
                <span asp-validation-for="Descricao" class="text-danger"></span>
              </div>
              <div class="form-group">
                <label asp-for="Enum" class="control-label"></label>
                <input asp-for="Enum" class="form-control" />
                <span asp-validation-for="Enum" class="text-danger"></span>
              </div>
              <div class="form-group">
                <label asp-for="Ordem" class="control-label"></label>
                <input asp-for="Ordem" class="form-control" />
                <span asp-validation-for="Ordem" class="text-danger"></span>
              </div>



      </div>
      <div class="card-footer">
        <a asp-action="Index" class="link link-info">Voltar</a>
        <input type="submit" value="Salvar" class="btn btn-primary" />
      </div>
    </form>
  </div>
