﻿@model UniContabilEntidades.Models.TipoFaturamentoModel
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Novo Tipo Faturamento";
}

@*<h1>Criar @Html.DisplayNameFor(model => model)</h1>*@

<div class="card">
  <form asp-action="Create">
    <div class="card-body">
      @Html.AntiForgeryToken()
      <div asp-validation-summary="ModelOnly" class="text-danger"></div>
      <!--Nome System.String-->

      <div class="col-md-4">
        <div class="form-group">
          <label asp-for="IdOrganizacao" class="control-label"></label>
          @Html.Select2("OrganizacaoSelect", "GetOrganizacaoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdOrganizacao), this.ViewContext.RouteData.Values["controller"].ToString())
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          <label asp-for="IdFilial" class="control-label"></label>
          @Html.Select2("FilialSelect", "GetFilialSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdFilial), this.ViewContext.RouteData.Values["controller"].ToString())
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          <label asp-for="Descricao" class="control-label"></label>
          <input asp-for="Descricao" class="form-control" />
          <span asp-validation-for="Descricao" class="text-danger"></span>
        </div>
      </div>

      @*<div class="col-md-4">
      <div class="form-group">
        @Html.CheckBoxFor(a => a.RetenIss.Value)
        @Html.LabelFor(a => a.RetenIss)
        @Html.ValidationMessageFor(a => a.RetenIss)
      </div>
    </div>*@

      <div class="col-md-4">
        <div class="form-group">
          @Html.CheckBoxFor(a => a.CalculaIss)
          @Html.LabelFor(a => a.CalculaIss)
          @Html.ValidationMessageFor(a => a.CalculaIss)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.AliquotaIss)
          @Html.TextBoxFor(a => a.AliquotaIss, new { @class = "form-control money" })
          @Html.ValidationMessageFor(a => a.AliquotaIss)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.CheckBoxFor(a => a.CalculaCMS)
          @Html.LabelFor(a => a.CalculaCMS)
          @Html.ValidationMessageFor(a => a.CalculaCMS)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.AliquotaCMS)
          @Html.TextBoxFor(a => a.AliquotaCMS, new { @class = "form-control money" })
          @Html.ValidationMessageFor(a => a.AliquotaCMS)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.CheckBoxFor(a => a.CalculaIR)
          @Html.LabelFor(a => a.CalculaIR)
          @Html.ValidationMessageFor(a => a.CalculaIR)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.AliquotaIR)
          @Html.TextBoxFor(a => a.AliquotaIR, new { @class = "form-control money" })
          @Html.ValidationMessageFor(a => a.AliquotaIR)
        </div>
      </div>


      <div class="col-md-4">
        <div class="form-group">
          @Html.CheckBoxFor(a => a.CalculaPIS)
          @Html.LabelFor(a => a.CalculaPIS)
          @Html.ValidationMessageFor(a => a.CalculaPIS)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.AliquotaPIS)
          @Html.TextBoxFor(a => a.AliquotaPIS, new { @class = "form-control money" })
          @Html.ValidationMessageFor(a => a.AliquotaPIS)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.CheckBoxFor(a => a.CalculaConfins)
          @Html.LabelFor(a => a.CalculaConfins)
          @Html.ValidationMessageFor(a => a.CalculaConfins)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.AliquotaConfins)
          @Html.TextBoxFor(a => a.AliquotaConfins, new { @class = "form-control money" })
          @Html.ValidationMessageFor(a => a.AliquotaConfins)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.CheckBoxFor(a => a.CalculaCSLL)
          @Html.LabelFor(a => a.CalculaCSLL)
          @Html.ValidationMessageFor(a => a.CalculaCSLL)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.AliquotaCSLL)
          @Html.TextBoxFor(a => a.AliquotaCSLL, new { @class = "form-control money" })
          @Html.ValidationMessageFor(a => a.AliquotaCSLL)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.CheckBoxFor(a => a.CalculaPI)
          @Html.LabelFor(a => a.CalculaPI)
          @Html.ValidationMessageFor(a => a.CalculaPI)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.AliquotaPI)
          @Html.TextBoxFor(a => a.AliquotaPI, new { @class = "form-control money" })
          @Html.ValidationMessageFor(a => a.AliquotaPI)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.ItemListaServico)
          @Html.TextBoxFor(a => a.ItemListaServico, new { @class = "form-control" })
          @Html.ValidationMessageFor(a => a.ItemListaServico)
        </div>
      </div>

    </div>
    <div class="card-footer">
      <button type="button" class="btn btn-secondary" onclick="location.href='@Url.Action("Index")'">Voltar</button>
      <button type="submit" class="btn btn-success">Salvar</button>
    </div>
  </form>

</div>


