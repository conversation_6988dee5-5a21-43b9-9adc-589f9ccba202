﻿@using UniContabil.Infrastructure.Controls
@model UniContabilEntidades.Models.ItensNotaFiscalFatVendaModel
<!-- Modal -->
  <div class="container">

    @using (Html.BeginForm("Create", "ItensNotaFiscalFatVenda", FormMethod.Post, new { id = "PostItensNotaFiscalFat" }))
    {

    <div class="modal-body">
      @Html.HiddenFor(a => a.Codigo)
      


    </div>
      <div class="box-footer">
        <button type="button" value="Voltar" class="btn btn-outline-dark" onclick="location.href='@Url.Action("Index","NotaFiscalFat")'">Cancelar</button>
        <button type="submit" value="Create" style="float: right;" class="btn btn-outline-primary pull-right">Salvar &#x2713;</button>
      </div>
    }


  </div>

