﻿@model List<ExportacaoLancamentosIndex>

@{
  ViewData["Title"] = "";
}

<script src="~/js/Controls-1.0.1.js"></script>
<script src="~/Views/ExportacaoLancamentos/ExportacaoLancamentos.js?wew=ew"></script>
<br />

<div class="table-group card">
  <div class="card-body">
    <div class="col-6 card-custom">
      <div style="width: 100%;display: flex;">
        <input class="form-control Data col-3" style="margin-right: 5px;" data-val="true" placeholder="Data Inícial" id="dtIni" name="dtIni" type="text" value="">
        <input class="form-control Data col-3" style="margin-right: 5px;" data-val="true" placeholder="Data Final" id="dtFim" name="dtFim" type="text" value="">
        <div class="form-group" style="margin-top: -1.8rem;margin-right: 5px;">
          <label for="TipoFrete">Exportado</label>
          <select class="form-control" id="Exportado" name="Exportado">
            <option selected="selected" value="">Selecione</option>
            <option value="1">Sim</option>
            <option value="2">Não</option>
          </select>
          <span class="field-validation-valid" data-valmsg-for="ValorFrete" data-valmsg-replace="true"></span>
        </div>
        <button class="btn btn-outline-primary btn-sm" onclick="ExportacaoLancamentos.Search()" id="BtnSearch" style="height: 28px;margin-left: 2px;"> Buscar </button>
        <button class="btn btn-outline-primary btn-sm" onclick="ExportacaoLancamentos.Exportar()" id="BtnSearch" style="height: 28px;margin-left: 2px;"> Exportar </button>
      </div>
    </div>
  </div>
  <div class="col-md-12" id="GridFiltro">
    @await Html.PartialAsync("_GridFiltro", new List<ExportacaoLancamentosIndex>())
  </div>
  <div class="justify-content-center spinner-table" style="display: none !important; margin-top: 10px;">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Carregando...</span>
    </div>
  </div>
</div>

