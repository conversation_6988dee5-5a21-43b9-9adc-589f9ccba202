/*!
* inputmask.phone.extensions.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2017 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 3.3.11
*/

!function(e){"function"==typeof define&&define.amd?define(["./dependencyLibs/inputmask.dependencyLib","./inputmask"],e):"object"==typeof exports?module.exports=e(require("./dependencyLibs/inputmask.dependencyLib"),require("./inputmask")):e(window.dependencyLib||jQuery,window.Inputmask)}(function(e,r){function n(e,r){var n=(e.mask||e).replace(/#/g,"9").replace(/\)/,"9").replace(/[+()#-]/g,""),t=(r.mask||r).replace(/#/g,"9").replace(/\)/,"9").replace(/[+()#-]/g,""),a=(e.mask||e).split("#")[0],o=(r.mask||r).split("#")[0];return 0===o.indexOf(a)?-1:0===a.indexOf(o)?1:n.localeCompare(t)}var t=r.prototype.analyseMask;return r.prototype.analyseMask=function(r,n,a){function o(e,n,t){n=n||"",t=t||s,""!==n&&(t[n]={});for(var a="",p=t[n]||t,i=e.length-1;i>=0;i--)p[a=(r=e[i].mask||e[i]).substr(0,1)]=p[a]||[],p[a].unshift(r.substr(1)),e.splice(i,1);for(var u in p)p[u].length>500&&o(p[u].slice(),u,p)}function p(r){var n="",t=[];for(var o in r)e.isArray(r[o])?1===r[o].length?t.push(o+r[o]):t.push(o+a.groupmarker.start+r[o].join(a.groupmarker.end+a.alternatormarker+a.groupmarker.start)+a.groupmarker.end):t.push(o+p(r[o]));return 1===t.length?n+=t[0]:n+=a.groupmarker.start+t.join(a.groupmarker.end+a.alternatormarker+a.groupmarker.start)+a.groupmarker.end,n}var s={};return a.phoneCodes&&(a.phoneCodes&&a.phoneCodes.length>1e3&&(o((r=r.substr(1,r.length-2)).split(a.groupmarker.end+a.alternatormarker+a.groupmarker.start)),r=p(s)),r=r.replace(/9/g,"\\9")),t.call(this,r,n,a)},r.extendAliases({abstractphone:{groupmarker:{start:"<",end:">"},countrycode:"",phoneCodes:[],mask:function(e){return e.definitions={"#":r.prototype.definitions[9]},e.phoneCodes.sort(n)},keepStatic:!0,onBeforeMask:function(e,r){var n=e.replace(/^0{1,2}/,"").replace(/[\s]/g,"");return(n.indexOf(r.countrycode)>1||-1===n.indexOf(r.countrycode))&&(n="+"+r.countrycode+n),n},onUnMask:function(e,r,n){return e.replace(/[()#-]/g,"")},inputmode:"tel"}}),r});