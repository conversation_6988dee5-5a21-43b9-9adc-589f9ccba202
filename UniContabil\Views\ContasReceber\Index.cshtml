﻿@model UniContabilEntidades.Models.ContasReceberModel
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;


@{
  ViewData["Title"] = "Index";
}
<h1>@Html.DisplayNameFor(model => model)</h1>
@if (ContextoUsuario.HasPermission("ContasReceber", TipoFuncao.Create))
{
  <p>
    <a asp-action="Create">+ Novo</a>
  </p>
}
<table class="table">
  <thead>
    <tr>
      <th>
        @Html.DisplayNameFor(model => model.CodigoIdent)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.NomeCliente)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.Serie)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DataEmissao)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.ValorBruto)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DataVencido)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.Parcelas)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.NomeHistorico)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.NomeTitulo)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.NomeCentroCusto)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.NomeContaContabel)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.NroBaixa)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DataBaixa)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.ValorTotalPago)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.Juros)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.Descontos)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.JurosDia)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DescontoDia)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.MultaMes)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.JurosDiaPerc)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.DescontoDiaPerc)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.MultaMesPerc)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.ValorLiquido)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.IR)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.PIS)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.CSSL)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.ISS)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.COFINS)
      </th>
      <th>
        @Html.DisplayNameFor(model => model.OutrasRetencoes)
      </th>
      <th></th>
    </tr>
  </thead>
  <tbody>
    @foreach (UniContabilEntidades.Models.ContasReceberModel item in ViewBag.PageItems)
    {
      <tr>
        <td>
          @Html.DisplayFor(modelItem => item.CodigoIdent)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.NomeCliente)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.Serie)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DataEmissao)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.ValorBruto)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DataVencido)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.Parcelas)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.NomeHistorico)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.NomeTitulo)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.NomeCentroCusto)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.NomeContaContabel)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.NroBaixa)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DataBaixa)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.ValorTotalPago)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.Juros)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.Descontos)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.JurosDia)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DescontoDia)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.MultaMes)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.JurosDiaPerc)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.DescontoDiaPerc)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.MultaMesPerc)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.ValorLiquido)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.IR)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.PIS)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.CSSL)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.ISS)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.COFINS)
        </td>
        <td>
          @Html.DisplayFor(modelItem => item.OutrasRetencoes)
        </td>
        <td>
          @if (ContextoUsuario.HasPermission("ContasReceber", TipoFuncao.Edit))
          {
            <a class="btn btn-secondary" asp-action="Edit" asp-route-id="@item.Id">Editar</a>
          }
          @if (ContextoUsuario.HasPermission("ContasReceber", TipoFuncao.Delete))
          {
            <button class="btn btn-danger" onclick="DeleteItem('@this.ViewContext.RouteData.Values["controller"].ToString()', @item.Id)">Apagar</button>
          }
        </td>
      </tr>
    }
  </tbody>
</table>
@Html.PagedListPager((IPagedList)ViewBag.PageItems, page => Url.Action("Index", new { page }),
  new PagedListRenderOptions
  {
    LiElementClasses = new string[] { "page-item" },
    PageClasses = new string[] { "page-link" },
    Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
    MaximumPageNumbersToDisplay = 5
  })

