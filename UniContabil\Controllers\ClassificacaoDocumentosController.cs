﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;

namespace UniContabil.Controllers
{
  public class ClassificacaoDocumentosController : LibController
  {
    public ClassificacaoDocumentosController()
    { }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        IPagedList<ClassificacaoDocumentosModel> List = new ClassificacaoDocumentosServices().GetPagedList(page);
        ViewBag.PageItems = List;
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpPost]
    public JsonResult EditVinculo(VincularCreditoDebitoModel model)
    {
      try
      {
        new ClassificacaoDocumentosServices(ContextoUsuario.UserLogged).VinculaPlanoContaClassificacao(model);
        return Json(new { error = false, message = "Foi vinculado com sucesso" });
      }
      catch (Exception ex)
      {
        return Json(new { error = true, message = "Ocorreu um erro, tente novamente mais tarde" });
      }
    }

    public PartialViewResult _GetEditPlanoConta(int Id, int idtipoPessoa)
    {
      E_ClassificacaoPlanoConta entity = new ContabilidadeService(ContextoUsuario.UserLogged).GetClassificacaoPlanoContaByIdClassificacaoDocumento(Id);
      if (entity != null)
      {
        VincularCreditoDebitoModel model = new VincularCreditoDebitoModel()
        {
          IdClassificacao = entity.CPC_IdClassificacao.Value,
          IdClassificacaoContaPlano = entity.CPC_Id,
          IdPlanoCredito = entity.CPC_IdPlanoContaCredito ?? 0,
          IdPlanoDebito = entity.CPC_IdPlanoContaDebito ?? 0,
          IdTipoPessoa = idtipoPessoa
        };
        return PartialView("_PartialVinculaPlanoContas", model);
      }
      else
      {
        return PartialView("_PartialVinculaPlanoContas", new VincularCreditoDebitoModel()
        {
          IdClassificacao = Id,
          IdTipoPessoa = idtipoPessoa
        });
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public PartialViewResult Edit(int id)
    {
      try
      {
        ClassificacaoDocumentosModel classificacaoDocumentosModel = new ClassificacaoDocumentosModel().FromDatabase(new ClassificacaoDocumentosServices().GetById(id));

        ClassificacaoDocumentosServices classificacaoDocumentosServices = new ClassificacaoDocumentosServices(ContextoUsuario.UserLogged);

        bool? isContab = classificacaoDocumentosServices.IsUserContabilidade();
        ViewBag.IsContab = isContab;

        bool? IsContabAdmin = classificacaoDocumentosServices.IsUserContabilidadeAdmin();
        ViewBag.IsContabAdmin = IsContabAdmin;

        if (classificacaoDocumentosModel == null)
          throw new Exception("Classificação não encontrada");

        return PartialView("Edit", classificacaoDocumentosModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        throw new Exception(Ex.Message);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(ClassificacaoDocumentosModel documentosModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new ClassificacaoDocumentosServices(ContextoUsuario.UserLogged).Edit(documentosModel);
          MessageAdd(new Message(MessageType.Success, LibAlertMessage.MessageEditSucess));
          return Json(new { erro = false });
        }
        return Json(new { erro = false });
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return Json(new { erro = true, message = Ex.Message });
      }
    }
    
    public ActionResult TrocaClassificacaoCat(int IdCategoria, int IdClassificacao)
    {
      try
      {
        new TreeviewService(ContextoUsuario.UserLogged).TrocaClassificacaoCat(IdClassificacao, IdCategoria);
        return Json(new { erro = false, message = "A categoria foi alterada com sucesso!" });
      }
      catch (Exception ex)
      {
        return Json(new { erro = true, message = "Ocorreu um erro, tente novmanete mais tarde!" });
      }
    }

    public JsonResult _GetTreeview(int Tipo)
    {
      List<TreeviewClassificacaoModel> Treeview = new List<TreeviewClassificacaoModel>();

      Treeview = new TreeviewService(ContextoUsuario.UserLogged).GetTreeviewClassificacaoAll(Tipo);
      JsonResult json = new JsonResult(Treeview);

      return json;
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public PartialViewResult Create()
    {
      try
      {
        return PartialView("Create");
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        throw new Exception(Ex.Message);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(ClassificacaoDocumentosModel classificacao)
    {
      try
      {
        int id = new ClassificacaoDocumentosServices().Create(classificacao);
        return Json(new { erro = false, cod = id });
      }
      catch (Exception Ex)
      {
        return Json(new { erro = true, message = Ex.Message });
      }
    }
    
    [HttpGet]
    public JsonResult VerificaVinculoClassificacao(int id)
    {
      try
      {
        bool retorno = new ClassificacaoDocumentosServices().VerificarVinculoClassificacao(id);
        return Json(new { erro = false, vinculo = retorno });
      }
      catch (Exception Ex)
      {
        return Json(new { erro = true, mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }

    [HttpGet]
    public JsonResult Delete(int id)
    {
      try
      {
        new ClassificacaoDocumentosServices().Delete(id);
        return Json(new { sucesso = true });
      }
      catch (Exception Ex)
      {
        return Json(new { sucesso = false, mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }

  }
}