/*!
* dependencyLibs/inputmask.dependencyLib.jqlite.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2017 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 3.3.7
*/

!function(a){"function"==typeof define&&define.amd?define(["jqlite","../global/window","../global/document]"],a):"object"==typeof exports?module.exports=a(require("jqlite"),require("../global/window"),require("../global/document")):window.dependencyLib=a(jqlite,window,document)}(function(a,b,c){function d(a,b){for(var c=0,d=a.length;c<d;c++)if(a[c]===b)return c;return-1}function e(a){return null==a?a+"":"object"==typeof a||"function"==typeof a?h[h.toString.call(a)]||"object":typeof a}function f(a){return null!=a&&a===a.window}function g(a){var b="length"in a&&a.length,c=e(a);return"function"!==c&&!f(a)&&(!(1!==a.nodeType||!b)||("array"===c||0===b||"number"==typeof b&&b>0&&b-1 in a))}for(var h={},i="Boolean Number String Function Array Date RegExp Object Error".split(" "),j=0;j<i.length;j++)h["[object "+i[j]+"]"]=i[j].toLowerCase();return a.inArray=function(a,b,c){return null==b?-1:d(b,a)},a.isFunction=function(a){return"function"===e(a)},a.isArray=Array.isArray,a.isPlainObject=function(a){return"object"===e(a)&&!a.nodeType&&!f(a)&&!(a.constructor&&!h.hasOwnProperty.call(a.constructor.prototype,"isPrototypeOf"))},a.extend=function(){var b,c,d,e,f,g,h=arguments[0]||{},i=1,j=arguments.length,k=!1;for("boolean"==typeof h&&(k=h,h=arguments[i]||{},i++),"object"==typeof h||a.isFunction(h)||(h={}),i===j&&(h=this,i--);i<j;i++)if(null!=(b=arguments[i]))for(c in b)d=h[c],e=b[c],h!==e&&(k&&e&&(a.isPlainObject(e)||(f=a.isArray(e)))?(f?(f=!1,g=d&&a.isArray(d)?d:[]):g=d&&a.isPlainObject(d)?d:{},h[c]=a.extend(k,g,e)):void 0!==e&&(h[c]=e));return h},a.each=function(a,b){var c=0;if(g(a))for(var d=a.length;c<d&&!1!==b.call(a[c],c,a[c]);c++);else for(c in a)if(!1===b.call(a[c],c,a[c]))break;return a},a.map=function(a,b){var c,d=0,e=a.length,f=g(a),h=[];if(f)for(;d<e;d++)null!=(c=b(a[d],d))&&h.push(c);else for(d in a)null!=(c=b(a[d],d))&&h.push(c);return[].concat(h)},a.data=function(b,c,d){return a(b).data(c,d)},a.Event=a.Event||function(a,b){b=b||{bubbles:!1,cancelable:!1,detail:void 0};var d=c.createEvent("CustomEvent");return d.initCustomEvent(a,b.bubbles,b.cancelable,b.detail),d},a.Event.prototype=b.Event.prototype,a});