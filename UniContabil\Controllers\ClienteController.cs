﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;
using UniContabilEntidades.Models;


namespace UniContabil.Controllers
{
  public class ClienteController : LibController
  {
    public ClienteController()
    { }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        IPagedList<ClienteModel> List = new ClienteServices(ContextoUsuario.UserLogged).GetPagedList(page);
        ViewBag.PageItems = List;
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(ClienteModel clienteModel)
    {
      try
      {
        if (ModelState.IsValid)
        {

          if (ContextoUsuario.UserLogged.IdOrganizacao == 0 && !ContextoUsuario.UserLogged.IdFilial.HasValue)
          {
            MessageAdd(new Message(MessageType.Warning, "É necessário selecionar a clínica."));
            return View(clienteModel);
          }

          new ClienteServices(ContextoUsuario.UserLogged).Create(clienteModel);
          return RedirectToAction(nameof(Index));
        }
        else
        {
          return View(clienteModel);
        }
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(clienteModel);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(int id)
    {
      try
      {
        ClienteModel clienteModel = new ClienteModel().FromDatabase(new ClienteServices().GetByGuid(id));

        if (clienteModel == null)
        {
          return NotFound();
        }

        return View(clienteModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(ClienteModel clienteModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new ClienteServices(ContextoUsuario.UserLogged).Edit(clienteModel);
          return RedirectToAction(nameof(Index));
        }
        return View(clienteModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(clienteModel);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string Delete(int id)
    {
      try
      {
        ClienteServices Service = new ClienteServices();
        var model = Service.GetByGuid(id);
        Service.Delete(model);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }

    [HttpGet]
    public ActionResult GetOrganizacaoSelect(string term)
    {
      OrganizacaoServices Service = new OrganizacaoServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
    [HttpGet]
    public ActionResult GetFilialSelect(string term)
    {
      FilialServices Service = new FilialServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
    [HttpGet]
    public ActionResult GetTipoClienteSelect(string term)
    {
      TipoClienteServices Service = new TipoClienteServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }

    [HttpGet]
    //[NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.GridDetalhesCliente)]
    public PartialViewResult _GridClientes(string Campo1)
    {
      ClienteServices clienteServices = new ClienteServices();
      List<GridCliente> ListaDetalhesCliente = clienteServices.GetListGridCliente(Campo1);
      return PartialView("InfoAddCliente/_GridClientes", ListaDetalhesCliente);
    }

    [HttpGet]
    //[NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.GridDetalhesCliente)]
    public PartialViewResult _GridDetalhesCliente(int IdCliente)
    {
      ClienteServices clienteServices = new ClienteServices();
      GridDetalhesCliente GridDetalhesCliente = clienteServices.GetGridDetalhesCliente(IdCliente);
      return PartialView("InfoAddCliente/_GridDetalhesCliente", GridDetalhesCliente);
    }
  }
}
