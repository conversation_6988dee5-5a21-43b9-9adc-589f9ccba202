﻿@model UniContabilEntidades.Models.CategoriaDocumentoModel
@using UniContabil.Infrastructure.Controls

<div class="card">
  <form asp-action="Edit" id="EditCategoria">
    <div class="card-header">
      <h5>Alterar Categoria</h5>
    </div>
    <div class="card-body">
      @Html.AntiForgeryToken()
      <div asp-validation-summary="ModelOnly" class="text-danger"></div>
      <input type="hidden" asp-for="Id" />
      <div class="form-group">
        <div class="form-group">
          <label asp-for="Codigo" class="control-label"></label>
          <input asp-for="Codigo" class="form-control" />
          <span asp-validation-for="Codigo" class="text-danger"></span>
        </div>
        <div class="form-group">
          <label asp-for="Descricao" class="control-label"></label>
          <input asp-for="Descricao" class="form-control" />
          <span asp-validation-for="Descricao" class="text-danger"></span>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <a asp-action="Index" class="link link-info">Voltar</a>
      <a class="btn btn-primary" id="saveCatEdit">Salvar</a>
    </div>
  </form>
</div>
