﻿@model UniContabilEntidades.Models.ProdutoModel
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Atualização Produto";
}
<h1>@*Editar @Html.DisplayNameFor(model => model)*@</h1>
<div class="card">
  <form asp-action="Edit">
    <div class="card-body">
      @Html.AntiForgeryToken()
      <div asp-validation-summary="ModelOnly" class="text-danger"></div>
      <div class="col-md-4">
        <div class="form-group">
          <label asp-for="Nome" class="control-label"></label>
          <input asp-for="Nome" class="form-control" />
          <span asp-validation-for="Nome" class="text-danger"></span>
        </div>
      </div>
      <!--Descrição System.String-->
      <div class="col-md-4">
        <div class="form-group">
          <label asp-for="Descrição" class="control-label"></label>
          <input asp-for="Descrição" class="form-control" />
          <span asp-validation-for="Descrição" class="text-danger"></span>
        </div>
      </div>
      <!--CodigoBarras System.String-->
      <!--<div class="col-md-4">
        <div class="form-group">
          <label asp-for="CodigoBarras" class="control-label"></label>
          <input asp-for="CodigoBarras" class="form-control" />
          <span asp-validation-for="CodigoBarras" class="text-danger"></span>
        </div>
      </div>-->
      <!--UnidadeMedida System.String-->
      <!--<div class="col-md-4">
        <div class="form-group">
          <label asp-for="UnidadeMedida" class="control-label"></label>
          <input asp-for="UnidadeMedida" class="form-control" />
          <span asp-validation-for="UnidadeMedida" class="text-danger"></span>
        </div>
      </div>-->
      <!--IdGrupoProduto System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]-->
      <!--<div class="col-md-4">
        <div class="form-group">
          <label asp-for="IdGrupoProduto" class="control-label"></label>
          @Html.Select2("GrupoProdutoSelect", "GetGrupoProdutoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdGrupoProduto), this.ViewContext.RouteData.Values["controller"].ToString())
        </div>
      </div>-->
      <!--IdSubGrupoProduto System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]-->
      <!--<div class="col-md-4">
        <div class="form-group">
          <label asp-for="IdSubGrupoProduto" class="control-label"></label>
          @Html.Select2("SubGrupoProdutoSelect", "GetSubGrupoProdutoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdSubGrupoProduto), this.ViewContext.RouteData.Values["controller"].ToString())
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          <label asp-for="TipoProduto" class="control-label"></label>
          <input asp-for="TipoProduto" class="form-control" />
          <span asp-validation-for="TipoProduto" class="text-danger"></span>
        </div>
      </div>-->

      @*<div class="col-md-4">
        <div class="form-group" style=" margin-top: 7px; margin-left : 7px">
          <label class="form-check-label">
            <input class="form-check-input" asp-for="ContraLoteAux" style=" margin-top: -3px;" /> @Html.DisplayNameFor(model => model.ContraLoteAux)
          </label>
        </div>

      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.ConsumoMedio)
          @Html.TextBoxFor(a => a.ConsumoMedio, new { @class = "form-control number" })
          @Html.ValidationMessageFor(a => a.ConsumoMedio)
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.ValorCustoPadrao)
          @Html.TextBoxFor(a => a.ValorCustoPadrao, new { @class = "form-control money" })
          @Html.ValidationMessageFor(a => a.ValorCustoPadrao)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.EstoqueMinimo)
          @Html.TextBoxFor(a => a.EstoqueMinimo, new { @class = "form-control number" })
          @Html.ValidationMessageFor(a => a.EstoqueMinimo)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.DiasReposicao)
          @Html.TextBoxFor(a => a.DiasReposicao, new { @class = "form-control numerosOnly" })
          @Html.ValidationMessageFor(a => a.DiasReposicao)
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.Ncm)
          @Html.TextBoxFor(a => a.Ncm, new { @class = "form-control" })
          @Html.ValidationMessageFor(a => a.Ncm)
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.ICMS)
          @Html.TextBoxFor(a => a.ICMS, new { @class = "form-control porcentagem" })
          @Html.ValidationMessageFor(a => a.ICMS)
        </div>
      </div>*@
      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.PIS)
          @Html.TextBoxFor(a => a.PIS, new { @class = "form-control porcentagem", maxlength = 9 })
          @Html.ValidationMessageFor(a => a.PIS)
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.COFINS)
          @Html.TextBoxFor(a => a.COFINS, new { @class = "form-control porcentagem", maxlength = 9 })
          @Html.ValidationMessageFor(a => a.COFINS)
        </div>
      </div>
      @*<div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.BC)
          @Html.TextBoxFor(a => a.BC, new { @class = "form-control porcentagem" })
          @Html.ValidationMessageFor(a => a.BC)
        </div>
      </div>*@
      <div class="col-md-4">
        <div class="form-group">
          @Html.Label("CFOPSelect")
          <div style="display: flex; justify-content: space-between; align-items: flex-end;">
            <div style="width: 85%">
              @Html.Select2("CFOPSelect", "GetCFOPSelect", "Selecione o CFOP", "", "", Model.CFOPSelect == null ? "" : Model.CFOPSelect.id, Model.CFOPSelect == null ? "" : Model.CFOPSelect.text)
            </div>
            <div style="width: 10%; display: flex;">
              <i class="mw-drip-search" id="DetalhesCFOP"></i>
            </div>
          </div>
        </div>
      </div>
      <!--DataCadastro System.DateTime-->

      <div class="form-group col-md-2" style=" margin-top: 7px;">
        <label class="form-check-label">
          <input class="form-check-input" asp-for="Ativo" style=" margin-top: -3px;" /> @Html.DisplayNameFor(model => model.Ativo)
        </label>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.ItemListaServico)
          @Html.TextBoxFor(a => a.ItemListaServico, new { @class = "form-control", maxlength = 5 })
          @Html.ValidationMessageFor(a => a.ItemListaServico)
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          @Html.LabelFor(a => a.CodigoCnae)
          @Html.TextBoxFor(a => a.CodigoCnae, new { @class = "form-control", maxlength = 10 })
          @Html.ValidationMessageFor(a => a.CodigoCnae)
        </div>
      </div>
      @*<div class="form-group">
      <input type="submit" value="Adicionar" class="btn btn-primary" />
    </div>*@
    </div>
    <div class="card-footer">
      <button type="button" class="btn btn-secondary" onclick="location.href='@Url.Action("Index")'">Voltar</button>
      <button type="submit" class="btn btn-success">Salvar</button>
    </div>
  </form>

</div>

