﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <UserSecretsId>c285fe18-ad79-4e4e-8028-8dfa50967eae</UserSecretsId>
    <NoWin32Manifest>true</NoWin32Manifest>
	<AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
	<Nullable></Nullable>
	<AssemblyName>UniContabil</AssemblyName>
	<RootNamespace>UniContabil</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="Controllers\Index.cshtml" />
    <Content Remove="wwwroot\Views\ExportacaoLancamentos\ExportacaoLancamentos.js" />
    <Content Remove="wwwroot\Views\Filial\FilialIndex.js" />
    <Content Remove="wwwroot\Views\Usuarios\GridTipoGrupoFilal.js" />
  </ItemGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="efpt.config.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="bootstrap" Version="4.6.0" />
    <PackageReference Include="FastReport.Compat" Version="2020.3.6" />
    <PackageReference Include="FastReport.OpenSource" Version="2020.3.4" />
    <PackageReference Include="FastReport.OpenSource.Export.PdfSimple" Version="2020.3.4" />
    <PackageReference Include="FastReport.OpenSource.Web" Version="2020.3.4" />
    <PackageReference Include="Font.Awesome" Version="5.15.3" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.36" />
    <PackageReference Include="iTextSharp" Version="********" />
    <PackageReference Include="Magick.NET-Q16-x64" Version="8.3.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="3.1.15" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.1.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="3.8.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="3.8.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="3.1.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="3.1.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="3.1.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="3.1.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="3.1.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration" Version="3.1.4" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="3.1.4" />
    <PackageReference Include="Nancy" Version="2.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="12.0.3" />
    <PackageReference Include="QRCoder" Version="1.3.9" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="5.0.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.1" />
    <PackageReference Include="System.Drawing.Common" Version="5.0.0" />
    <PackageReference Include="System.IO" Version="4.3.0" />
    <PackageReference Include="System.Private.ServiceModel" Version="4.4.4" />
    <PackageReference Include="System.Security.Cryptography.Xml" Version="5.0.0" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="4.4.4" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.4.4" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.4.4" />
    <PackageReference Include="System.ServiceModel.Primitives" Version="4.4.4" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.4.4" />
    <PackageReference Include="X.PagedList.Mvc.Core" Version="8.0.7" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\Scripts\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="wwwroot\Views\ExportacaoLancamentos\ExportacaoLancamentos.js" />
    <None Include="wwwroot\Views\Filial\FilialIndex.js" />
    <None Include="wwwroot\Views\Filial\Filial.js" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="UniContabilDomain">
      <HintPath>..\..\ContabService\MWERPDomain\MWERPDomain\bin\Debug\netcoreapp3.1\UniContabilDomain.dll</HintPath>
    </Reference>
    <Reference Include="UniContabilEntidades">
      <HintPath>..\..\ContabEntities\MWERPEntidades\MWERPEntidades\bin\Debug\netcoreapp3.1\UniContabilEntidades.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties libman_1json__JsonSchema="" /></VisualStudio></ProjectExtensions>



</Project>
