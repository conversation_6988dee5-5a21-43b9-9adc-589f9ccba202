/*!
* inputmask.regex.extensions.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2017 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 3.3.11
*/

!function(e){"function"==typeof define&&define.amd?define(["./dependencyLibs/inputmask.dependencyLib","./inputmask"],e):"object"==typeof exports?module.exports=e(require("./dependencyLibs/inputmask.dependencyLib"),require("./inputmask")):e(window.dependencyLib||jQuery,window.Inputmask)}(function(e,r){return r.extendAliases({Regex:{mask:"r",greedy:!1,repeat:"*",regex:null,regexTokens:null,tokenizer:/\[\^?]?(?:[^\\\]]+|\\[\S\s]?)*]?|\\(?:0(?:[0-3][0-7]{0,2}|[4-7][0-7]?)?|[1-9][0-9]*|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}|c[A-Za-z]|[\S\s]?)|\((?:\?[:=!]?)?|(?:[?*+]|\{[0-9]+(?:,[0-9]*)?\})\??|[^.?*+^${[()|\\]+|./g,quantifierFilter:/[0-9]+[^,]/,isComplete:function(e,r){return new RegExp(r.regex,r.casing?"i":"").test(e.join(""))},definitions:{r:{validator:function(r,t,a,i,n){function s(e,r){this.matches=[],this.isGroup=e||!1,this.isQuantifier=r||!1,this.quantifier={min:1,max:1},this.repeaterPart=void 0}function u(r,t){var a=!1;t&&(f+="(",l++);for(var i=0;i<r.matches.length;i++){var s=r.matches[i];if(!0===s.isGroup)a=u(s,!0);else if(!0===s.isQuantifier){var h=e.inArray(s,r.matches),c=r.matches[h-1],o=f;if(isNaN(s.quantifier.max)){for(;s.repeaterPart&&s.repeaterPart!==f&&s.repeaterPart.length>f.length&&!(a=u(c,!0)););(a=a||u(c,!0))&&(s.repeaterPart=f),f=o+s.quantifier.max}else{for(var m=0,g=s.quantifier.max-1;m<g&&!(a=u(c,!0));m++);f=o+"{"+s.quantifier.min+","+s.quantifier.max+"}"}}else if(void 0!==s.matches)for(var d=0;d<s.length&&!(a=u(s[d],t));d++);else{var x;if("["==s.charAt(0)){x=f,x+=s;for(b=0;b<l;b++)x+=")";a=(w=new RegExp("^("+x+")$",n.casing?"i":"")).test(p)}else for(var k=0,v=s.length;k<v;k++)if("\\"!==s.charAt(k)){x=f,x=(x+=s.substr(0,k+1)).replace(/\|$/,"");for(var b=0;b<l;b++)x+=")";var w=new RegExp("^("+x+")$",n.casing?"i":"");if(a=w.test(p))break}f+=s}if(a)break}return t&&(f+=")",l--),a}var p,h,c=t.buffer.slice(),f="",o=!1,l=0;null===n.regexTokens&&function(){var e,r,t=new s,a=[];for(n.regexTokens=[];e=n.tokenizer.exec(n.regex);)switch((r=e[0]).charAt(0)){case"(":a.push(new s(!0));break;case")":h=a.pop(),a.length>0?a[a.length-1].matches.push(h):t.matches.push(h);break;case"{":case"+":case"*":var i=new s(!1,!0),u=(r=r.replace(/[{}]/g,"")).split(","),p=isNaN(u[0])?u[0]:parseInt(u[0]),c=1===u.length?p:isNaN(u[1])?u[1]:parseInt(u[1]);if(i.quantifier={min:p,max:c},a.length>0){var f=a[a.length-1].matches;(e=f.pop()).isGroup||((h=new s(!0)).matches.push(e),e=h),f.push(e),f.push(i)}else(e=t.matches.pop()).isGroup||((h=new s(!0)).matches.push(e),e=h),t.matches.push(e),t.matches.push(i);break;default:a.length>0?a[a.length-1].matches.push(r):t.matches.push(r)}t.matches.length>0&&n.regexTokens.push(t)}(),c.splice(a,0,r),p=c.join("");for(var m=0;m<n.regexTokens.length;m++){var g=n.regexTokens[m];if(o=u(g,g.isGroup))break}return o},cardinality:1}}}}),r});