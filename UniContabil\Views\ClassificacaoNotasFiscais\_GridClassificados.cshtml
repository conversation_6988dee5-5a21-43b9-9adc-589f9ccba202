﻿@using UniContabilEntidades.Models
@model List<LancamentosModel>

<table class="table table-hover table-sm table-striped">
  <thead style=" background-color: #456b8e; color: white !important; text-align: left;">
    <tr>
      <th style=" border-radius: 15px 0px 0px 5px;">
        Data / Hora
      </th>
      <th style="border: black 1px 0px solid;">
        Nome do Documento
      </th>
      <th style="border: black 1px 0px solid;">
        Valor
      </th>
      <th style=" border-radius: 0px 15px 5px 0px;">
      </th>
    </tr>
  </thead>
  <tbody style="color: black;">
    @if (Model != null)
    {
      for (int i = 0; i < Model.Count; i++)
      {
    <tr data-cod="@Model[i].Id">
      <td class="linha_@i" data-cod="@Model[i].Id">
        @if (Model[i].DataLancamento.HasValue)
        {
          <text> @Model[i].DataLancamento.Value.ToString("dd/MM/yyyy") </text>
        }
      </td>
      <td data-cod="@Model[i].Id">
        @Model[i].Descricao
      </td>
      <td data-cod="@Model[i].Id">
        @if(Model[i].Valor.HasValue)
        {
          <text> R$ @Model[i].Valor </text>
        }
      </td>
      <td>
        <a class="btn delete" data-cod="@Model[i].Id" title="Apagar" style="padding:0 !important;font-size:13px !important; color: red !important"><i class="mw-drip-trash"></i></a>
        @*<img src="~/Content/img/Lixeira.png" width="10" height="10" class="link link-danger delete" " style="font-size: small;"/>*@
      </td>
    </tr>
      }
    }
  </tbody>
</table>
