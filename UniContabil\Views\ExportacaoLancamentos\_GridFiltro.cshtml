﻿@using UniContabil.Infrastructure;
@model List<ExportacaoLancamentosIndex>

@{
  ViewData["Title"] = "";
}
@if (Model != null && Model.Count() > 0)
{

  <div class="table-group-content ">
    <div class="table-responsive">
      <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
        <thead style="text-align:center">
          <tr>
            <th>
              Razão Social
            </th>
            <th>
              CPF/CNPJ
            </th>
            <th>
              Tel. Fixo
            </th>
            <th>
              E-mail
            </th>
            <th>
              Tel. Celular
            </th>
            <th>
              Ativo?
            </th>
          </tr>
        </thead>
        <tbody>
          @for (int i = 0; i < Model.Count; i++)
          {
            <tr>
              <td>
                @Html.CheckBoxFor(a => a[i].Check, new { @idfil = string.Format("{0}", Model[i].IdFilial), @style = "margin-left: 5pt;margin - top: 5pt; height: 13px !important;", @class = "selectsEx", @id = string.Format("selectfilial_{0}", i) })
                @Html.DisplayFor(m => m[i].Nome)
              </td>
              <td>
                @Html.DisplayFor(m => m[i].CPFCNPJ)
              </td>
              <td>
                @Html.DisplayFor(m => m[i].TelefoneFixo)
              </td>
              <td>
                @Html.DisplayFor(m => m[i].Email)
              </td>
              <td>
                @Html.DisplayFor(m => m[i].TelCelular)
              </td>
              <td style="text-align:center">
                @if (Model[i].Ativo)
                {
                  <text> Sim </text>
                }
                else
                {
                  <text> Não </text>
                }
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
}

