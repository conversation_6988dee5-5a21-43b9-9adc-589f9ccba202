﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;
using UniContabilEntidades.Models.Dashboard;

namespace UniContabil.Controllers
{
  public class DashboardController : LibController
  {
    public DashboardController()
    { }

    [HttpGet]
    public ActionResult Index()
    {
      try
      {
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    public PartialViewResult GetGridDRE()
    {
      try
      {
        List<DashboardGridDRE> listdre = new List<DashboardGridDRE>();
        listdre = new DashboardServices(ContextoUsuario.UserLogged).GetDSBDRE();

        return PartialView("_GridDRE", listdre);
      }
      catch (Exception)
      {
        throw;
      }
    }

    public JsonResult AtualizaDRE()
    {
      try
      {
        new DashboardServices(ContextoUsuario.UserLogged).AtualizaDRE();
        return Json(new { erro = false, message = "Atualizado com Sucesso!" });
      }
      catch (Exception ex)
      {
        return Json(new { erro = true, message = ex.Message });
      }
    }

    public JsonResult GetLancamentos(int ano)
    {
      try
      {
        LancamentosDashboardModel lancamentos =  new DashboardServices(ContextoUsuario.UserLogged).GetLancamentos(ano);
        return Json(new { erro = false, message = "Atualizado com Sucesso!", lancamentos = lancamentos });
      }
      catch (Exception ex)
      {
        return Json(new { erro = true, message = ex.Message });
      }
    }

  }
}
