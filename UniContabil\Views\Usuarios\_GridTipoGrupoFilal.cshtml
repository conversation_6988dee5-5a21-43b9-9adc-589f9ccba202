﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model List<OrganizacaoUsuarioPartial>

@{
  ViewData["Title"] = "Usuario";
}
<style>
  .selectstiposgrupos {
    margin-left: 5pt;
    margin-top: 5pt;
    height: 13px !important;
  }
</style>
@if (Model.Count > 0)
{
  <table id="option-grupos-table">
    <thead>
      <tr>
        <th>
          Grupos
        </th>
      </tr>
    </thead>
    <tbody>
      @{ int i = 0; }
      @foreach (OrganizacaoUsuarioPartial OrganizacaoUsuarioPartial in Model)
      {
      <tr data-linha="@i" data-iditem="@OrganizacaoUsuarioPartial.Id">
        @Html.HiddenFor(a => a[i].Id)
        <td>
          <input @if (OrganizacaoUsuarioPartial.Create) {<text> checked </text> } type="checkbox" data-codigo="@OrganizacaoUsuarioPartial.Id" data-createall="@OrganizacaoUsuarioPartial.CreateAll" data-codfilial="@OrganizacaoUsuarioPartial.IdFilial" data-codorg="@OrganizacaoUsuarioPartial.IdOrganizacao" data-idtipogrupo="@OrganizacaoUsuarioPartial.TipoGrupoFilalPartial.Id" data-linha="@i" id="@String.Format("selecttipogrupo_{0}", i)" class="selectstiposgrupos" />
          @OrganizacaoUsuarioPartial.TipoGrupoFilalPartial.Descricao
        </td>
      </tr>
        i++;
      }
    </tbody>
  </table>
}


<script src="~/Views/Usuarios/GridTipoGrupoFilal.js?ds=bh"></script>
