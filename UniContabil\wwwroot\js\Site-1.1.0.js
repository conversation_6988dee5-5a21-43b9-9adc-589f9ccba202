﻿$.fn.modal = function (type) {
  if (type == "show") {
    $(this).css("display", "block");
    $(".modal-background", this).css("opacity", "1");
    $(".modal", this).animate({ opacity: 1, top: "50%" }, 500);
  }
  else if (type == "hide") {
    $(".modal", this).css("opacity", "0");
    $(".modal", this).css("top", "40%");
    $(".modal-background", this).css("opacity", "0");
    $(this).css("display", "none");
  }
};

var MWERP = function () {
};

MWERP.init = function () {
  MWERP.IniciaMenu();
  MWERP.IniciaMascaras();
  MWERP.IniciaInputs();
  MWERP.ConvertDate();
  MWERP.VerificaNumero();

  $(".modal .select-all > input").change(function () {
    if (this.checked) {
      $(".reply input").prop("checked", true);
    }
    else {
      $(".reply input").prop("checked", false);
    }
  });

  $(".modal-close, .modal-background").on("click", function () {
    $(".modal").css("opacity", "0");
    $(".modal").css("top", "40%");
    $(".modal-background").css("opacity", "0");
    $(".modal-group").css("display", "none");
  });

  $(document).on("click", "#BtnGeraNota", function () {
    var CodigoPedido = $('#CodigoPedido').val();
    var TipoPedido = $('#TipoPedido').val();
    var SerieId = $('#SeriesNotasSelect_id').val();
    var SerieText = $('#SeriesNotasSelect_text').val();

    if (SerieId == "" || SerieId == "0")
      SweetAlert('Atenção', 'Gentileza selecionar uma serie.', 'warning', 'Fechar');
    else {

      var model = {
        CodigoPedido: CodigoPedido,
        TipoPedido: TipoPedido,
        Serie: SerieText
      };

      if (TipoPedido == 2) {

        $.ajax({
          type: 'POST',
          url: GetURLBaseComplete() + '/NotaFiscalFat/GeraNotaFiscal',
          dataType: 'json',
          data: model,
          success: function (data) {
            if (!data.erro) {
              $('#_ModalSerie').modal('hide')
              Alerta(data.titulo, data.mensagem, "green", 5000);
            }
            else {
              Alerta(data.titulo, data.mensagem, "red", 5000);
            }
          }
        });

      } else {

        $.ajax({
          type: 'POST',
          url: GetURLBaseComplete() + '/NotaFiscalCompras/GeraNotaFiscal',
          dataType: 'json',
          data: model,
          success: function (data) {
            if (!data.erro) {
              $('#_ModalSerie').modal('hide')
              Alerta(data.titulo, data.mensagem, "green", 5000);
            }
            else {
              Alerta(data.titulo, data.mensagem, "red", 5000);
            }
          }
        });

      }
    }
  });
}

MWERP.InfoAddFornecedor = function () {
  $(document).on("click", "#DetalhesFornecedor", function () {
    $('#ModalPesquisaFornecedor input').val("");
    $('#ResultFornecedor').html("");
    $('#ModalPesquisaFornecedor').modal("show");
  });

  $(document).on("click", "#PesquisarModalFornecedor", function () {
    var Campo1 = $('#Campo1Fornecedor').val();
    MWERP.GridFornecedor(Campo1);
  });

  $(document).on("click", ".MaisInformacoesFornecedor", function () {
    var idProduto = $(this).data("codigofornecedor");
    $('#ShowDetalhesFornecedor').html("");
    MWERP.GridDetalhesFornecedor(idProduto);
  });

  MWERP.GridDetalhesFornecedor = function (IdFornecedor) {
    var url = GetURLBaseComplete() + '/Fornecedor/_GridDetalhesFornecedor';
    var model = {
      idFornecedor: IdFornecedor
    }

    $.ajax({
      url: url,
      cache: false,
      dataType: "html",
      type: "GET",
      data: model,
      success: function (resposta) {
        $('#ShowDetalhesFornecedor').html("");
        $('#ShowDetalhesFornecedor').html(resposta);
        $('#ModalDetalhesFornecedor input').attr('disabled', 'disabled');
        $('#ModalDetalhesFornecedor').modal("show");
      }
    });
  }

  MWERP.GridFornecedor = function (Campo1) {
    var url = GetURLBaseComplete() + '/Fornecedor/_GridFornecedor';
    var model = {
      Campo1: Campo1
    }

    $.ajax({
      url: url,
      cache: false,
      dataType: "html",
      type: "GET",
      data: model,
      success: function (resposta) {
        $('#ResultFornecedor').html("");
        $('#ResultFornecedor').html(resposta);
      }
    });
  }
}

MWERP.InfoAddProduto = function () {
  $(document).on("click", "#DetalhesProduto", function () {
    $('#ModalPesquisaProduto input').val("");
    $('#ResultProduto').html("");
    $('#ModalPesquisaProduto').modal("show");
  });

  $(document).on("click", "#PesquisarModalProduto", function () {
    var Campo1 = $('#Campo1Produto').val();
    MWERP.GridProduto(Campo1);
  });

  $(document).on("click", ".MaisInformacoesProduto", function () {
    var idProduto = $(this).data("codigoproduto");
    $('#ShowDetalhesProduto').html("");
    MWERP.GridDetalhesProduto(idProduto);
  });

  MWERP.GridDetalhesProduto = function (idProduto) {
    var url = GetURLBaseComplete() + '/Produto/_GridDetalhesProduto';
    var model = {
      idProduto: idProduto
    }

    $.ajax({
      url: url,
      cache: false,
      dataType: "html",
      type: "GET",
      data: model,
      success: function (resposta) {
        $('#ShowDetalhesProduto').html("");
        $('#ShowDetalhesProduto').html(resposta);
        $('#ModalDetalhesProduto input').attr('disabled', 'disabled');
        $('#ModalDetalhesProduto textarea').attr('disabled', 'disabled');
        $('#ModalDetalhesProduto').modal("show");
      }
    });
  }

  MWERP.GridProduto = function (Campo1) {
    var url = GetURLBaseComplete() + '/Produto/_GridProduto';
    var model = {
      Campo1: Campo1
    }

    $.ajax({
      url: url,
      cache: false,
      dataType: "html",
      type: "GET",
      data: model,
      success: function (resposta) {
        $('#ResultProduto').html("");
        $('#ResultProduto').html(resposta);
      }
    });
  }
}

MWERP.InfoAddCliente = function () {
  $(document).on("click", "#DetalhesCliente", function () {
    $('#ModalPesquisaCliente input').val("");
    $('#ResultCliente').html("");
    $('#ModalPesquisaCliente').modal("show");
  });

  $(document).on("click", "#PesquisarModalCliente", function () {
    var Campo1 = $('#Campo1Cliente').val();
    MWERP.GridClientes(Campo1);
  });

  $(document).on("click", ".MaisInformacoesClientes", function () {
    var idCliente = $(this).data("codigocliente");
    $('#ShowDetalhesCliente').html("");
    MWERP.GridDetalhesCliente(idCliente);
  });

  MWERP.GridDetalhesCliente = function (idCliente) {
    var url = GetURLBaseComplete() + '/Cliente/_GridDetalhesCliente';
    var model = {
      IdCliente: idCliente
    }

    $.ajax({
      url: url,
      cache: false,
      dataType: "html",
      type: "GET",
      data: model,
      success: function (resposta) {
        $('#ShowDetalhesCliente').html("");
        $('#ShowDetalhesCliente').html(resposta);
        $('#ModalDetalhesCliente input').attr('disabled', 'disabled');
        $('#ModalDetalhesCliente').modal("show");
      }
    });
  }

  MWERP.GridClientes = function (Campo1) {
    var url = GetURLBaseComplete() + '/Cliente/_GridClientes';
    var model = {
      Campo1: Campo1
    }

    $.ajax({
      url: url,
      cache: false,
      dataType: "html",
      type: "GET",
      data: model,
      success: function (resposta) {
        $('#ResultCliente').html("");
        $('#ResultCliente').html(resposta);
      }
    });
  }
}


MWERP.IniciaMenu = function () {
  $(".close-sidebar").click(function () {
    $(".sidebar").css("width", "0");
    $(".side-content").css("margin-left", "0");
    $(".user-info").css("margin-left", "75px");
  });

  $(".open-sidebar").click(function () {
    $(".sidebar").css("width", "250px");
    $(".side-content").css("margin-left", "250px");
    $(".user-info").css("margin-left", "275px");
  });

  $(document).on("click", "#salvarMudarOrg", function () {
    const idOrganizacao = $("#UsuarioOrganizacaoSelect_id").val();
    let idFilial = $("#UsuarioFilialSelect_id").val();

    if (idOrganizacao != "0" && idFilial != "0") {
      idFilial = parseInt(idFilial);

      if (idFilial == -1)
        idFilial = null;

      const obj = {
        idOrganizacao: parseInt(idOrganizacao),
        idFilial: idFilial
      };

      $.post(GetURLBaseComplete() + "/Account/ChangeOrganizacao", obj, function (data) {
        data = JSON.parse(data);
        if (data.Sucesso) 
          location.reload();
        else if (!data.Sucesso && data.Reload)
          location.reload();
        else 
          Alerta("Erro", data.Mensagem, "red", 5000);
      });
    }
    else {
      Alerta("Aviso", "Por favor selecione os campos de organização e filial.", "yellow", 5000);
    }
  });  
}

MWERP.IniciaMascaras = function () {
  //  $('[data-toggle="tooltip"]').tooltip();

    $('.telefone').inputmask({
      mask: '(99)99999-999[9]',
      removeMaskOnSubmit: true
    });

  $('.telefone2').inputmask({
    mask: '(99)9999-999[9]',
    removeMaskOnSubmit: true
  });

  $(".CartaoMask").inputmask({ "mask": "9999" });
  //$(".CodBancoAgencia").inputmask({ "mask": "**********" });
  //$(".CodConta").inputmask({ "mask": "***************" });
  //$(".CodDV").inputmask({ "mask": "9" });

  //  $(".phone").inputmask({
  //    mask: ["(99) 9999-9999", "(99) 99999-9999"],
  //    keepStatic: true,
  //    removeMaskOnSubmit: true
  //  });

  $('.CEP').inputmask('99.999-999', { removeMaskOnSubmit: true });

  $('.Data').inputmask('99/99/9999', { removeMaskOnSubmit: false });

  $('.DateTimePick').inputmask('99/99/9999', { removeMaskOnSubmit: true });

  $('.numerosOnly').inputmask('9{1,15}', { removeMaskOnSubmit: true });


  //  $('.numerosOnlyTwo').inputmask('9{1,2}', { removeMaskOnSubmit: true });

  //  $('.numerosOnlyTen').inputmask('9{1,9}', { removeMaskOnSubmit: true });

  //  $('.numerosOnlyNine').inputmask('9{1,9}', { removeMaskOnSubmit: true });

  //  $('.numerosOnlySeven').inputmask('9{1,7}', { removeMaskOnSubmit: true });

  //  $('.numerosOnlyFour').inputmask('9{1,4}', { removeMaskOnSubmit: true });

  //  $('.numerosOnlyThree').inputmask('9{1,3}', { removeMaskOnSubmit: true });

    $('.CPF').inputmask({
      mask: '999.999.999-99',
      removeMaskOnSubmit: true
    });
  $('.CPFCNPJ').inputmask({
    mask: ['999.999.999-99', '99.999.999/9999-99'],
    removeMaskOnSubmit: true
  });

  //  $('.timehour').inputmask("hh:mm", {
  //    removeMaskOnSubmit: false,
  //  });

  $(".moneyMask").maskMoney({
    prefix: 'R$ ',
    affixesStay: false,
    allowNegative: false,
    thousands: '',
    decimal: ',',
    affixesStay: false
  });

  $(".money").inputmask('decimal', {
    removeMaskOnSubmit: true,
    alias: 'numeric',
    groupSeparator: '.',
    autoGroup: true,
    digits: 2,
    integerDigits: 7,
    radixPoint: ",",
    digitsOptional: false,
    allowMinus: false,
    prefix: 'R$ ',
    placeholder: ''
  });
  $(".percent").inputmask('decimal', {
    removeMaskOnSubmit: true,
    alias: 'numeric',
    groupSeparator: '.',
    autoGroup: true,
    digits: 2,
    integerDigits: 7,
    radixPoint: ",",
    digitsOptional: false,
    allowMinus: false,
    suffix: ' %',
    placeholder: ''
  });

  $(".porcentagem").inputmask('decimal', {
    removeMaskOnSubmit: true,
    alias: 'numeric',
    autoGroup: true,
    integerDigits: 3,
    allowMinus: false,
    suffix: ' %',
    placeholder: ''
  });

  $(".number").inputmask('decimal', {
    removeMaskOnSubmit: true,
    alias: 'numeric',
    groupSeparator: '.',
    autoGroup: true,
    integerDigits: 7,
    radixPoint: ",",
    digitsOptional: false,
    allowMinus: false,
    placeholder: ''
  });

  //  $(".perAcresc").inputmask('decimal', {
  //    removeMaskOnSubmit: true,
  //    alias: 'numeric',
  //    groupSeparator: '.',
  //    autoGroup: true,
  //    digits: 2,
  //    integerDigits: 7,
  //    radixPoint: ",",
  //    digitsOptional: false,
  //    allowMinus: false,
  //    prefix: '',
  //    placeholder: ''
  //  });

  //  $('.Complemento').inputmask({
  //    mask: "a{1,20}",
  //    removeMaskOnSubmit: true,
  //    greedy: false,
  //    onBeforePaste: function (pastedValue, opts) {
  //      pastedValue = pastedValue.toLowerCase();
  //      return pastedValue.replace("mailto:", "");
  //    },
  //    definitions: {
  //      'a': {
  //        validator: "[.A-Za-z0-9 ]",
  //        cardinality: 1,
  //        casing: "upper"
  //      }
  //    }
  //  });

    $('.CNPJ').inputmask('99.999.999/9999-99', { removeMaskOnSubmit: true });

  //  $('.INSS').inputmask('***********', { removeMaskOnSubmit: true });

    $('.EMAIL').inputmask({
      mask: "*{1,100}@*{1,20}[.*{2,6}][.*{1,2}]",
      greedy: false,
      onBeforePaste: function (pastedValue, opts) {
        pastedValue = pastedValue.toLowerCase();
        return pastedValue.replace("mailto:", "");
      },
      definitions: {
        '*': {
          validator: "[.0-9A-Za-z!#$%&'*+/=?^_`{|}~\-]",
          cardinality: 1,
          casing: "lower"
        }
      }
    });
  //  $('.URLINPUT').inputmask({
  //    mask: "http://*{1,100}",
  //    greedy: false,
  //    onBeforePaste: function (pastedValue, opts) {
  //      pastedValue = pastedValue.toLowerCase();
  //      return pastedValue.replace("mailto:", "");
  //    },
  //    definitions: {
  //      '*': {
  //        validator: "[.0-9A-Za-z!#$%&'*+/=?^_`{|}~\-]",
  //        cardinality: 1,
  //        casing: "lower"
  //      }
  //    }
  //  });

  //  $('.RG').inputmask('99.999.999-9', { removeMaskOnSubmit: true });

}

MWERP.ConvertDate = function (inputFormat) {
  function pad(s) { return (s < 10) ? '0' + s : s; }
  var d = new Date(inputFormat)
  return [pad(d.getDate()), pad(d.getMonth() + 1), d.getFullYear()].join('/')
};

MWERP.VerificaNumero = function (event) {
  $(document).on("keypress keyup blur", ".allownumericwithoutdecimal", function (event) {
    $(this).val($(this).val().replace(/[^\d].+/, ""));
    if ((event.which < 48 || event.which > 57)) {
      event.preventDefault();
    }
  });
};

MWERP.IniciaInputs = function () {
  $('.PickerDate').datepicker({
    language: 'pt-BR',
    position: 'top left'
  });

  $('.PickerDateBottomLeft').datepicker({
    language: 'pt-BR',
    position: 'bottom left'
  });

  $('.PickerDatebottom').datepicker({
    language: 'pt-BR',
    position: 'bottom left'
  });

  $('.PickerTimebottom').datepicker({
    language: 'pt-BR',
    position: 'bottom left',
    timepicker: true,
    dateFormat: ' ',
    onlyTimepicker: false
  });

  $('.PickerDateRange').datepicker({
    language: 'pt-BR',
    position: 'bottom left',
    range: true,
    toggleSelected: false,
    multipleDatesSeparator: " - "
  });

}

var Loading = {
  On: function () {
    $(".overlay").css({ "display": "block", "opacity": "1" });
    $(".overlay > .spinner").css({ "display": "block" });
  },
  Off: function () {
    $(".overlay").css({ "display": "none", "opacity": "0" });
    $(".overlay > .spinner").css({ "display": "none" });
  }
}

window.onload = function () {
  Loading.Off();
  $('html, body').animate({
    scrollTop: 0
  }, 300);
};

jQuery.ajaxSetup({
  beforeSend: function (xhr) {
    Loading.On();
  },

  complete: function (xhr, status) {
    Loading.Off();
  }
});

$(document).ready(function () {
  MWERP.init();
  MWERP.InfoAddFornecedor();
  MWERP.InfoAddProduto();
  MWERP.InfoAddCliente();
});

let firstCurrencyLoad = true;

function Alerta(title, message, type, timer, position) {
  iziToast.show({
    title: title,
    message: message,
    position: position ? position : 'bottomRight',
    color: type,
    timeout: timer === false ? timer : timer ? timer : 5000,
    drag: false
  });
}

function ToastOpcoes(title, message, color, timeout, buttons) {

  iziToast.question({
    timeout: timeout,
    close: false,
    overlay: true,
    displayMode: 2,
    color: color, // blue, red, green, yellow
    //zindex: 999,
    title: title,
    message: message,
    position: 'center',
    buttons: buttons
  });
}

function GetURLBase() {
  var pathArray = location.href.split('/');
  var protocol = pathArray[0];
  var host = pathArray[2];
  var url = protocol + '//' + host;

  return url;
}

function GetURLBaseComplete() {
  var pathArray = location.href.split('/');
  var protocol = pathArray[0];
  var host = pathArray[2];
  var app = pathArray[3];
  var url = protocol + '//' + host + '/' + app;

  return url;
}

function SweetAlert(titulo, texto, icon, button) {
  swal({
    title: titulo,
    text: texto,
    icon: icon,
    button: button,
  });
}

function DeleteItem(url, id) {
  swal({
    title: "Apagar",
    text: "Tem certeza que deseja apagar?",
    icon: "warning",
    buttons: ["Cancelar", "Apagar"],
    dangerMode: true,
  })
    .then((continuar) => {
      if (continuar) {
        $.post(GetURLBaseComplete() + "/" + url + "/Delete/" + id, function (data) {
          data = JSON.parse(data);
          if (data.Sucesso) {
            location.reload();
          }
          else {
            Alerta("Erro", data.Mensagem, "red");
          }
        });
      }
    });
}