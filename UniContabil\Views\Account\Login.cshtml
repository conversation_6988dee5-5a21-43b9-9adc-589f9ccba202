﻿@using UniContabil.Infrastructure.Controls
@using System.Configuration
@model UniContabilEntidades.Models.LoginModel
@{
  ViewBag.Title = "Login";
  Layout = "~/Views/Shared/_LayoutAccount.cshtml";
  var UrlBase = ConfigurationManager.AppSettings["URLBase"];
}

<link rel="stylesheet" href="~/Views/Account/Account.css?awa=ads" />

<div>
  <div class="col-md-3 divcard">
    <h4>ACESSE SUA CONTA</h4>
    @using (Html.BeginForm("Login", "Account", FormMethod.Post))
    {
      <div class="row">
        <div class="col-md-12">
          <div class="form-group">
            @Html.TextBoxFor(m => m.CPF, new { type = "text", @class = "CPF form-control", placeholder = "CPF" })
            @Html.ValidationMessageFor(m => m.CPF)
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <div class="form-group">
            @Html.PasswordFor(m => m.Senha, new { @class = "form-control", placeholder = "Senha" })
            @Html.ValidationMessageFor(m => m.Senha)

          </div>
        </div>
      </div>
      @*<div class="row">
          <div class="col-md-12">
            <div class="form-group" style="float:left !important">
              <input type="checkbox" class="form-control" />
              Continuar Conectado
            </div>
          </div>
        </div>*@
      <div class="row" style="justify-content: center">
        <button class="btn" style="background-color: #2375aa;
                                    font-size: 15pt !important;
                                    color: white;" type="submit">
          Entrar
        </button>
      </div>
    }
    <div class="row" style="justify-content: center">
      <a class="" href="@Url.Action("ForgotPassword")">Esqueceu sua senha?</a>
    </div>
    <div class="row" style="justify-content: center">
      <a class="" href="@Url.Action("Register","Account")">Não possuo uma conta.</a>
    </div>
  </div>
</div>
<style>
  .divcard {
    position: fixed;
    right: 5%;
    top: 31%;
    width: 100%;
    height: 50vh;
    text-align: center;
  }

  .form-group {
    margin-bottom: 1rem !important;
  }

  input {
    background-color: #eef4f3 !important;
    height: calc(2.75rem + 2px) !important;
    color: #818181 !important;
    font-size: 15pt !important;
  }

  h4 {
    color: #2e7daf;
    font-family: monospace;
  }

  body {
    background-image: url('@(UrlBase)Content/img/Imagem home unicontabil.png'), url('@(UrlBase)Content/img/Logo_Unicontabil.jpeg'), url('@(UrlBase)Content/img/fundo.svg');
    background-repeat: no-repeat !important;
    background-size: 40%, 30%, 100% !important;
    background-position-x: 15%, 95% !important;
    background-position-y: 0%, -50% !important;
  }
</style>