﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@model UniContabilEntidades.Models.TabelaTipoMovimentacaoModel
@{
  ViewBag.Title = "Pedido Compra";
  //Layout = "~/Views/Shared/_LayoutAccount.cshtml";
}

@*<script src="~/Views/TabelaTipoMovimentacaoModel/TabelaTipoMovimentacaoModel.js"></script>
<link rel="stylesheet" href="~/Views/TabelaTipoMovimentacaoModel/TabelaTipoMovimentacaoModel.css?b=b" />*@

<div class="card">
  @using (Html.BeginForm("Create", "TabelaTipoMovimentacao", FormMethod.Post@*, new { @style = "text-align: end;" }*@))
{
<div class="card-header">
  <div class="card-title">
  </div>
  <div class="card-options">
  </div>
</div>
<div class="card-body" id="CabecalhoTabelaTipoMovimentacaoModel">
  @Html.AntiForgeryToken()
  <div class="form-group">
    @Html.LabelFor(a => a.Descricao)
    @Html.TextBoxFor(a => a.Descricao, new { @class = "form-control" })
    @Html.ValidationMessageFor(a => a.Descricao)
  </div>
  <div class="form-group">
    @Html.LabelFor(a => a.ICMS)
    @Html.TextBoxFor(a => a.ICMS, new { @class = "form-control percent" })
    @Html.ValidationMessageFor(a => a.ICMS)
  </div>
  <div class="form-group">
    @Html.LabelFor(a => a.PIS)
    @Html.TextBoxFor(a => a.PIS, new { @class = "form-control percent" })
    @Html.ValidationMessageFor(a => a.PIS)
  </div>
  <div class="form-group">
    @Html.LabelFor(a => a.COFINS)
    @Html.TextBoxFor(a => a.COFINS, new { @class = "form-control percent" })
    @Html.ValidationMessageFor(a => a.COFINS)
  </div>
  <div class="form-group">
    @Html.LabelFor(a => a.BC)
    @Html.TextBoxFor(a => a.BC, new { @class = "form-control percent" })
    @Html.ValidationMessageFor(a => a.BC)
  </div>
  <div class="form-group">
    @Html.Label("CodigoCFOP")
    <div style="display: flex; justify-content: space-between; align-items: flex-end;">
      <div style="width: 85%">
        @Html.Select2("CFOPSelect", "GetCFOPSelect", "Selecione o CFOP", "", "", Model.CFOPSelect == null ? "" : Model.CFOPSelect.id, Model.CFOPSelect == null ? "" : Model.CFOPSelect.text)
      </div>
      <div style="width: 10%; display: flex;">
        <i class="mw-drip-search" id="DetalhesCFOP"></i>
      </div>
    </div>
  </div>

</div>
<div class="card-footer">
  @if (ContextoUsuario.HasPermission("TabelaTipoMovimentacao", TipoFuncao.Create))
  {
    <button type="submit" class="btn btn-success">Salvar</button>
  }
</div>

}
</div>
