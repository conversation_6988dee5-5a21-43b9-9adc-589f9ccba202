﻿@model UniContabilEntidades.Models.MovimentacaoBancariaModel
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Editar " + @Html.DisplayNameFor(model => model);

  Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="card">
  <form asp-action="Create">
    <div class="card-header">
      <div class="card-title">
      </div>
      <div class="card-options">
      </div>
    </div>
    <div class="card-body">
      @Html.AntiForgeryToken()
      <div asp-validation-summary="ModelOnly" class="text-danger"></div>
      <input type="hidden" asp-for="Id" />
      <div class="form-group">
        <label asp-for="IdOrganizacao" class="control-label"></label>
        @Html.Select2("OrganizacaoSelect", "GetOrganizacaoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdOrganizacao), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.OrganizacaoSelect == null ? "" : Model.OrganizacaoSelect.id, Model.OrganizacaoSelect == null ? "" : Model.OrganizacaoSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="IdFilial" class="control-label"></label>
        @Html.Select2("FilialSelect", "GetFilialSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdFilial), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.FilialSelect == null ? "" : Model.FilialSelect.id, Model.FilialSelect == null ? "" : Model.FilialSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="IdCentroCusto" class="control-label"></label>
        @Html.Select2("CentroCustoSelect", "GetCentroCustoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdCentroCusto), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.CentroCustoSelect == null ? "" : Model.CentroCustoSelect.id, Model.CentroCustoSelect == null ? "" : Model.CentroCustoSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="IdContaContabel" class="control-label"></label>
        @Html.Select2("ContaContabelSelect", "GetContaContabelSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdContaContabel), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.ContaContabelSelect == null ? "" : Model.ContaContabelSelect.id, Model.ContaContabelSelect == null ? "" : Model.ContaContabelSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="IdHistoricoPadrao" class="control-label"></label>
        @Html.Select2("HistoricoPadraoSelect", "GetHistoricoPadraoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdHistoricoPadrao), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.HistoricoPadraoSelect == null ? "" : Model.HistoricoPadraoSelect.id, Model.HistoricoPadraoSelect == null ? "" : Model.HistoricoPadraoSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="IdBanco" class="control-label"></label>
        @Html.Select2("BancoSelect", "GetBancoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdBanco), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.BancoSelect == null ? "" : Model.BancoSelect.id, Model.BancoSelect == null ? "" : Model.BancoSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="IdTipoLancamento" class="control-label"></label>
        @Html.Select2("TipoLancamentoSelect", "GetTipoLancamentoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdTipoLancamento), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.TipoLancamentoSelect == null ? "" : Model.TipoLancamentoSelect.id, Model.TipoLancamentoSelect == null ? "" : Model.TipoLancamentoSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="Registro" class="control-label"></label>
        <input asp-for="Registro" class="form-control" />
        <span asp-validation-for="Registro" class="text-danger"></span>
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.DataInicio)
        @Html.TextBoxFor(a => a.DataInicio, new { @class = "form-control Data" })
        @Html.ValidationMessageFor(a => a.DataInicio)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.DataFim)
        @Html.TextBoxFor(a => a.DataFim, new { @class = "form-control Data" })
        @Html.ValidationMessageFor(a => a.DataFim)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.NumeroDocumento)
        @Html.TextBoxFor(a => a.NumeroDocumento, new { @class = "form-control numerosOnly" })
        @Html.ValidationMessageFor(a => a.NumeroDocumento)
      </div>
      <div class="form-group">
        <label asp-for="HistoricoMovimento" class="control-label"></label>
        <input asp-for="HistoricoMovimento" class="form-control" />
        <span asp-validation-for="HistoricoMovimento" class="text-danger"></span>
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.ValorLancamento)
        @Html.TextBoxFor(a => a.ValorLancamento, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.ValorLancamento)
      </div>
    </div>
    <div class="card-footer">
      <a asp-action="Index" class="link link-info">Voltar</a>
      <input type="submit" value="Adicionar" class="btn btn-primary" />
    </div>
  </form>
</div>