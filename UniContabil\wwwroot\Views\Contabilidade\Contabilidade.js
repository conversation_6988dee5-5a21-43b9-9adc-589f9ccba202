﻿var Contabilidade = function () { };

function reloadAba(aba) {

  let name = "";

  if (!aba)
    aba = "aba-users"

  name = aba;
  if (aba == "aba-users") {
    $("#aba-organizacoes").removeClass("active");
    $("#body-aba-organizacoes").addClass("hide-body");

    $("#aba-users").addClass("active");
    $("#body-aba-users").removeClass("hide-body");

  } else {
    $("#aba-users").removeClass("active");
    $("#body-aba-users").addClass("hide-body");

    $("#aba-organizacoes").addClass("active");
    $("#body-aba-organizacoes").removeClass("hide-body");
  }
}

Contabilidade.VerAnexo = function (IdAnexo) {
  $("#iframeVerAnexo").html('');
  $("#iframeVerAnexo").attr("src", GetURLBaseComplete() + "/Contabilidade/VerAnexo/" + IdAnexo);
  $("#ModalVerAnexo").modal('show');
}

Contabilidade.AdicionarUsuarioContabilidade = function () {
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + "/Contabilidade/_ModalCreateUserSemCpf?IdContab=" + $("#IdContab").val(),
    success: function (data) {
      $("#ModalCriarUsuario .modal-body").html(data);

      $('#divCadastro .telefone').inputmask({
        mask: '(99)99999-999[9]',
        removeMaskOnSubmit: true
      });

      $('#divCadastro .CPF').inputmask({
        mask: '999.999.999-99',
        removeMaskOnSubmit: true
      });

      $('#divCadastro .EMAIL').inputmask({
        mask: "*{1,100}@*{1,20}[.*{2,6}][.*{1,2}]",
        greedy: false,
        onBeforePaste: function (pastedValue, opts) {
          pastedValue = pastedValue.toLowerCase();
          return pastedValue.replace("mailto:", "");
        },
        definitions: {
          '*': {
            validator: "[.0-9A-Za-z!#$%&'*+/=?^_`{|}~\-]",
            cardinality: 1,
            casing: "lower"
          }
        }
      });
      $("#ModalCriarUsuario").modal('show');
    }
  })
}

Contabilidade.DeleteAnexo = function (IdAnexo) {
  $.ajax({
    type: "GET",
    url: GetURLBaseComplete() + "/Contabilidade/DeleteAnexo?Id=" + IdAnexo,
    success: function (data) {
      if (!data.error) {
        Contabilidade.loadAnexos();
        Alerta("Sucesso", data.message, "green")
      } else {
        Alerta("Erro", data.message, "red")
      }
    }
  })
}

Contabilidade.EnviarAprovacao = function () {
var data = $("#formEditContabilidade").serialize()

  $.ajax({
    type: "POST",
    url: GetURLBaseComplete() + "/Contabilidade/SalvarEnviarAprovacao/",
    data: data,
    success: function (data) {
      if (!data.error) {
        location.reload();
        Alerta("Sucesso", data.message, "green")
      } else {
        Alerta("Erro", data.message, "red")
      }
    }
  })
}

Contabilidade.DeleteUser = function (IdUser) {
  $.ajax({
    type: "GET",
    url: GetURLBaseComplete() + "/Contabilidade/DeleteUser?Id=" + IdUser,
    success: function (data) {
      if (!data.error) {
        Contabilidade.loadUsers();
        Alerta("Sucesso", data.message, "green")
      } else {
        Alerta("Erro", data.message, "red")
      }
    }
  })
}

Contabilidade.loadAnexos = function () {
  $.ajax({
    type: "GET",
    url: GetURLBaseComplete() + "/Contabilidade/_GetGridAnexos?IdContab=" + $("#IdContab").val(),
    success: function (data) {
      $('#tabelaAnexos').html(data);
    }
  })
}

Contabilidade.loadUsers = function () {
  $.ajax({
    type: "GET",
    url: GetURLBaseComplete() + "/Contabilidade/_GetGridUsers?IdContab=" + $("#IdContab").val(),
    success: function (data) {
      $('#tabelaUsers').html(data);
    }
  })
}

Contabilidade.EnviarAdicionarContab = function () {
  $.ajax({
    type: "POST",
    url: GetURLBaseComplete() + "/Contabilidade/CreateContabilidadeUser",
    data: $("#formCadastroContabilidade").serialize(),
    success: function (data) {
      if (!data.error) {
        Alerta("Sucesso", data.message, "green")
        $("#ModalCriarUsuario input").val('');
        Contabilidade.loadUsers();
        $("#ModalCriarUsuario").modal('hide')
      } else {
        Alerta("Erro", data.message, "red")
      }
    }
  })
}

Contabilidade.EnviarAdicionar = function () {
  var model = {
    CPF: $("#cpfAdicionar").inputmask('unmaskedvalue'),
    HasAdm: $("#isAdm").is(":checked"),
    IdContabilidade: $("#IdContabilidade").val()
  }
  $.ajax({
    type: "POST",
    url: GetURLBaseComplete() + "/Contabilidade/AdicionaCpfContabilidade",
    data: model,
    success: function (data) {
      if (!data.error) {
        Alerta("Sucesso", data.message, "green")
        Contabilidade.loadUsers()
      } else {
        Alerta("Erro", data.message, "red")
        if (!data.hasCPF) {
          $.ajax({
            type: "GET",
            url: GetURLBaseComplete() + "/Contabilidade/_ModalCreateUser?CPF=" + data.cpf
              + "&isAdm=" + data.isAdm + "&IdContab=" + data.idContab,
            data: model,
            success: function (dataIn) {
              $("#ModalCriarUsuario .modal-body").html(dataIn);

              $('#divCadastro .telefone').inputmask({
                mask: '(99)99999-999[9]',
                removeMaskOnSubmit: true
              });

              $('#divCadastro .CPF').inputmask({
                mask: '999.999.999-99',
                removeMaskOnSubmit: true
              });

              $('#divCadastro .EMAIL').inputmask({
                mask: "*{1,100}@*{1,20}[.*{2,6}][.*{1,2}]",
                greedy: false,
                onBeforePaste: function (pastedValue, opts) {
                  pastedValue = pastedValue.toLowerCase();
                  return pastedValue.replace("mailto:", "");
                },
                definitions: {
                  '*': {
                    validator: "[.0-9A-Za-z!#$%&'*+/=?^_`{|}~\-]",
                    cardinality: 1,
                    casing: "lower"
                  }
                }
              });
              $("#ModalCriarUsuario").modal('show');
            }
          });
        }
      }
    }
  });
}

Contabilidade.init = function () {
  reloadAba(null);
  $("#aba-users").click(function () {
    $(".organizacoes").html("");
    $("#body-tipo-grupo-filial").html("");
    reloadAba("aba-users");
  });

  $("#aba-organizacoes").click(function () {
    $(".spinner-table").attr("style", "display: flex !important; margin-top: 5px;margin-bottom: 5px;");
    reloadAba("aba-organizacoes");
  });
  Contabilidade.loadAnexos();
  Contabilidade.loadUsers();

  $(document).on("keyup", "#CPFUser", function (e) {
    if ($(this).inputmask('unmaskedvalue').length == 11) {
      $.ajax({
        type: 'GET',
        url: GetURLBaseComplete() + "/Contabilidade/VerificaCpf?cpf=" + $(this).inputmask('unmaskedvalue'),
        success: function (data) {
          if (!data.error) {
            if (data.hasCpf) {
              $(".PassUser").attr('hidden', true);
              $(".ConfirmEmail").attr('hidden', true);
              $("#TelefoneUser").val(data.telefone)
              $("#EmailUser").val(data.email)
              $("#ConfirmEmail").val(data.email)
              $("#NomeUser").val(data.nome)
              $("#IdUser").val(data.idUser)
              $("#BtnCadastrar").text('Vincular')


              $("#EmailUser").attr('readonly', true);
              $("#TelefoneUser").attr('readonly', true);
              $("#ConfirmEmail").attr('readonly', true);
              $("#NomeUser").attr('readonly', true);

              Alerta("Atenção", "CPF encontrado no sistema", "green")
            } else {
              //Alerta("Atenção", "CPF não encontrado no sistema", "yellow")
              $(".PassUser").attr('hidden', false);
              $("#EmailUser").attr('readonly', false);
              $("#TelefoneUser").attr('readonly', false);
              $("#ConfirmEmail").attr('readonly', false);
              $("#NomeUser").attr('readonly', false);
              $("#IdUser").val('')
              $(".ConfirmEmail").attr('hidden', false);
              $("#BtnCadastrar").text('Cadastrar e vincular')
            }
          }
        }
      })
    } else {
      $(".PassUser").attr('hidden', false);
      $(".ConfirmEmail").attr('hidden', false);
      $("#BtnCadastrar").val('Cadastrar e vincular')
      $("#EmailUser").attr('readonly', false);
      $("#TelefoneUser").attr('readonly', false);
      $("#ConfirmEmail").attr('readonly', false);
      $("#NomeUser").attr('readonly', false);
      $("#IdUser").val('')
      $(".ConfirmEmail").attr('hidden', false);
      $("#BtnCadastrar").text('Cadastrar e vincular')
    }
  })

  $(document).on("change", "#fileAnexo", function (a) {
    if (typeof (FileReader) !== "undefined") {
      var fileName = a.target.value.split('\\').pop();
      var extensao = fileName.split(".")[fileName.split(".").length - 1];
      var reader = new FileReader();
      if (extensao.toLowerCase() == "pdf" || extensao.toLowerCase() == "png" || extensao.toLowerCase() == "jpg") {
        var fileInput = $('#fileAnexo')[0];
        var file = fileInput.files[0];
        var fdata = new FormData();
        fdata.append("File", file);
        fdata.append("IdContabilidade", $("#IdContabilidade").val());

        $.ajax({
          type: "POST",
          url: GetURLBaseComplete() + "/Contabilidade/SalvaAnexo",
          contentType: false,
          processData: false,
          data: fdata,
          success: function (message) {
            if (!message.erro) {
              Contabilidade.loadAnexos();
              Alerta("Sucesso", message.mensagem, 'grenn', 5000)
            } else {
              Alerta("Alerta", message.mensagem, 'yellow', 5000)
            }
          },
          error: function () {
            console.log("There was error uploading files!");
          }
        });

        //var model = {
        //  IdContabilidade: $("#IdContab").val(),
        //  File: $('#fileAnexo')[0]
        //}

        //$.ajax({
        //  type: "POST",
        //  url: GetURLBaseComplete() + "/Contabilidade/SalvaAnexo",
        //  data: model,
        //  success: function (data) {
        //    if (!data.error) {
        //      Alerta("Sucesso", data.message, "success")
        //      Contabilidade.loadAnexos();
        //    } else {
        //      Alerta("Erro", data.message, "error")
        //    }
        //  }
        //})
      } else {
        $("#anexo").val("");
        SweetAlert("Erro", "O arquivo anexado é diferente do arquivo permitido", "error")
      }


      //reader.onload = function (e) {

      //}
      //reader.readAsDataURL($('#anexo')[0].files[0]);

    }
    else {
      Alerta("Compatibilidade", "Este navegador não suporta FileReader.", 'yellow', 5000);
    }
  });

  $(document).on("click", ".btns-avaliacao", function (a) {
    const btn = $(this).data("button");

    if (btn == "aprovar") {
      $("#modalLabel")[0].innerText = 'Aprovar'
      $("#btn-aprovar").show();
      $("#btn-reprovar").hide();
    } else {
      $("#modalLabel")[0].innerText = 'Reprovar'
      $("#btn-aprovar").hide();
      $("#btn-reprovar").show();
    }
    $("#motivo").val("");
    $("#modal").modal("show");
  });

  $(document).on("click", ".IsPJ", function () {
    var check = $(this).val();
    Contabilidade.IsPJ(check);

  });
}

Contabilidade.Aprovar = function (cod) {
  $("#modal").modal("hide");
  $(".spinner-menu").attr("style", "display: block !important");
  let motivo = $("#motivo").val();
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/Contabilidade/Aprovar',
    dataType: 'json',
    data: {
      id: cod,
      motivo: motivo,
    },
    success: function (data) {
      $(".spinner-menu").attr("style", "display: none !important");
      if (!data.Error) {
        location.reload();
        SweetAlert("Sucesso", data.mensage, "success");
      }
      else {
        SweetAlert("Erro", data.mensage, "danger");
      }
    }
  });
}

Contabilidade.Reprovar = function (cod) {
  $("#modal").modal("hide");
  $(".spinner-menu").attr("style", "display: block !important");

  let motivo = $("#motivo").val();
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/Contabilidade/Reprovar',
    dataType: 'json',
    data: {
      id: cod,
      motivo: motivo,
    },
    success: function (data) {
      $(".spinner-menu").attr("style", "display: none !important");
      if (!data.Error) {
        location.reload();
        SweetAlert("Sucesso", data.mensage, "success");
      }
      else {
        SweetAlert("Erro", data.mensage, "danger");
      }
    }
  });
}

Contabilidade.completaCampos = function () {
  var val = $('#campoCEP').inputmask('unmaskedvalue');

  if (val.length == 8) {
    $.ajax({
      url: 'https://viacep.com.br/ws/' + val + '/json',
      type: 'GET',
      dataType: 'json',
      success: function (data) {
        if (data.erro) {

        } else {
          $('#Logradouro').val(data['logradouro'])
          $('#Complemento').val(data['complemento'])
          $('#Bairro').val(data['bairro'])
          $.ajax({
            type: 'GET',
            url: GetURLBaseComplete() + '/select2/GetUFSelect?term=' + data['uf'],
            success: function (sucessData) {
              console.log(sucessData)
              $("#select2-UFSelectLookup-container").attr('title', sucessData['items'][0]['text'])
              $("#UFSelect_id").val(sucessData['items'][0]['id'])
              $("#UFSelect_text").val(sucessData['items'][0]['text'])
              $('#select2-UFSelectLookup-container').html('')
              $('#select2-UFSelectLookup-container').append(sucessData['items'][0]['text'])
              $.ajax({
                type: 'GET',
                url: GetURLBaseComplete() + '/select2/GetMunicipioSelect?term=' + data['localidade'] + "&id=" + sucessData['items'][0]['id'],
                success: function (sucessDataCidade) {
                  console.log(sucessDataCidade)
                  $("#select2-MunicipioSelectLookup-container").attr('title', sucessDataCidade['items'][0]['text'])
                  $("#MunicipioSelect_id").val(sucessDataCidade['items'][0]['id'])
                  $("#MunicipioSelect_text").val(sucessDataCidade['items'][0]['text'])
                  $('#select2-MunicipioSelectLookup-container').html('')
                  $('#select2-MunicipioSelectLookup-container').append(sucessDataCidade['items'][0]['text'])
                }
              })
            }
          })
        }
      }
    })
  }
}

Contabilidade.IsPJ = function (check) {
  if (check == 1) {
    $("#lblCPF").html("");
    $("#lblCPF").html("CNPJ:");
    $("#cpf").removeClass("CPF");
    $("#cpf").addClass("CNPJ");
  }
  else {
    $("#lblCPF").html("");
    $("#lblCPF").html("CPF:");
    $("#cpf").removeClass("CNPJ");
    $("#cpf").addClass("CPF");
  }
  $("#IsPJ_" + check)[0].checked = true;

  $('.CPF').inputmask({
    mask: '999.999.999-99',
    removeMaskOnSubmit: true
  });
  $('.CNPJ').inputmask('99.999.999/9999-99', { removeMaskOnSubmit: true });
}

$(document).ready(function () {
  Contabilidade.init();

  var sizecampo = $("#cpf").val().length;

  if (sizecampo > 11) {
    Contabilidade.IsPJ(1);
  }
  else
  {
    Contabilidade.IsPJ(0);
  }
})