﻿@model UniContabilEntidades.Models.FornecedorModel
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Novo " + @Html.DisplayNameFor(model => model);
  Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="card">
  <form asp-action="Create">
    <div class="card-header">
      <div class="card-title">
      </div>
      <div class="card-options">
      </div>
    </div>
    <div class="card-body">
      @Html.AntiForgeryToken()
      <div asp-validation-summary="ModelOnly" class="text-danger"></div>
      <div class="form-group">
        <label asp-for="IdTipoFornecedor" class="control-label"></label>
        @Html.Select2("TipoFornecedorSelect", "GetTipoFornecedorSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdTipoFornecedor), this.ViewContext.RouteData.Values["controller"].ToString())
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.CPFCNPJ)
        @Html.TextBoxFor(a => a.CPFCNPJ, new { @class = "form-control CPF" })
        @Html.ValidationMessageFor(a => a.CPFCNPJ)
      </div>
      <div class="form-group">
        <label asp-for="Nome" class="control-label"></label>
        <input asp-for="Nome" class="form-control" />
        <span asp-validation-for="Nome" class="text-danger"></span>
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.CEP)
        @Html.TextBoxFor(a => a.CEP, new { @class = "form-control CEP" })
        @Html.ValidationMessageFor(a => a.CEP)
      </div>
      <div class="form-group">
        <label asp-for="TipoLogradouro" class="control-label"></label>
        <input asp-for="TipoLogradouro" class="form-control" />
        <span asp-validation-for="TipoLogradouro" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="Logradouro" class="control-label"></label>
        <input asp-for="Logradouro" class="form-control" />
        <span asp-validation-for="Logradouro" class="text-danger"></span>
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.Numero)
        @Html.TextBoxFor(a => a.Numero, new { @class = "form-control numerosOnly" })
        @Html.ValidationMessageFor(a => a.Numero)
      </div>
      <div class="form-group">
        <label asp-for="Complemento" class="control-label"></label>
        <input asp-for="Complemento" class="form-control" />
        <span asp-validation-for="Complemento" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="Cidade" class="control-label"></label>
        <input asp-for="Cidade" class="form-control" />
        <span asp-validation-for="Cidade" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="Estado" class="control-label"></label>
        <input asp-for="Estado" class="form-control" />
        <span asp-validation-for="Estado" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="TelefoneFixo" class="control-label"></label>
        <input asp-for="TelefoneFixo" class="form-control" />
        <span asp-validation-for="TelefoneFixo" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="TelefoneCelular" class="control-label"></label>
        <input asp-for="TelefoneCelular" class="form-control" />
        <span asp-validation-for="TelefoneCelular" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="Email" class="control-label"></label>
        <input asp-for="Email" class="form-control" />
        <span asp-validation-for="Email" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="TelefoneWhatsApp" class="control-label"></label>
        <input asp-for="TelefoneWhatsApp" class="form-control" />
        <span asp-validation-for="TelefoneWhatsApp" class="text-danger"></span>
      </div>
      <div class="form-group form-check">
        <div class="row">
          <div class="col-md-12">
            <label class="form-check-label">
              <input class="form-check-input" asp-for="Ativo" /> @Html.DisplayNameFor(model => model.Ativo)
            </label>
          </div>
        </div>
      </div>
    </div>
   
    <div class="card-footer">
      <a asp-action="Index" class="link link-info">Voltar</a>
      <input type="submit" value="Adicionar" class="btn btn-primary" />
    </div>
  </form>
</div>

