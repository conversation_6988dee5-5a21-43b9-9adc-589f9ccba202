﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;

namespace UniContabil.Controllers
{
  public class TipoFornecedorController : LibController
  {
    public TipoFornecedorController()
    { }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        IPagedList<TipoFornecedorModel> List = new TipoFornecedorServices().GetPagedList(page);
        ViewBag.PageItems = List;
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(TipoFornecedorModel tipoFornecedorModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          var model = tipoFornecedorModel.ToDatabase(ContextoUsuario.UserLogged.IdOrganizacao, ContextoUsuario.UserLogged.IdFilial);
          new TipoFornecedorServices().Create(model);

          return RedirectToAction(nameof(Index));
        }
        else
        {
          return View(tipoFornecedorModel);
        }
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(tipoFornecedorModel);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(int id)
    {
      try
      {
        TipoFornecedorModel tipoFornecedorModel = new TipoFornecedorModel().FromDatabase(new TipoFornecedorServices().GetById(id));

        if (tipoFornecedorModel == null)
        {
          return NotFound();
        }

        return View(tipoFornecedorModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(TipoFornecedorModel tipoFornecedorModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          TipoFornecedorServices tipoFornecedorServices = new TipoFornecedorServices();
          E_TipoFornecedor fornecedor = tipoFornecedorServices.GetById(tipoFornecedorModel.Id);
          var model = tipoFornecedorModel.ToDatabase(fornecedor.TF_IdOrganizacao, fornecedor.TF_IdFilial);
          new TipoFornecedorServices().Edit(model);
          return RedirectToAction(nameof(Index));
        }
        return View(tipoFornecedorModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(tipoFornecedorModel);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string Delete(int id)
    {
      try
      {
        TipoFornecedorServices Service = new TipoFornecedorServices();
        var model = Service.GetById(id);
        Service.Delete(model);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }

    [HttpGet]
    public ActionResult GetOrganizacaoSelect(string term)
    {
      OrganizacaoServices Service = new OrganizacaoServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
    [HttpGet]
    public ActionResult GetFilialSelect(string term)
    {
      FilialServices Service = new FilialServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
  }
}
