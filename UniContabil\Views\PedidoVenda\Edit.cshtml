﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model PedidoVendaModel

@{
  ViewData["Title"] = "Pedido de Venda";
}

<script src="~/Views/PedidoVenda/PedidoVenda.js?ddasb=b"></script>
<link rel="stylesheet" href="~/Views/PedidoVenda/PedidoVenda.css?yy=by" />

<div class="card">
  @using (Html.BeginForm("Edit", "PedidoVenda", FormMethod.Post))
  {
    <div class="card-header">
      <div class="card-title">
        Pedido @Model.CodigoIdent
      </div>
      <div class="card-options">
      </div>
    </div>

    <div class="card-body">
      @Html.HiddenFor(a => a.Codigo)
      @Html.HiddenFor(a => a.CodigoIdent)

      <div class="col-12">
        <div class="form-group">
          @Html.Label("Cliente")
          <div style="display: flex; justify-content: space-between; align-items: flex-end;">
            <div style="width: 95%">
              @Html.Select2("ClienteSelect", "GetClienteSelect", "Selecione o Cliente", "", "", Model.ClienteSelect == null ? "" : Model.ClienteSelect.id, Model.ClienteSelect == null ? "" : Model.ClienteSelect.text)
            </div>
            <div style="width: 3%; display: flex;">
              <i class="mw-drip-search" id="DetalhesCliente"></i>
            </div>
          </div>
        </div>
      </div>

      @*<div class="form-group">
      <label class="control-label">Cliente</label>
      @Html.Select2("ClienteSelect", "GetClienteSelect", "Selecione o Cliente", "", "", Model.ClienteSelect == null ? "" : Model.ClienteSelect.id, Model.ClienteSelect == null ? "" : Model.ClienteSelect.text)
    </div>*@

      <div class="break"></div>

      <div class="form-group">
        @Html.Label("Forma Pagamento")
        @Html.Select2("FormaPagamentolSelect", "GetFormaPagamentoSelect", "Selecione a Forma de Pagamento", "", "", Model.FormaPagamentolSelect == null ? "" : Model.FormaPagamentolSelect.id, Model.FormaPagamentolSelect == null ? "" : Model.FormaPagamentolSelect.text)
      </div>

      <div class="form-group">
        @Html.Label("Condição Pagamento")
        @Html.Select2("CondicaoPagamentoSelect", "GetCondicaoPagamentoSelect", "Selecione a Condição de Pagamento", "", "", Model.CondicaoPagamentoSelect == null ? "" : Model.CondicaoPagamentoSelect.id, Model.CondicaoPagamentoSelect == null ? "" : Model.CondicaoPagamentoSelect.text)
      </div>

      <div class="form-group">
        @Html.LabelFor(a => a.TipoFrete)
        <select class="form-control" asp-for="TipoFrete" asp-items="Html.GetEnumSelectList<TipoFreteEnum>()">
          <option selected="selected" value="">Selecione</option>
        </select>
        @Html.ValidationMessageFor(a => a.TipoFrete)
      </div>

      <div class="form-group">
        @Html.LabelFor(a => a.ValorFrete)
        @Html.TextBoxFor(a => a.ValorFrete, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.ValorFrete)
      </div>

      <div class="form-group">
        @Html.LabelFor(a => a.CEPEntrega)
        @Html.TextBoxFor(a => a.CEPEntrega, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.CEPEntrega)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.EndEntrega)
        @Html.TextBoxFor(a => a.EndEntrega, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.EndEntrega)
      </div>

      <div class="form-group">
        <label asp-for="CodigoUF" class="control-label"></label>
        @Html.Select2("UFSelect", "GetUFSelect", "Selecione " + @Html.DisplayNameFor(model => model.CodigoUF), "Select2", valor: Model.UFSelect == null ? "0" : Model.UFSelect.id, text: Model.UFSelect == null ? "" : Model.UFSelect.text)
      </div>

      <div class="form-group">
        <label asp-for="CodigoMunicipio" class="control-label"></label>
        @Html.Select2("MunicipioSelect", "GetMunicipioSelect", "Selecione " + @Html.DisplayNameFor(model => model.CodigoMunicipio), "Select2", afterSelect: "UFSelect", valor: Model.MunicipioSelect == null ? "0" : Model.MunicipioSelect.id, text: Model.MunicipioSelect == null ? "" : Model.MunicipioSelect.text)
      </div>

      @*<div class="form-group">
        @Html.LabelFor(a => a.DataEmissao)
        @Html.TextBoxFor(a => a.DataEmissao, new { @class = "form-control DateTimePick" })
        @Html.ValidationMessageFor(a => a.DataEmissao)
      </div>*@

      <div class="form-group">
        @Html.LabelFor(a => a.ValorTotal)
        @Html.TextBoxFor(a => a.ValorTotal, new { @class = "form-control money" })
        @Html.ValidationMessageFor(a => a.ValorTotal)
      </div>

      <div class="form-group">
        @Html.LabelFor(a => a.CodMunicipio)
        @Html.TextBoxFor(a => a.CodMunicipio, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.CodMunicipio)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.CodTributacaoMunicipio)
        @Html.TextBoxFor(a => a.CodTributacaoMunicipio, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.CodTributacaoMunicipio)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.InscricaoMunicipal)
        @Html.TextBoxFor(a => a.InscricaoMunicipal, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.InscricaoMunicipal)
      </div>
      <div class="form-group">
        <label asp-for="TipoFaturamentoSelect" class="control-label"></label>
        @Html.Select2("TipoFaturamentoSelect", "GetTipoFaturamentoSelect", "Selecione " + @Html.DisplayNameFor(model => model.TipoFaturamentoSelect), "Select2", valor: Model.TipoFaturamentoSelect == null ? "0" : Model.TipoFaturamentoSelect.id, text: Model.TipoFaturamentoSelect == null ? "" : Model.TipoFaturamentoSelect.text)
      </div>
      <div class="form-group">
        <label asp-for="TipoNaturezaSelect" class="control-label"></label>
        @Html.Select2("TipoNaturezaSelect", "GetTipoNaturezaOperacaoSelect", "Selecione " + @Html.DisplayNameFor(model => model.TipoNaturezaSelect), "Select2", valor: Model.TipoNaturezaSelect == null ? "0" : Model.TipoNaturezaSelect.id, text: Model.TipoNaturezaSelect == null ? "" : Model.TipoNaturezaSelect.text)
      </div>
      <div class="form-group form-check">
        <label class="form-check-label">
          <input class="form-check-input" asp-for="ISSRetido" /> @Html.DisplayNameFor(model => model.ISSRetido)
        </label>
      </div>
    </div>
    <div class="card-footer">
      @if (ContextoUsuario.HasPermission("PedidoVenda", TipoFuncao.Edit))
      {
        <button type="submit" class="btn btn-success">Salvar Pedido</button>
      }

    </div>
  }
</div>

@if (ContextoUsuario.HasPermission("ItensPedidoVenda", TipoFuncao.Create)
|| ContextoUsuario.HasPermission("ItensPedidoVenda", TipoFuncao.Edit))
{
  <div class="table-group card">
    @using (Html.BeginForm("Edit", "PedidoVenda", FormMethod.Post))
    {
      <div class="card-header">
        <div class="card-title">
          Adicionar itens de Venda
        </div>
        <div class="card-options">
          @if (ContextoUsuario.HasPermission("ItensPedidoVenda", TipoFuncao.Create))
          {
            <button type="button" class="btn btn-primary" id="AdicionarItemVenda" value="0">Adicionar Item Venda</button>
          }
          @if (ContextoUsuario.HasPermission("ItensPedidoVenda", TipoFuncao.Edit))
          {
            <button type="button" style="display:none;" class="btn btn-success" id="EditarItemVenda" value="1">Editar Item Venda</button>
          }
        </div>
      </div>

      <div class="card-body" id="DivItensPedido">
        <input id="CodigoModal" name="CodigoModal" type="hidden" value="">
        <input id="CodigoOrganizacaoModal" name="CodigoOrganizacaoModal" type="hidden" value="">
        <input id="CodigoFilialModal" name="CodigoFilialModal" type="hidden" value="">
        <input id="CodPedidoCompraModal" name="CodPedidoCompraModal" type="hidden" value="">
        <input id="Descricao" name="Descricao" type="hidden" value="">


        <div class="col-md-2">
          <div class="form-group">
            <label for="SeqItem">Sequencia</label>
            <input class="form-control" data-val="true" id="SeqItem" name="SeqItem" type="text" value="" disabled="disabled">
          </div>
        </div>

        <div class="col-md-5">
          <div class="form-group">
            @Html.Label("Produto")
            <div style="display: flex; justify-content: space-between; align-items: flex-end;">
              <div style="width: 85%">
                @Html.Select2("ProdutoSelect", "GetProdutoSelect", "Selecione o Produto")
              </div>
              <div style="width: 10%; display: flex;">
                <i class="mw-drip-search" id="DetalhesProduto"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-1">
          <div class="form-group">
            <label for="Unid">Unidade</label>
            <input class="form-control number" data-val="true" id="Unid" name="Unid" type="text" value="0">
          </div>
        </div>

        <div class="col-md-1">
          <div class="form-group">
            <label for="Qtde">Quantidade</label>
            <input class="form-control number" data-val="true" id="Qtde" name="Qtde" type="text" value="0">
          </div>
        </div>

        <div class="col-md-2">
          <div class="form-group">
            <label for="ValorUnit">Valor Unitário</label>
            <input class="form-control money" data-val="true" id="ValorUnit" name="ValorUnit" type="text" value="0">
          </div>
        </div>

        <div class="col-md-2">
          <div class="form-group">
            <label for="DescontoPerc">Desconto %</label>
            <input class="form-control porcentagem" data-val="true" id="DescontoPerc" name="DescontoPerc" type="text" value="0">
          </div>
        </div>

        <div class="col-md-2">
          <div class="form-group">
            <label for="Desconto">Desconto R$</label>
            <input class="form-control money" data-val="true" id="Desconto" name="Desconto" type="text" value="0">
          </div>
        </div>

        <div class="col-md-2">
          <div class="form-group">
            <label for="ValorTotalItem money">Valor Total </label>
            <input class="form-control money" data-val="true" id="ValorTotalItem" readonly="readonly" name="ValorTotalItem" type="text" value="0">
          </div>
        </div>

        <div class="col-md-12">
          <div class="form-group">
            <label for="Observacoes">Observações</label>
            <textarea class="form-control" data-val="true" id="Observacoes" name="Observacoes" type="text" value=""> </textarea>
          </div>
        </div>

      </div>

      <div class="card-footer">
        <button type="button" class="btn btn-secondary" onclick="location.href='@Url.Action("Index", "PedidoVenda")'">Voltar</button>

        @if (ContextoUsuario.HasPermission("NotaFiscalFat", TipoFuncao.GerarNotaFiscalVenda))
        {
          <button type="button" class="btn btn-primary" id="OpenModalSeries" value="">Gerar Nota Fiscal</button>
        }

      </div>
    }
  </div>
}


@if (ContextoUsuario.HasPermission("PedidoVenda", TipoFuncao.GridItensPedidoVenda)
|| ContextoUsuario.HasPermission("ItensPedidoVenda", TipoFuncao.Create)
|| ContextoUsuario.HasPermission("ItensPedidoVenda", TipoFuncao.Edit)
|| ContextoUsuario.HasPermission("ItensPedidoVenda", TipoFuncao.Delete))
{
  <div class="table-group card">
    @using (Html.BeginForm("Edit", "PedidoVenda", FormMethod.Post))
    {
      <div class="card-header">
        <div class="card-title">
          Itens de Venda
        </div>
        <div class="card-options">

        </div>
      </div>

      <div id="DivGridItens">
        <div class="form-group">
          @await Html.PartialAsync("_GridItensPedidos", Model.ListItensPedido)
        </div>
      </div>

      <div class="card-footer">


      </div>
    }

  </div>

}


@await Html.PartialAsync("_ModalSerie", new SeriesNotasModel())
@await Html.PartialAsync("InfoAddProduto/_ModalPesquisaProduto", new ModalPesquisaProduto())
@await Html.PartialAsync("InfoAddProduto/_ModalDetalhesProduto")
@await Html.PartialAsync("InfoAddCliente/_ModalPesquisaCliente", new ModalPesquisaCliente())
@await Html.PartialAsync("InfoAddCliente/_ModalDetalhesCliente")











@*@if (ContextoUsuario.HasPermission("ItensPedidoVenda", TipoFuncao.Create)
  || ContextoUsuario.HasPermission("ItensPedidoVenda", TipoFuncao.Edit))
  {
    <div class="container" id="DivItensPedido">

      <input id="CodigoModal" name="CodigoModal" type="hidden" value="">
      <input id="CodigoOrganizacaoModal" name="CodigoOrganizacaoModal" type="hidden" value="">
      <input id="CodigoFilialModal" name="CodigoFilialModal" type="hidden" value="">
      <input id="CodPedidoCompraModal" name="CodPedidoCompraModal" type="hidden" value="">
      <input id="Descricao" name="Descricao" type="hidden" value="">*@

@*<div class="row">
  <div class="col-md-1">
    <div class="form-group">
      <label for="SeqItem">Sequencia</label>
      <input class="form-control" data-val="true" id="SeqItem" name="SeqItem" type="text" value="" disabled="disabled">
    </div>
  </div>*@

@*<div class="col-md-5">
    <div class="form-group">
      @Html.Label("Produto")
      <div style="display: flex; justify-content: space-between; align-items: flex-end;">
        <div style="width: 85%">
          @Html.Select2("ProdutoSelect", "GetProdutoSelect", "Selecione o Produto")
        </div>
        <div style="width: 10%; display: flex;">
          <i class="mw-drip-search" id="DetalhesProduto"></i>
        </div>
      </div>
    </div>
  </div>*@

@*<div class="col-md-1col-md-1">
    <div class="form-group">
      <label for="Unid">Unidade</label>
      <input class="form-control" data-val="true" id="Unid" name="Unid" type="text" value="0">
    </div>
  </div>*@

@*<div class="col-md-1">
    <div class="form-group">
      <label for="Qtde">Quantidade</label>
      <input class="form-control number" data-val="true" id="Qtde" name="Qtde" type="text" value="0">
    </div>
  </div>*@

@*<div class="col-md-2">
    <div class="form-group">
      <label for="ValorUnit">Valor Unitário</label>
      <input class="form-control money" data-val="true" id="ValorUnit" name="ValorUnit" type="text" value="0">
    </div>
  </div>*@

@*<div class="col-md-2">
      <div class="form-group">
        <label for="ValorTotalItem money">Valor Total Item</label>
        <input class="form-control money" data-val="true" id="ValorTotalItem" name="ValorTotalItem" type="text" value="0">
      </div>
    </div>
  </div>*@

@*<div class="row">
  <div class="col-md-2">
    <div class="form-group">
      <label for="DescontoPerc">Desconto %</label>
      <input class="form-control porcentagem" data-val="true" id="DescontoPerc" name="DescontoPerc" type="text" value="0">
    </div>
  </div>*@

@*<div class="col-md-2">
      <div class="form-group">
        <label for="Desconto">Desconto R$</label>
        <input class="form-control money" data-val="true" id="Desconto" name="Desconto" type="text" value="0">
      </div>
    </div>
  </div>*@

@*<div class="row">
    <div class="col-md-12">
      <div class="form-group">
        <label for="Observacoes">Observações</label>
        <textarea class="form-control" data-val="true" id="Observacoes" name="Observacoes" type="text" value=""> </textarea>
      </div>
    </div>
  </div>*@

@*<div class="row" style="justify-content: flex-end;">

        @if (ContextoUsuario.HasPermission("ItensPedidoVenda", TipoFuncao.Create))
        {
          <button type="button" class="btn btn-primary" id="AdicionarItemVenda" value="0">Adicionar Item Venda</button>
        }
        @if (ContextoUsuario.HasPermission("ItensPedidoVenda", TipoFuncao.Edit))
        {
          <button type="button" style="display:none;" class="btn btn-success" id="EditarItemVenda" value="1">Editar Item Venda</button>
        }
      </div>
    </div>
  }


  <button type="button" class="btn btn-secondary" onclick="location.href='@Url.Action("Index", "PedidoVenda")'">Voltar</button>
  @if (ContextoUsuario.HasPermission("PedidoVenda", TipoFuncao.Edit))
  {
    <button type="submit" class="btn btn-success">Salvar Pedido</button>
  }
  @if (ContextoUsuario.HasPermission("NotaFiscalFat", TipoFuncao.GerarNotaFiscalVenda))
  {
    <button type="button" class="btn btn-primary" id="OpenModalSeries" value="">Gerar Nota Fiscal</button>
  }
    }
  @await Html.PartialAsync("_ModalSerie", new SeriesNotasModel())
  @await Html.PartialAsync("InfoAddProduto/_ModalPesquisaProduto", new ModalPesquisaProduto())
  @await Html.PartialAsync("InfoAddProduto/_ModalDetalhesProduto")
  @await Html.PartialAsync("InfoAddCliente/_ModalPesquisaCliente", new ModalPesquisaCliente())
  @await Html.PartialAsync("InfoAddCliente/_ModalDetalhesCliente")*@
