﻿@model UniContabilEntidades.Models.FilialModel
@using UniContabil.Infrastructure
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Editar " + @Html.DisplayNameFor(model => model);
}

<script src="~/js/Controls-1.0.1.js?uw=ewa"></script>
<script src="~/Views/Filial/FilialEdit.js?uw=ewa"></script>
<style>
  .hide-body {
    display: none !important;
  }

  .card-children {
    box-shadow: none !important;
  }
</style>
<br />

<div class="card">
  @Html.HiddenFor(model => model.Id)
  <ul class="nav nav-tabs">
    <li class="nav-item">
      <a class="nav-link active" id="aba-1">Dados</a>
    </li>
    @if (!string.IsNullOrEmpty(Model.CPFCNPJ) && Model.CPFCNPJ.Length > 11)
    {
      <li class="nav-item">
        <a class="nav-link" id="aba-2"><PERSON><PERSON><PERSON><PERSON></a>
      </li>
    }
    @if (ContextoUsuario.UserLogged.Contabilidade == null)
    {
      <li class="nav-item">
        <a class="nav-link" id="aba-3">Secretárias</a>
      </li>
    }
  </ul>
  <div class="card" id="body-aba-1">
    <form asp-action="Edit">
      <div class="card-body">
        @Html.AntiForgeryToken()
        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
        <input type="hidden" asp-for="Id" />
        <input type="hidden" id="IdContabilidade" />
        <input type="hidden" asp-for="IdOrganizacao" />
        <input data-val="true" id="TipoPessoaSelect_id" name="TipoPessoaSelect.id" type="hidden" value="@Model.TipoPessoaSelect.id">

        <div class="col-md-12" style="margin-top: 5px; margin-bottom: 10px;">
          <div class="col-md-12" style="">
            <hr style="margin:0;">
          </div>
          <div style="position: absolute;top: -18px;left: 42%;background: white;">
            <label style="font-size: 17pt;">Dados Cadastrais</label>
          </div>
        </div>

        <div class="form-group">
          <label asp-for="Nome" class="control-label"></label>
          <input asp-for="Nome" class="form-control" />
          <span asp-validation-for="Nome" class="text-danger"></span>
        </div>
        <div class="form-group">
          @Html.LabelFor(a => a.CPFCNPJ)
          @Html.TextBoxFor(a => a.CPFCNPJ, new { @class = "form-control CPFCNPJ" })
          @Html.ValidationMessageFor(a => a.CPFCNPJ)
        </div>

        <div class="form-group">
          @Html.LabelFor(a => a.CEP)
          @Html.TextBoxFor(a => a.CEP, new { @class = "form-control CEP", id = "campoCEP" })
          @Html.ValidationMessageFor(a => a.CEP)
        </div>
        <div class="form-group">
          <label asp-for="Logradouro" class="control-label"></label>
          <input asp-for="Logradouro" class="form-control" />
          <span asp-validation-for="Logradouro" class="text-danger"></span>
        </div>
        <div class="form-group">
          @Html.LabelFor(a => a.Numero)
          @Html.TextBoxFor(a => a.Numero, new { @class = "form-control numerosOnly" })
          @Html.ValidationMessageFor(a => a.Numero)
        </div>
        <div class="form-group">
          <label asp-for="Bairro" class="control-label"></label>
          <input asp-for="Bairro" class="form-control" />
          <span asp-validation-for="Bairro" class="text-danger"></span>
        </div>
        <div class="form-group">
          <label asp-for="Complemento" class="control-label"></label>
          <input asp-for="Complemento" class="form-control" />
          <span asp-validation-for="Complemento" class="text-danger"></span>
        </div>
        <div class="form-group">
          <label asp-for="IdUF" class="control-label"></label>
          @Html.Select2("UFSelect", "GetUFSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdUF), "Select2", valor: Model.UFSelect == null ? "0" : Model.UFSelect.id, text: Model.UFSelect == null ? "" : Model.UFSelect.text)
        </div>
        <div class="form-group">
          <label asp-for="IdMunicipio" class="control-label"></label>
          @Html.Select2("MunicipioSelect", "GetMunicipioSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdMunicipio), "Select2", afterSelect: "UFSelect", valor: Model.MunicipioSelect == null ? "0" : Model.MunicipioSelect.id, text: Model.MunicipioSelect == null ? "" : Model.MunicipioSelect.text)
        </div>
        <div class="form-group">
          <label asp-for="TelefoneFixo" class="control-label"></label>
          <input asp-for="TelefoneFixo" class="form-control" />
          <span asp-validation-for="TelefoneFixo" class="text-danger"></span>
        </div>
        <div class="form-group">
          <label asp-for="Email" class="control-label"></label>
          <input asp-for="Email" class="form-control" />
          <span asp-validation-for="Email" class="text-danger"></span>
        </div>
        <div class="form-group">
          <label asp-for="TelefoneCelular" class="control-label"></label>
          <input asp-for="TelefoneCelular" class="form-control" />
          <span asp-validation-for="TelefoneCelular" class="text-danger"></span>
        </div>

        @*<div class="form-group col-md-2" style=" margin-top: 7px;">
            <label class="form-check-label">
              <input class="form-check-input" style="margin-top:-3px;" asp-for="TelWhatsApp" /> @Html.DisplayNameFor(model => model.TelWhatsApp)
            </label>
          </div>
          <div class="form-group col-md-2" style=" margin-top: 7px;">
            <label class="form-check-label">
              <input class="form-check-input" asp-for="Ativo" style=" margin-top: -3px;" /> @Html.DisplayNameFor(model => model.Ativo)
            </label>
          </div>*@

        <!-- DADOS FISCAIS -->

        <div class="col-md-12" style="margin-top: 32px; margin-bottom: 10px;">
          <div class="col-md-12">
            <hr style="margin:0;">
          </div>
          <div style="position: absolute;top: -18px;left: 42%;background: white;">
            <label style="font-size: 17pt;">Dados Fiscais </label>
          </div>
        </div>

        <div class="form-group col-md-2">
          <label asp-for="InscricaoEstadual" class="control-label"></label>
          <input asp-for="InscricaoEstadual" class="form-control" />
          <span asp-validation-for="InscricaoEstadual" class="text-danger"></span>
        </div>

        <div class="form-group col-md-2">
          <label asp-for="InscricaoMunicipal" class="control-label"></label>
          <input asp-for="InscricaoMunicipal" class="form-control" />
          <span asp-validation-for="InscricaoMunicipal" class="text-danger"></span>
        </div>

        <div class="form-group col-md-2 form-check" style="margin-top: 8px; margin-left: -4px; ">
          <label class="form-check-label">
            <input class="form-check-input" asp-for="IncentivadorCulturalFloat" style=" margin-top: -3px;" /> Incentivador cultural
          </label>
        </div>

        <div class="form-group col-md-2">
          <label asp-for="CodigoTributacaoMunicipio" class="control-label"></label>
          <input asp-for="CodigoTributacaoMunicipio" class="form-control" />
          <span asp-validation-for="CodigoTributacaoMunicipio" class="text-danger"></span>
        </div>

        <div class="form-group col-md-3">
          <label asp-for="IdRegimeEspecialTributacao" class="control-label"></label>
          @Html.Select2("RegimeEspecialTributacaoSelect", "GetRegimeEspecialTributacaoSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdRegimeEspecialTributacao), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.RegimeEspecialTributacaoSelect == null ? "" : Model.RegimeEspecialTributacaoSelect.id, Model.RegimeEspecialTributacaoSelect == null ? "" : Model.RegimeEspecialTributacaoSelect.text)
        </div>
      </div>

      <div class="card-body">

        <div class="col-md-12" style="margin-top: 15px; margin-bottom: 10px;">
          <div class="col-md-12" style="">
            <hr style="margin:0;">
          </div>
          <div style="position: absolute;top: -18px;left: 42%;background: white;">
            <label style="font-size: 17pt;">Contabilidade</label>
          </div>
        </div>

        <input type="hidden" name="IdContabilidade" id="IdContabilidade" />
        <div class="col-md-12">
          <div class="row" style="align-items: flex-end;">
            <div class="col-md-7">
              <div class="form-group">
                <label>Nome/Razão Sócial Contabilidade</label>
                <input class="form-control" readonly="readonly" id="Contabilidade_text" value="@(Model == null ? "" : Model.ContabilidadeSelect == null ? "" : Model.ContabilidadeSelect.text)" />
              </div>
            </div>
            @if (ContextoUsuario.UserLogged.Contabilidade == null)
            {
              <div class="col-md-5">
                <div class="form-group">
                  <button type="button" class="btn btn-sm btn-info" id="editContabilidade">Associar Contabilidade</button>
                </div>
              </div>
            }
          </div>
        </div>
      </div>

      <div class="card-footer">
        <a asp-action="Index" class="link link-info">Voltar</a>
        <input type="submit" value="Salvar" class="btn btn-primary" />
      </div>
    </form>
  </div>

  <div class="card card-children hide-body" id="body-aba-2">
    @await Html.PartialAsync("_GridControlePermissao", new List<OrganizacaoUsuarioDetails>())
  </div>

  <div class="card card-children hide-body" id="body-aba-3">
    @await Html.PartialAsync("_GridSecretaria", new List<OrganizacaoUsuarioDetails>())
  </div>
</div>

@Html.Partial("Modal")
@Html.Partial("_ModalEmailContab")


@if (ContextoUsuario.UserLogged.Contabilidade != null)
{
  <script>
    $(".card #TipoPessoaSelectLookup, .card #UFSelectLookup, .card #MunicipioSelectLookup").attr("disabled", "disabled");
    $(".card input").attr("disabled", "disabled");
    $(".card #InscricaoEstadual, #IncentivadorCulturalFloat, #CodigoTributacaoMunicipio, #InscricaoMunicipal").removeAttr("disabled");
  </script>
}