﻿@model UniContabilEntidades.Models.TipoClienteModel
@using UniContabil.Infrastructure.Controls

    @{
    ViewData["Title"] = "Editar " + @Html.DisplayNameFor(model => model);
    }
  <div class="card">
    <form asp-action="Edit">
      <div class="card-header">
        <div class="card-title">
        </div>
        <div class="card-options">
        </div>
      </div>
      <div class="card-body">
        @Html.AntiForgeryToken()
        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
             
              <div class="form-group">
                <label asp-for="Descricao" class="control-label"></label>
                <input asp-for="Descricao" class="form-control" />
                <span asp-validation-for="Descricao" class="text-danger"></span>
              </div>
              <div class="form-group">
                @Html.LabelFor(a => a.Enum)
                @Html.TextBoxFor(a => a.Enum, new { @class = "form-control numerosOnly" })
                @Html.ValidationMessageFor(a => a.Enum)
              </div>
              <div class="form-group">
                @Html.LabelFor(a => a.Ordem)
                @Html.TextBoxFor(a => a.Ordem, new { @class = "form-control numerosOnly" })
                @Html.ValidationMessageFor(a => a.Ordem)
              </div>
      </div>
      <div class="card-footer">
        <a asp-action="Index" class="link link-info">Voltar</a>
        <input type="submit" value="Salvar" class="btn btn-primary" />
      </div>
    </form>
  </div>
