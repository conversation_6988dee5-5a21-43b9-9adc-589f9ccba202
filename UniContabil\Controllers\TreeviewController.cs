﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;

namespace UniContabil.Controllers
{
  public class TreeviewController : LibController
  {
    public TreeviewController()
    { }

    public ActionResult Index()
    {
      try
      {
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    public JsonResult Bloqtree(TreeViewModelDB treev)
    {
      try
      {
        new TreeviewService(ContextoUsuario.UserLogged).Create(treev);

        return Json(new { Erro = false, Message = "Sucesso." });
      }
      catch (Exception ex)
      {
        return Json(new { Erro = true, Message = ex.Message });
      }
    }

    public JsonResult Desbloqtree(TreeViewModelDB treev)
    {
      try
      {
        new TreeviewService(ContextoUsuario.UserLogged).Delete(treev);

        return Json(new { Erro = false, Message = "Sucesso." });
      }
      catch (Exception ex)
      {
        return Json(new { Erro = true, Message = ex.Message });
      }
    }

    public JsonResult _GetTreeview(int Tipo)
    {
      List<TreeviewClassificacaoModel> Treeview = new List<TreeviewClassificacaoModel>();

      Treeview = new TreeviewService(ContextoUsuario.UserLogged).GetTreeviewClassificacaoConfig(Tipo);
      JsonResult json = new JsonResult(Treeview);

      return json;
    }

  }
}
