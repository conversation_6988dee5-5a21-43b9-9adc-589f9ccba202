﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using MWHelper.Exceptions;

using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniContabilDomain.Services;
using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;

namespace UniContabil.Controllers
{
  public class MovimentacaoRecorrenteController : LibController
  {
    public MovimentacaoRecorrenteController(IConfiguration configRoot)
    {
      _Service = new MovimentacoesRecorrentesServices(ContextoUsuario.UserLogged);
      _configuration = configRoot;
    }
    private MovimentacoesRecorrentesServices _Service;
    private IConfiguration _configuration;

    public IActionResult Index(string Search)
    {
      ViewBag.Search = Search ?? "";
      List<E_MovimentacaoRecorrente> movimentacao = _Service.GetAll(Search);
      return View(movimentacao);
    }

    public IActionResult Create()
    {

      return View(new MovimentacoesReconrrentesCreateModel() { CPFCNPJ = "" });
    }

    //[HttpPost]
    //[NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    //public string Delete(Guid id)
    //{
    //  try
    //  {
    //    MovimentacoesRecorrentesServices Service = new MovimentacoesRecorrentesServices();
    //    var model = Service.GetById(id);
    //    Service.Delete(model);
    //    return JsonConvert.SerializeObject(new { Sucesso = true });
    //  }
    //  catch (Exception Ex)
    //  {
    //    return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
    //  }
    //}

    [HttpPost]
    public JsonResult Delete(Guid id)
    {
      try
      {
        _Service.DeleteMovimentacao(id);
        return Json(new { error = false, message = "Deletado com sucesso!" });
      }
      catch (Exception ex)
      {
        return Json(new { error = true, message = "Erro inesperado, tente novamente mais tarde!" });
      }
    }

    public IActionResult Edit(Guid id)
    {
      E_MovimentacaoRecorrente model = _Service.GetById(id);
      string classificacao = "";


      if (model.MR_IdClassificacao.HasValue)
      {
        classificacao = new ClassificacaoDocumentosServices().GetById(model.MR_IdClassificacao.Value).CD_Descricao;
      }

      return View(new MovimentacoesReconrrentesEditModel()
      {
        CPFCNPJ = model.MR_CPFCNPJ,
        DataInicial = model.MR_DataInicial,
        DataNascimento = model.MR_DataNascimento,
        Descricao = model.MR_Descricao,
        Id = model.MR_Id,
        Nome = model.MR_Nome,
        NrOcorrencias = model.MR_NroOcorrencias,
        TipoRecorrencia = model.MR_TipoRecorrencia,
        Valor = model.MR_Valor,
        FileName = model.MR_NomeAnexo,
        HasRemovedAnexo = false,
        IdClassificacao = model.MR_IdClassificacao,
        Classificacao = classificacao
      });
    }

    public PartialViewResult GetPartialEditItem()
    {
      return PartialView("ModalAlteraValor");
    }

    [HttpPost]
    public JsonResult AlterarItens(AlteraValorLancamento model)
    {
      try
      {
        _Service.AlterLancamento(model);
        return Json(new { erro = false, message = "Os itens foram alterados!" });
      }
      catch (CustomException ex)
      {
        return Json(new { erro = true, message = ex.Message });
      }
      catch (Exception ex)
      {
        return Json(new { erro = true, message = "Erro desconhecido!" });
      }
    }

    public JsonResult Gerar(Guid IdMov)
    {
      try
      {
        _Service.Gerar(IdMov, _configuration["CaminhoDocs"]);
        return Json(new { erro = false, message = "Os itens foram gerados!" });

      }
      catch (CustomException ex)
      {
        return Json(new { erro = true, message = ex.Message });
      }
      catch (Exception ex)
      {
        return Json(new { erro = true, message = "Erro desconhecido!" });
      }
    }

    public PartialViewResult _GetPartialItens(FiltroLancamento filtro)
    {
      List<IndexLancamentos> model = _Service.GetAllLancamentos(filtro);
      return PartialView("_GridLancamentos", model);
    }

    [HttpPost]
    public JsonResult DeleteLancamento(DeleteLancamentosModel model)
    {
      try
      {
        _Service.DeleteLancamento(model);
        return Json(new { error = false, message = "Deletado com sucesso!" });
      }
      catch (Exception ex)
      {
        return Json(new { error = true, message = "Aconteceu algum erro desconhecido!" });
      }
    }

    [HttpPost]
    public IActionResult Edit(MovimentacoesReconrrentesEditModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          if (string.IsNullOrEmpty(model.CPFCNPJ))
          {
            MessageAdd(new Message(MessageType.Error, "O CPF/CNPJ não pode ser nulo. Favor preencher o campo."));
            return View(model);
          }
          if (model.NrOcorrencias < 1)
          {
            MessageAdd(new Message(MessageType.Error, "O numero minimo de ocorrencia é 1"));
            return View(model);
          }
          if (model.CPFCNPJ.Length == 11)
          {
            ValidadorHelper.ValidaCPF(model.CPFCNPJ);

            if (model.Nome == null || model.DataNascimento == null)
            {
              MessageAdd(new Message(MessageType.Error, "Você deve preencher as informações pessoais."));
              return View(model);
            }
          }
          else
          {
            ValidadorHelper.ValidaCNPJ(model.CPFCNPJ);
          }

          model.path = _configuration["CaminhoDocs"];
          _Service.Edit(model);

          MessageAdd(new Message(MessageType.Success, "As alterações salvas!"));
          return RedirectToAction(nameof(Edit), new { id = model.Id });
        }
        MessageAdd(new Message(MessageType.Error, "Verifique os campos!"));
        return View(model);
      }
      catch (CustomException ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, "Ocorreu um erro desconhecido, tente novamente mais tarde!"));
        return View(model);
      }

    }

    [HttpPost]
    public IActionResult Create(MovimentacoesReconrrentesCreateModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          if (string.IsNullOrEmpty(model.CPFCNPJ))
          {
            MessageAdd(new Message(MessageType.Error, "O CPF/CNPJ não pode ser nulo. Favor preencher o campo."));
            return View(model);
          }
          if (model.CPFCNPJ.Length == 11)
          {
            ValidadorHelper.ValidaCPF(model.CPFCNPJ);

            if (model.Nome == null || model.DataNascimento == null)
            {
              MessageAdd(new Message(MessageType.Error, "Você deve preencher as informações pessoais."));
              return View(model);
            }
          }
          else
          {
            ValidadorHelper.ValidaCNPJ(model.CPFCNPJ);
          }

          model.path = _configuration["CaminhoDocs"];
          Guid idCreate = _Service.Create(model);

          MessageAdd(new Message(MessageType.Success, "Foi criado com sucesso!"));
          return RedirectToAction(nameof(Edit), new { id = idCreate });
        }
        MessageAdd(new Message(MessageType.Error, "Verifique os campos!"));
        return View(model);
      }
      catch (CustomException ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, "Ocorreu um erro desconhecido, tente novamente mais tarde!"));
        return View(model);
      }

    }
  }
}
