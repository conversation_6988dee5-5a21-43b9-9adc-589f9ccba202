﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;

namespace UniContabil.Controllers
{
  public class OrganizacaoController : LibController
  {
    public OrganizacaoController()
    { }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        IPagedList<OrganizacaoModel> List = new OrganizacaoServices(ContextoUsuario.UserLogged).GetPagedList(page);
        ViewBag.PageItems = List;
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(OrganizacaoModel organizacaoModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          var model = organizacaoModel.ToDatabase();
          new OrganizacaoServices().Create(model);

          return RedirectToAction(nameof(Index));
        }
        else
        {
          return View(organizacaoModel);
        }
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(organizacaoModel);
      }
    }


    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(int id)
    {
      try
      {
        OrganizacaoModel organizacaoModel = new OrganizacaoModel().FromDatabase(new OrganizacaoServices().GetById(id));

        if (organizacaoModel == null)
        {
          return NotFound();
        }

        return View(organizacaoModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(OrganizacaoModel organizacaoModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          var model = organizacaoModel.ToDatabase();
          new OrganizacaoServices().Edit(model);
          return RedirectToAction(nameof(Index));
        }
        return View(organizacaoModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(organizacaoModel);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string Delete(int id)
    {
      try
      {
        OrganizacaoServices Service = new OrganizacaoServices();
        var model = Service.GetById(id);
        Service.Delete(model);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }

    public PartialViewResult GetModalOrg()
    {
      try
      {
        OrganizacaoModal org = new OrganizacaoModal();

        if (ContextoUsuario.UserLogged.IdOrganizacao > 0)
          org.IdOrganizacao = ContextoUsuario.UserLogged.IdOrganizacao;

        if(ContextoUsuario.UserLogged.IdFilial.HasValue)
          org.IdFilial = ContextoUsuario.UserLogged.IdFilial.Value;

        return PartialView("_PartialModalOrg", org);
      }
      catch (Exception)
      {
        throw;
      }
    }

  }
}
