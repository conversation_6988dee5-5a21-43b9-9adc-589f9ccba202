﻿<div class="modal fade" id="ResetPassword" tabindex="-1" role="dialog" aria-labelledby="ResetPasswordLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ResetPasswordLabel">Alterar <PERSON></h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" style="padding-top: 0px;">
        <div class="row">
          <div class="card-body">
            @using (Html.BeginForm("ResetPassword", "Perfil", FormMethod.Post, new { enctype = "multipart/form-data", id = "formResetPassword" }))
            {
              <div class="card-body" style="padding-top: 0px;">

                <div class="col-md-12">
                  <div class="form-group">
                    <label for="NowPass">Senha Atual</label>
                    <input class="form-control" id="NowPass" name="NowPass" type="password">
                    <span class="field-validation-valid" data-valmsg-for="NowPass" data-valmsg-replace="true"></span>
                  </div>
                </div>

                <div class="col-md-12">
                  <div class="form-group">
                    <label for="NewPass">Nova Senha</label>
                    <input class="form-control" id="NewPass" name="NewPass" type="password">
                    <span class="field-validation-valid" data-valmsg-for="NewPass" data-valmsg-replace="true"></span>
                  </div>
                </div>

                <div class="col-md-12">
                  <div class="form-group">
                    <label for="ConfirmPass">Confirmar Senha</label>
                    <input class="form-control" id="ConfirmPass" name="ConfirmPass" type="password">
                    <span class="field-validation-valid" data-valmsg-for="ConfirmPass" data-valmsg-replace="true"></span>
                  </div>
                </div>

              </div>
              <div class="card-footer">
                <button type="button" class="btn btn-primary" onclick="Shared.ResetPassword()">Salvar</button>
              </div>
            }

          </div>
        </div>
      </div>
    </div>
  </div>
</div>