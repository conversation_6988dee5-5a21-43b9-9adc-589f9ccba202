﻿function reloadAba(aba) {

  let name = "";

  if (!aba)
    aba = "aba-users"

  name = aba;
  if (aba == "aba-users") {
    $("#aba-organizacoes").removeClass("active");
    $("#body-aba-organizacoes").addClass("hide-body");

    $("#aba-users").addClass("active");
    $("#body-aba-users").removeClass("hide-body");

  } else {
    $("#aba-users").removeClass("active");
    $("#body-aba-users").addClass("hide-body");

    $("#aba-organizacoes").addClass("active");
    $("#body-aba-organizacoes").removeClass("hide-body");
  }
}

$(document).ready(function () {
  reloadAba(null);

  $("#aba-users").click(function () {
    $(".organizacoes").html("");
    $("#body-tipo-grupo-filial").html("");
    reloadAba("aba-users");
  });

  $("#aba-organizacoes").click(function () {
    $(".spinner-table").attr("style", "display: flex !important; margin-top: 5px;margin-bottom: 5px;");
    reloadAba("aba-organizacoes");
    loadAbaOrganizacoes();
  });

  $(".select-all").change(function () {
    alert('checked');
    //if (this.checked) {
    //  $(".reply input").prop("checked", true);
    //}
    //else {
    //  $(".reply input").prop("checked", false);
    //}
  });

  $('#selectAll').on('ifChecked', function (event) {
    for (var i = 0; i < $('#filiais-table tbody tr').length; i++) {
      $("#selectfilial_" + i).iCheck("check");
    }
  });

  $('#selectAll').on('ifUnchecked', function (event) {
    for (var i = 0; i < $('#filiais-table tbody tr').length; i++) {
      $("#selectfilial_" + i).iCheck("uncheck");
    }
  });

});

function loadFiltroFiliais() {
  const cod = $("#Codigo").val();
  const search = $("#Search").val();
  //if (search) {
    $(".body-filiais-content").html("");
    $(".spinner-table").attr("style", "display: flex !important; margin-top: 5px;margin-bottom: 5px;");
    $.ajax({
      type: 'GET',
      url: GetURLBaseComplete() + '/Usuarios/GetFiliais',
      dataType: 'html',
      data: {
        id: cod,
        filtro: search,
      },
      success: function (data) {
        $(".spinner-table").attr("style", "display: none !important");
        $(".body-filiais-content").html(data);
        //$(".spinner-menu").attr("style", "display: none !important");
      }
    });
  //}
  //else
  //  Alerta("Atenção", "Gentileza preencher o campo pesquisa", "yellow", 6000);

}

function loadAbaOrganizacoes() {
  const cod = $("#Codigo").val();
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Usuarios/GetControlePermissao',
    dataType: 'html',
    data: {
      id: cod
    },
    success: function (data) {
      $("#body-aba-organizacoes").html(data);
      $(".spinner-table").attr("style", "display: none !important");
    }
  });
}