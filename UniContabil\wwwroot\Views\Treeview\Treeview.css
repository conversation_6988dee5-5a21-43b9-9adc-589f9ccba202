﻿
.classif {
  width: 50%;
  color: #3a7492;
}

  .classif :hover {
    background-color: #3a7492;
    color: #d1ebff;
  }

.selecionado {
  background-color: #3a7492;
  color: #d1ebff !important;
}

.Docs {
  margin-top: 10px;
  height: 60vh;
  background-color: #f4f4f4;
  width: 100%;
  border-radius: 10px;
}

.divTable {
  
}

#resizebleDiv {
  border-top: 1px #d1ebff solid;
  overflow: auto;
  min-height: 12vh;
  min-width: 100%;
  max-width: 100%;
  height: 12vh;
  margin-bottom: 10px !important;
}

hr {
  background-color: #597384;
  height: 2px;
}

h5 {
  margin: 0;
  border-bottom: 1px #d1ebff solid;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: #e3e3e3 !important;
}

.table-hover tbody tr:hover {
  background-color: #97989a !important;
  color: white;
}


.table-bordered {
  border: 2px solid #3a7492 !important;
}

  .table-bordered th, .table-bordered td {
    border: 2px solid #3a7492 !important;
    border-bottom: none !important;
    border-top: none !important;
  }

p {
  margin: 0 !important;
}

th, td {
  padding: 0 !important;
}

hr {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.jstree-default .jstree-anchor {
  font-size: 10pt !important;
}
