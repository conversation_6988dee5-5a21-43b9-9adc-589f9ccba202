﻿@using UniContabil.Infrastructure.Controls
@model UniContabilEntidades.Models.SeriesNotasModel
<!-- Modal -->
  <div class="modal fade" id="_ModalSerie" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Gerar Nota Fiscal</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <input id="TipoPedido" name="TipoPedido" type="hidden" value="">
          <input id="CodigoPedido" name="CodigoPedido" type="hidden" value="">
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                @Html.Label("Serie")
                @Html.Select2("SeriesNotasSelect", "GetSeriesSelect", "Selecione a Serie", "", "", Model.SeriesNotasSelect == null ? "" : Model.SeriesNotasSelect.id, Model.SeriesNotasSelect == null ? "" : Model.SeriesNotasSelect.text)
              </div>
            </div>

          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
            <button type="button" class="btn btn-success" id="BtnGeraNota" data-criar="1">Gerar</button>
          </div>
        </div>
      </div>
    </div>
  </div>

