﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;


namespace UniContabil.Controllers
{
  public class ModuloController : LibController
  {
    public ModuloController()
    { }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        IPagedList<ModuloModel> List = new ModuloServices().GetPagedList(page);
        ViewBag.PageItems = List;
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(ModuloModel moduloModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          var model = moduloModel.ToDatabase();
          new ModuloServices().Create(model);

          return RedirectToAction(nameof(Index));
        }
        else
        {
          return View(moduloModel);
        }
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(moduloModel);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(int id)
    {
      try
      {
        ModuloModel moduloModel = new ModuloModel().FromDatabase(new ModuloServices().GetById(id));

        if (moduloModel == null)
        {
          return NotFound();
        }

        return View(moduloModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(ModuloModel moduloModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          var model = moduloModel.ToDatabase();
          new ModuloServices().Edit(model);
          return RedirectToAction(nameof(Index));
        }
        return View(moduloModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(moduloModel);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string Delete(int id)
    {
      try
      {
        ModuloServices Service = new ModuloServices();
        var model = Service.GetById(id);

        if (model != null)
        {
          Service.RemoverReferencias(id);
          Service.Delete(model);
        }

        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }

  }
}
