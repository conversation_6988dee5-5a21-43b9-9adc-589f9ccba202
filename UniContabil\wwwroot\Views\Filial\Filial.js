﻿var Filial = function () { };


Filial.init = function () {
  $(document).on('click', '#addContab', function () {
    $("#modalContabilidade").modal('show');
  });

  $(document).on('click', '#save', function () {
    var idcontab = $("#ContabilidadeSelect_id").val();
    var contabtext = $("#ContabilidadeSelect_text").val();

    var continput = $("#Contabilidade_text").val();

    if (continput) {
      swal({
        title: "Adicionar Contabilidade",
        text: "Ao alterar a contabilidade, você removerá todas as permissões da contabilidade atual e dará permissão aos usuários de contabilidade selecionada.",
        icon: "warning",
        buttons: ["Cancelar", "Confirmar"],
      })
        .then((continuar) => {
          if (continuar) {
            $("#IdContabilidade").val(idcontab);
            $("#Contabilidade_text").val(contabtext);
            swal({
              title: "Sucesso",
              text: "Vinculado com Sucesso.",
              icon: "success",
            });
            $("#modalContabilidade").modal('hide');
          }
        });
    }
    else {
      $("#IdContabilidade").val(idcontab);
      $("#Contabilidade_text").val(contabtext);
      swal({
        title: "Sucesso",
        text: "Vinculado com Sucesso.",
        icon: "success",
      });
      $("#modalContabilidade").modal('hide');
    }
  });

  $('#campoCEP').on('keyup', function () {
    console.log('Digitado')
    completaCampos();
  });

  $(document).on("click", "#enviaEmailContab", function () {
    $("#NomeContab").val("");
    $("#EmailContab").val("");
    $("#CPFCNPJContab").val("");

    $("#ModalEmailContab").modal('show');
  });

  $(document).on("click", "#sendEmail", function () {

    let cpfcnpj = $("#CPFCNPJContab").val();

    if (cpfcnpj) {
      cpfcnpj = cpfcnpj.replaceAll(".", "");
      cpfcnpj = cpfcnpj.replaceAll("-", "");
      cpfcnpj = cpfcnpj.replaceAll("/", "");
    }

    var Model = {
      Nome: $("#NomeContab").val(),
      Email: $("#EmailContab").val(),
      CPFCNPJ: cpfcnpj,
    }

    $.ajax({
      url: GetURLBaseComplete() + "/Filial/SendEmailContab/",
      type: 'POST',
      data: Model,
      dataType: 'json',
      success: function (data) {
        if (!data.erro) {
          swal({
            title: "Sucesso",
            text: data.message,
            icon: "success",
          });
          $("#ModalEmailContab").modal('hide');
        }
        else {
          swal({
            title: "Erro",
            text: data.message,
            icon: "error",
          });
        }
      }
    })

  });
};

$(document).ready(function () {

  completaCampos();
  console.log('ready')
  Filial.init();
}
);

function completaCampos() {
  var val = $('#campoCEP').inputmask('unmaskedvalue');

  if (val.length == 8) {
    $.ajax({
      url: 'https://viacep.com.br/ws/' + val + '/json',
      type: 'GET',
      dataType: 'json',
      success: function (data) {
        if (data.erro) {

        } else {
          $('#Logradouro').val(data['logradouro']);
          $('#Complemento').val(data['complemento']);
          $('#Bairro').val(data['bairro']);
          $.ajax({
            type: 'GET',
            url: GetURLBaseComplete() + '/select2/GetUFSelect?term=' + data['uf'],
            success: function (sucessData) {
              console.log(sucessData)
              $("#select2-UFSelectLookup-container").attr('title', sucessData['items'][0]['text']);
              $("#UFSelect_id").val(sucessData['items'][0]['id']);
              $("#UFSelect_text").val(sucessData['items'][0]['text']);
              $('#select2-UFSelectLookup-container').html('');
              $('#select2-UFSelectLookup-container').append(sucessData['items'][0]['text']);
              $.ajax({
                type: 'GET',
                url: GetURLBaseComplete() + '/select2/GetMunicipioSelect?term=' + data['localidade'] + "&id=" + sucessData['items'][0]['id'],
                success: function (sucessDataCidade) {
                  console.log(sucessDataCidade)
                  $("#select2-MunicipioSelect-container").attr('title', sucessDataCidade['items'][0]['text']);
                  $("#MunicipioSelect_id").val(sucessDataCidade['items'][0]['id']);
                  $("#MunicipioSelect_text").val(sucessDataCidade['items'][0]['text']);
                  $('#select2-MunicipioSelectLookup-container').html('');
                  $('#select2-MunicipioSelectLookup-container').append(sucessDataCidade['items'][0]['text']);
                }
              })
            }
          })
        }
      }
    })
  }
}