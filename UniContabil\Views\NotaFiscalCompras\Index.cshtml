﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure.Controls
@model List<NotaFiscalCompIndexModel>
@{
  ViewBag.Title = "Notas Fiscais de Compra";
}

<div class="table-group card">
  <div class="card-header" id="CabecalhoPedidoCompra">
    <div class="card-title">Notas</div>
    <div class="card-options">
      @*@if (ContextoUsuario.HasPermission("PedidoCompra", TipoFuncao.Create))
      {
        <button type="button" class="btn btn-success" onclick="location.href='@Url.Action("Create", "NotaFiscalFat")'">Adicionar Nota</button>
      }*@
    </div>
  </div>
  <div class="table-group-content">
    <table>
      <thead>
        <tr>
          <th>
            Código
          </th>
          <th>
            Organizacao
          </th>
          <th>
            Fornecedor
          </th>
          <th>
            Serie
          </th>
          <th>
            Valor Total
          <th>
            Desconto Total
          </th>
          <th>
            Data de Emissão
          </th>
          <th>
          </th>
        </tr>
      </thead>
      <tbody>
        @foreach (NotaFiscalCompIndexModel NotaFiscal in Model)
        {
          <tr>
            <td>@NotaFiscal.CodigoIdent</td>
            <td>@NotaFiscal.Organizacao</td>
            <td>
              @if (!string.IsNullOrEmpty(NotaFiscal.Fornecedor))
              {
                <text> NotaFiscal.Fornecedor </text>
              }
            </td>
            <td>@NotaFiscal.Serie </td>
            <td>@NotaFiscal.ValorTotal </td>
            <td>@NotaFiscal.DescontoTotal</td>
            <td>@NotaFiscal.DtEmissao.ToString("dd/MM/yyyy")</td>
            <td style="width:100px">
              <a href="@Url.Action("Edit", "NotaFiscalCompras", new { id = @NotaFiscal.Codigo})" class="link link-primary" title="Editar">
                Editar
              </a>
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
  <div class="card-footer">
  </div>
</div>