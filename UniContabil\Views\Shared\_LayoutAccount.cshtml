﻿<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>@ViewData["Title"] - UniContabil</title>
  <link rel="stylesheet" href="~/Content/MWFont.css" />
  <link rel="stylesheet" href="~/Content/Site-1.1.0.css" />
  <link rel="stylesheet" href="~/Content/select2.min.css" />
  <link rel="stylesheet" href="~/Content/iziToast.min.css" />
  <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
  <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap-grid.min.css" />
  <link href="~/Content/font-awesome.min.css" rel="stylesheet" />

  <script src="~/lib/jquery/dist/jquery.min.js"></script>
  <script src="~/js/Site-1.1.0.js"></script>
  <script src="~/js/jquery-ui.min.js"></script>
  <script src="~/lib/bootstrap/dist/js/bootstrap.js"></script>
  <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="~/js/select2.min.js"></script>
  <script src="~/js/Select2Controls-1.1.0.js"></script>
  <script src="~/js/iziToast.min.js"></script>
  <script src="~/js/SweetAlert/SweetAlert.js"></script>
  <script src="~/Scripts/inputmask/inputmask.js"></script>
  <script src="~/Scripts/inputmask/jquery.inputmask.js"></script>
  <script src="~/Scripts/inputmask/inputmask.extensions.js"></script>
  <script src="~/Scripts/inputmask/inputmask.date.extensions.js"></script>
  <script src="~/Scripts/inputmask/inputmask.numeric.extensions.js"></script>

</head>
<body>
  @RenderBody()
  @using UniContabil.Infrastructure.Controls
  @Html.LibMessageBoxAlert()
  @RenderSection("Scripts", required: false)
</body>
</html>
