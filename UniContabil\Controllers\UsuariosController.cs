﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using UniContabil.Infrastructure.Exceptions;

using UniContabilEntidades.Models;
using UniContabilDomain.Services;
using UniContabilEntidades.Models.DataBase;
using X.PagedList;
using Estoque.Infrastructure;

namespace UniContabil.Controllers
{
  [Authorize]
  public class UsuariosController : LibController
  {
    private UsuariosServices UsuariosServices
    {
      get
      {
        if (_UsuariosServices == null)
          _UsuariosServices = new UsuariosServices(ContextoUsuario.UserLogged);

        return _UsuariosServices;
      }
    }
    private UsuariosServices _UsuariosServices;

    private ControlePermissaoUserServices ControlePermissaoUserServices
    {
      get
      {
        if (_ControlePermissaoUserServices == null)
          _ControlePermissaoUserServices = new ControlePermissaoUserServices(ContextoUsuario.UserLogged);

        return _ControlePermissaoUserServices;
      }
    }
    private ControlePermissaoUserServices _ControlePermissaoUserServices;

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int? page)
    {
      int pageNumber = page ?? 1;
      IPagedList<UsuarioIndex> ListaUsuariosModelIndex = UsuariosServices.GetAll(pageNumber);
      return View(ListaUsuariosModelIndex);
    }

    public ActionResult Create()
    {
      try
      {
        BasicRegisterModel model = new BasicRegisterModel();
        model.TipoCadastro = (int)EnumTipoCadastro.Unicooper;
        return View(model);
      }
      catch (Exception ex)
      {
        throw;
      }
    }

    [HttpPost]
    public ActionResult Create(BasicRegisterModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          AccountServices accountServices = new AccountServices();
          accountServices.RegistrarUsuario(model);
          return RedirectToAction("Index");
        }
        MessageAdd(new Message(MessageType.Warning, "Verifique os campos preenchidos."));
        return View(model);
      }
      catch (CustomException ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        TimeOutMessage = "120000";
        return View(model);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.ToString()));
        TimeOutMessage = "120000";
        return View(model);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(Guid coduser)
    {
      UsuarioBasico UsuariosModel = UsuariosServices.GetUsuarioBasicoById(coduser);
      return View(UsuariosModel);
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(UsuarioBasico UsuariosModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          UsuariosServices.Edit(UsuariosModel);
          MessageAdd(new Message(MessageType.Success, "Dados atualizados com sucesso."));
          return View(UsuariosModel);
          //return RedirectToAction("Index", "Usuarios");
        }

        List<string> ListaErro = ModelStateHelper.GetErroList(ViewData.ModelState.Values);
        MessageAdd(new Message(MessageType.Error, String.Join(" <br> ", ListaErro.ToArray())));
        return View(UsuariosModel);
      }
      catch (CustomException ex)
      {
        MessageAdd(new Message(MessageType.Success, ex.Message));
        return View(UsuariosModel);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Success, ex.Message));
        return View(UsuariosModel);
      }
    }

    [HttpGet]
    public PartialViewResult GetControlePermissao(Guid id)
    {
      try
      {
        ControlePermissaoUserModel controle = ControlePermissaoUserServices.GetByUser(id);
        return PartialView("_GridControlePermissao", controle);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpGet]
    public PartialViewResult GetFiliais(Guid id, string filtro)
    {
      try
      {
        List<FilialPartial> lista = ControlePermissaoUserServices.FiltroFiliaisByUser(id, filtro);

        return PartialView("_GridFiliais", lista);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpGet]
    public JsonResult SaveVinculo(Guid id, int? codfilial, int codorg)
    {
      try
      {
        E_OrganizacaoUsuario org = new OrganizacaoUsuarioServices().GetBy(id, codorg, codfilial);

        if (org == null)
        {
          org = new E_OrganizacaoUsuario()
          {
            IdUsuario = id,
            IdOrganizacao = codorg,
            IdFilial = codfilial
          };

          new OrganizacaoServices().Create(org);
          return new JsonResult(new { Error = false, Mensage = "Salvo com Sucesso." });
        }

        return new JsonResult(new { Error = false, Mensage = "Vinculo já existente" });
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpGet]
    public JsonResult SaveAllVinculo(Guid id)
    {
      try
      {
        ControlePermissaoUserServices.SaveVinculoAll(id);
        return new JsonResult(new { Error = false, Mensage = "Salvo com Sucesso." });
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpPost]
    public JsonResult ProcessaAssociacao(OrganizacaoUsuarioPartial model)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        if (ModelState.IsValid)
        {
          ControlePermissaoUserServices.CreateAssociacaoByUser(model);

          string msg;

          if (model.Create)
            msg = "Associação adicionado com sucesso";
          else
            msg = "Associação removida com sucesso";

          retornoAjax.Titulo = "Sucesso";
          retornoAjax.Mensagem = msg;
          retornoAjax.Erro = false;
          return Json(retornoAjax);
        }
        List<string> ListaErro = ModelStateHelper.GetErroList(ViewData.ModelState.Values);
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = String.Join(" <br> ", ListaErro.ToArray());
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public JsonResult DeleteAssociacao(Guid id, int codfilial, int codorg)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        ControlePermissaoUserServices.DeleteAllAssociacaoToUser(id, codfilial, codorg);

        string msg = "Associações removida com sucesso";

        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = msg;
        retornoAjax.Erro = false;
        return Json(retornoAjax);

      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public JsonResult DeleteAssociacaoAll(Guid id)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        ControlePermissaoUserServices.DeleteAllAssociacao(id);

        string msg = "Associações removida com sucesso";

        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = msg;
        retornoAjax.Erro = false;
        return Json(retornoAjax);

      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
    }

    #region CRUD Secretaria

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult IndexSecretaria(int? page)
    {
      try
      {
        int pageNumber = page ?? 1;
        IPagedList<UsuarioIndex> ListaUsuariosModelIndex = UsuariosServices.GetSecretariasByClinica(pageNumber);
        return View(ListaUsuariosModelIndex);
      }
      catch (CustomException ex)
      {
        throw new Exception(ex.Message);
      }
      catch(Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult CreateSecretaria()
    {
      try
      {
        BasicRegisterModel model = new BasicRegisterModel();
        model.TipoCadastro = (int)EnumTipoCadastro.Secretaria;
        return View(model);
      }
      catch (Exception ex)
      {
        throw;
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult CreateSecretaria(BasicRegisterModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          AccountServices accountServices = new AccountServices(ContextoUsuario.UserLogged);
          accountServices.RegistrarUsuario(model);
          MessageAdd(new Message(MessageType.Success, "Secretário criada e vinculada com sucesso."));
          return RedirectToAction("IndexSecretaria");
        }
        MessageAdd(new Message(MessageType.Warning, "Verifique os campos preenchidos."));
        return View(model);
      }
      catch (CustomException ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.Message));
        TimeOutMessage = "120000";
        return View(model);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Error, ex.ToString()));
        TimeOutMessage = "120000";
        return View(model);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult EditSecretaria(Guid coduser)
    {
      UsuarioBasico UsuariosModel = UsuariosServices.GetUsuarioBasicoById(coduser);
      return View(UsuariosModel);
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult EditSecretaria(UsuarioBasico UsuariosModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          UsuariosServices.Edit(UsuariosModel);
          MessageAdd(new Message(MessageType.Success, "Dados atualizados com sucesso."));
          return View(UsuariosModel);
          //return RedirectToAction("Index", "Usuarios");
        }

        List<string> ListaErro = ModelStateHelper.GetErroList(ViewData.ModelState.Values);
        MessageAdd(new Message(MessageType.Error, String.Join(" <br> ", ListaErro.ToArray())));
        return View(UsuariosModel);
      }
      catch (CustomException ex)
      {
        MessageAdd(new Message(MessageType.Success, ex.Message));
        return View(UsuariosModel);
      }
      catch (Exception ex)
      {
        MessageAdd(new Message(MessageType.Success, ex.Message));
        return View(UsuariosModel);
      }
    }

    #endregion

  }
}