﻿@model UniContabilEntidades.Models.ClienteModel
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Atualizar Cliente ";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

<script src="~/Views/Cliente/Cliente.js?qwe=qwe"></script>

<div class="card">
  <form asp-action="Edit">
    <div class="card-header">
      <div class="card-title">
      </div>
      <div class="card-options">
      </div>
    </div>
    <div class="card-body">
      @Html.AntiForgeryToken()
      <div asp-validation-summary="ModelOnly" class="text-danger"></div>
      <input type="hidden" asp-for="Id" />
      <div class="form-group">
        @Html.LabelFor(a => a.CodigoIdent)
        @Html.TextBoxFor(a => a.CodigoIdent, new { @class = "form-control", disabled = "true" })
        @Html.ValidationMessageFor(a => a.CodigoIdent)
      </div>
      <div class="form-group">
        <label asp-for="IdTipoCliente" class="control-label"></label>
        @Html.Select2("TipoClienteSelect", "GetTipoClienteSelect", "Selecione " + @Html.DisplayNameFor(model => model.IdTipoCliente), this.ViewContext.RouteData.Values["controller"].ToString(), "", Model.TipoClienteSelect == null ? "" : Model.TipoClienteSelect.id, Model.TipoClienteSelect == null ? "" : Model.TipoClienteSelect.text)
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.CPFCNPJ)
        @Html.TextBoxFor(a => a.CPFCNPJ, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.CPFCNPJ)
      </div>
      <div class="form-group">
        <label asp-for="Nome" class="control-label"></label>
        <input asp-for="Nome" class="form-control" />
        <span asp-validation-for="Nome" class="text-danger"></span>
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.CEP)
        @Html.TextBoxFor(a => a.CEP, new { @class = "form-control CEP" })
        @Html.ValidationMessageFor(a => a.CEP)
      </div>
      <div class="form-group">
        <label asp-for="Logradouro" class="control-label"></label>
        <input asp-for="Logradouro" class="form-control" />
        <span asp-validation-for="Logradouro" class="text-danger"></span>
      </div>
      <div class="form-group">
        @Html.LabelFor(a => a.Numero)
        @Html.TextBoxFor(a => a.Numero, new { @class = "form-control numerosOnly" })
        @Html.ValidationMessageFor(a => a.Numero)
      </div>
      <div class="form-group">
        <label asp-for="Complemento" class="control-label"></label>
        <input asp-for="Complemento" class="form-control" />
        <span asp-validation-for="Complemento" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="Bairro" class="control-label"></label>
        <input asp-for="Bairro" class="form-control" />
        <span asp-validation-for="Bairro" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="Cidade" class="control-label"></label>
        <input asp-for="Cidade" class="form-control" />
        <span asp-validation-for="Cidade" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="Estado" class="control-label"></label>
        <input asp-for="Estado" class="form-control" />
        <span asp-validation-for="Estado" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="TelefoneFixo" class="control-label"></label>
        <input asp-for="TelefoneFixo" class="form-control telefone" />
        <span asp-validation-for="TelefoneFixo" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="Email" class="control-label"></label>
        <input asp-for="Email" class="form-control EMAIL" />
        <span asp-validation-for="Email" class="text-danger"></span>
      </div>
      <div class="form-group">
        <label asp-for="TelefoneCelular" class="control-label"></label>
        <input asp-for="TelefoneCelular" class="form-control telefone" />
        <span asp-validation-for="TelefoneCelular" class="text-danger"></span>
      </div>
      @*<div class="form-group form-check">
          <label class="form-check-label">
            <input class="form-check-input" asp-for="WhatsApp" /> @Html.DisplayNameFor(model => model.WhatsApp)
          </label>
        </div>*@
      <input type="hidden" value="false" name="WhatsApp" id="WhatsApp" />
      <div class="form-group form-check">
        <label class="form-check-label">
          <input class="form-check-input" asp-for="Ativo" /> @Html.DisplayNameFor(model => model.Ativo)
        </label>
      </div>
      <div class="form-group form-check">
        <label class="form-check-label">
          <input class="form-check-input" asp-for="ISSRetido" /> @Html.DisplayNameFor(model => model.ISSRetido)
        </label>
      </div>
      <br />
      <div class="col-md-8" id="divConv" style="display: flex;margin-top:1%">
        <div class="form-group col-md-4">
          <label asp-for="InscricaoMunicipal" class="control-label"></label>
          <input asp-for="InscricaoMunicipal" class="form-control" />
          <span asp-validation-for="InscricaoMunicipal" class="text-danger"></span>
        </div>
        <div class="form-group col-md-4">
          <label asp-for="CodMunicipio" class="control-label"></label>
          <input asp-for="CodMunicipio" class="form-control" />
          <span asp-validation-for="CodMunicipio" class="text-danger"></span>
        </div>
        <div class="form-group col-md-4">
          <label asp-for="CodTributacaoMunicipio" class="control-label"></label>
          <input asp-for="CodTributacaoMunicipio" class="form-control" />
          <span asp-validation-for="CodTributacaoMunicipio" class="text-danger"></span>
        </div>
      </div>
      <div class="col-md-8" id="divPart" style="display: flex; margin-top:1%">
        <div class="form-group col-md-4">
          <label asp-for="DtaNascimento" class="control-label"></label>
          <input asp-for="DtaNascimento" class="form-control" />
          <span asp-validation-for="DtaNascimento" class="text-danger"></span>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <a asp-action="Index" class="link link-info">Voltar</a>
      <input type="submit" value="Editar" class="btn btn-primary" />
    </div>
  </form>
</div>

