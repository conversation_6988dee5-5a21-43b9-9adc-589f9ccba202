﻿@model UniContabilEntidades.Models.FilialModel
@using X.PagedList.Mvc.Core;
@using X.PagedList;
@using X.PagedList.Mvc.Core.Common;
@using UniContabil.Infrastructure;

@{
  ViewData["Title"] = @Html.DisplayNameFor(model => model);
}


<script src="~/Views/Filial/FilialIndex.js?was=dgda"></script>

<div class="table-group card">
  <div class="card-header ">

    <div class="row">
      @if (ContextoUsuario.UserLogged.Contabilidade == null)
      {
        if (ContextoUsuario.HasPermission("Filial", TipoFuncao.Create))
        {
          <a class="btn btn-success" asp-action="Create"><i class="mw-drip-plus"></i></a>
        }
      }
      else
      {
        <div class="col-md-12">
          <div class="form-group">
            <button type="button" class="btn btn-sm btn-warning" id="enviaEmailMed">Enviar Convite para o Cliente</button>
          </div>
        </div>
      }
    </div>
  </div>
  <div class="table-group-content ">
    <div class="table-responsive">
      <table class="table table-sm table-striped table-bordered table-houver" style="padding: 0 !important;white-space: nowrap;">
        <thead style="text-align:center">
          <tr>
            <th>
              Razão Social
            </th>
            <th>
              @Html.DisplayNameFor(model => model.CPFCNPJ)
            </th>
            <th>
              Tipo
            </th>
            <th>
              Tel. Fixo
            </th>
            <th>
              @Html.DisplayNameFor(model => model.Email)
            </th>
            <th>
              Tel. Celular
            </th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          @foreach (UniContabilEntidades.Models.FilialModel item in ViewBag.PageItems)
          {
            <tr>
              <td>
                @Html.DisplayFor(modelItem => item.Nome)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.CPFCNPJ)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.TipoPessoa)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.TelefoneFixo)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.Email)
              </td>
              <td>
                @Html.DisplayFor(modelItem => item.TelefoneCelular)
              </td>
              <td>
                @if (ContextoUsuario.HasPermission("Filial", TipoFuncao.Edit))
                {
                  <a class="btn btn-warning btn-sm" asp-action="Edit" asp-route-id="@item.Id"><i class="mw-drip-document-edit"></i></a>
                }
                @if (ContextoUsuario.HasPermission("Filial", TipoFuncao.Delete) && !item.TipoPessoa.Equals("PF-Pessoa Fisica"))
                {
                  @*<button class="btn btn-danger btn-sm" onclick="DeleteItem('@this.ViewContext.RouteData.Values["controller"].ToString()', @item.Id)"><i class="mw-drip-trash"></i></button>*@
                }
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
    <div class="card-footer">
      @Html.PagedListPager((IPagedList)ViewBag.PageItems, page => Url.Action("Index", new { page }),
      new PagedListRenderOptions
           {
              LiElementClasses = new string[] { "page-item" },
              PageClasses = new string[] { "page-link" },
              Display = X.PagedList.Web.Common.PagedListDisplayMode.Always,
              MaximumPageNumbersToDisplay = 5
            })
    </div>
  </div>
</div>

<div class="card">
  <div class="card-header">
    <div class="col-md-12 row" style="align-items:center; justify-content:space-between">
      <h6>Médico(s) / Clínica(s) Convidado(s)</h6>
      <div class="col-md-3 row" style="justify-content: space-evenly ">
        <i class="mw-circle-full" style="color: yellow"></i> Pendente
        <i class="mw-circle-full" style="color: green"></i> Aceito
        <i class="mw-circle-full" style="color: red"></i> Rejeitado
      </div>
    </div>
  </div>
  <div class="card-body" id="medConvite">

  </div>
</div>

<div class="card">
  <div class="card-header">
    <div class="col-md-12 row" style="align-items:center; justify-content:space-between">
      <h6>Convites feitos à(s) contabilidade(s) </h6>
      @*<div class="col-md-3 row" style=" justify-content: space-evenly">
        <i class="mw-circle-full" style="color: yellow"></i> Pendente
        <i class="mw-circle-full" style="color: green"></i> Aceito
        <i class="mw-circle-full" style="color: red"></i> Rejeitado
      </div>*@
    </div>
  </div>
  <div class="card-body" id="ContabConvite">

  </div>
</div>


@Html.Partial("_ModalEmailMedico")