﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;
using Microsoft.AspNetCore.Authorization;

namespace UniContabil.Controllers
{
  [Authorize]
  public class BancoController : LibController
  {
    public BancoController()
    { }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        IPagedList<BancoModel> list = new BancoServices(ContextoUsuario.UserLogged).GetPagedList(page);

        if (list == null)
          list = new List<BancoModel>().ToPagedList();

        return View(list);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View(new BancoModel());
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(BancoModel bancoModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          if (ContextoUsuario.UserLogged.IdOrganizacao == 0 && !ContextoUsuario.UserLogged.IdFilial.HasValue)
          {
            MessageAdd(new Message(MessageType.Warning, "É necessário selecionar a clínica."));
            return View(bancoModel);
          }

          new BancoServices(ContextoUsuario.UserLogged).Create(bancoModel);
          MessageAdd(new Message(MessageType.Success, "Banco criado com sucesso."));
          return RedirectToAction(nameof(Index));
        }
        
        MessageAdd(new Message(MessageType.Warning, "Favor verificar os campos."));
        return View(bancoModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(bancoModel);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(int id)
    {
      try
      {
        BancoEditModel bancoModel = new BancoServices(ContextoUsuario.UserLogged).GetById(id);

        if (bancoModel == null)
          return NotFound();

        return View(bancoModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(BancoEditModel bancoModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new BancoServices(ContextoUsuario.UserLogged).Edit(bancoModel);
          MessageAdd(new Message(MessageType.Success, "Banco criado com sucesso."));
          return RedirectToAction(nameof(Index));
        }

        MessageAdd(new Message(MessageType.Warning, "Favor verificar os campos."));
        return View(bancoModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(bancoModel);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string Delete(int id)
    {
      try
      {
        new BancoServices(ContextoUsuario.UserLogged).Delete(id);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }

    [HttpGet]
    public ActionResult GetOrganizacaoSelect(string term)
    {
      OrganizacaoServices Service = new OrganizacaoServices();
      List<Select2Model> List = Service.GetByTerm(term);
      return Json(new { items = List });
    }
  }
}
