﻿@using UniContabil.Infrastructure.Controls
@using UniContabil.Infrastructure
@using UniContabilEntidades.Models
@model List<FilialPartial>

@{
  ViewData["Title"] = "Usuario";
}
<style>
  .selects {
    margin-left: 5pt;
    margin-top: 5pt;
    height: 13px !important;
  }

  .divtable {
    overflow-y: auto;
    max-height: 250px;
    margin-bottom: 10px;
  }

    .divtable::-webkit-scrollbar {
      width: 2px;
    }

    .divtable::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    }

    .divtable::-webkit-scrollbar-thumb {
      background-color: #afafaf;
      outline: 1px solid #adabab;
      border-radius: 10px;
    }

  .table-group .table-group-content table th {
    font-size: 15pt !important;
    color: black !important;
  }
</style>
@if (Model.Count > 0)
{

  <div class="table-group card">
    <div class="table-group-content">
      <div class="table-responsive">
        <table id="filiais-table"  >
          <thead>
            <tr>
              <th>
                <input style="height: 13px !important;margin-left: 5pt;" type="checkbox" id="selectAll" class="select-all" /> Filial
              </th>
            </tr>
            <tr>
              <th>
                Nome/Razão Social
              </th>
              <th>
                CNPJ/CPF
              </th>
              <th>
                Endereço
              </th>
              <th>Tipo Pessoa</th>
              <th></th>
            </tr>

          </thead>

          <tbody>
            @for (int i = 0; i < Model.Count; i++)
            @*@foreach (var item in Model)*@
            {
              <tr data-trfilial="@Model[i].Id">
                @Html.HiddenFor(a => a[i].Id)
                @Html.HiddenFor(a => a[i].Nome)
                <td>
                  <input type="hidden" data-codigo="@Model[i].Id" data-codorg="@Model[i].IdOrganizacao" data-linha="@i" id="@String.Format("h_selectfilial_{0}", i)" class="selects" />
                  @*<input type="checkbox" data-codigo="@Model[i].Id" id="@String.Format("z{0}__Check", i)" name="@String.Format("Model[{0}].Check", i)" class="selects" />*@
                  @Html.CheckBoxFor(a => a[i].Check, new { @style = "margin-left: 5pt;margin - top: 5pt; height: 13px !important;", @class = "selects", @id = string.Format("selectfilial_{0}", i) })
                  <span> @Model[i].Nome </span>
                </td>
                <td> <span> @Model[i].CPFCNPJ </span></td>
                <td> <span> @Model[i].Logradouro </span></td>
                <td>  <span> @Model[i].TipoPessoa </span></td>
              </tr>
            }
          </tbody>
        </table>
      </div>
      </div>
  </div>
}

<script src="~/Views/Usuarios/GridFiliais.js?ds=bh"></script>
