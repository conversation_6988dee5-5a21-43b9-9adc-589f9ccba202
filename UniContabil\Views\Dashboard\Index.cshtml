﻿@{
  ViewBag.Title = "Dashboard";
}

<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script src="~/Views/Dashboard/Dashboard.js?2=1"></script>
<link href="~/Views/Dashboard/Dashboard.css?2=1" rel="stylesheet" />


<br />
<br />
<div class="row">
  <div class="col-md-6">
    <div class="card">
      <div class="card-header ui-sortable-handle" style="text-align: center;
                                                        font-weight: bold;
                                                        border-bottom: 5px solid #2bb0d1 !important;
                                                        cursor: move;">
        <h3 class="card-title" style="width: 100%;color:#2bb0d1">
          Maio/2022
        </h3>
      </div>
      <div class="card-body">
        <div class="col-md-6">
          <div id="RepasseMes" style="border-left: 5px solid #2bb0d1;"></div>
        </div>
        <div class="col-md-6">
          <div class="small-box PrevisaoRepasse">
            <div class="inner">
              <h3 style="margin:0 !important">R$ 75.000</h3>
              <p style="padding:0 !important">Previsão próximo Repasse</p>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>

  <div class="col-md-6">
    <div class="card">
      <div class="card-header ui-sortable-handle" style="text-align: center;
                                                        font-weight: bold;
                                                        border-bottom: 5px solid #3c7391 !important;
                                                        cursor: move;">
        <h3 class="card-title" style="width: 100%;color:#3c7391;">
          Ano 2022
        </h3>
      </div>
      <div class="card-body">
        <div class="col-md-6">
          <div class="col-md-12">
            <div class="small-box totalFat">
              <div class="inner">
                <h3 id="TotalReceita" style="margin:0 !important"></h3>
                <p style="padding:0 !important">Total Receita</p>
              </div>
            </div>
          </div>
          <br />
          <div class="col-md-12">
            <div id="GuiaStatus"></div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="col-md-12">
            <div class="small-box glosas">
              <div class="inner">
                <h3 id="TotalDespesa" style="margin:0 !important"></h3>
                <p style="padding:0 !important">Total de Despesas</p>
              </div>
            </div>
          </div>
          <br />
          <div class="col-md-12">
            <div id="DistribuicaoGlosas"></div>
          </div>
        </div>
      </div>
    </div>


    @*<div class="card">
      <div class="card-header ui-sortable-handle" style="text-align: center;
                                                         border-radius: 0 !important;
                                                         font-weight: bold;
                                                         border-bottom: 5px solid #3c7391 !important;
                                                         cursor: move;">
        <h3 class="card-title" style=" width: 100%;">
          DRE - DEMONSTRATIVO DE RESULTADOS DO EXERCÍCIO
        </h3>
      </div>
      <div class="card-body">
        <div class="col-md-12">
          <div id="DemonstrativoResult"></div>
        </div>
      </div>
      <div class="card-body">
        <div class="col-md-12">
          <div id="Demonstrativo"></div>
        </div>
      </div>

    </div>*@

  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-body" id="gridDRE">
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function () {
    $.ajax({
      url: GetURLBaseComplete() + "/Dashboard/GetGridDRE",
      dataType: "html",
      type: "GET",
      success: function (data) {
        $("#gridDRE").html("");
        $("#gridDRE").html(data);
      }
    });
  });
</script>