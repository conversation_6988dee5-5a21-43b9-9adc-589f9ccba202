﻿@using UniContabilDomain
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>@ViewData["Title"] - UniContabil</title>
  <environment include="Development">
    <link rel="stylesheet" href="~/lib/font-awesome/css/fontawesome.css" />
  </environment>

  <link rel="stylesheet" href="~/Content/MWFont.css" />
  <link rel="stylesheet" href="~/Content/Site-1.1.0.css?odi=oioi" />
  <link rel="stylesheet" href="~/Content/select2.min.css" />
  <link rel="stylesheet" href="~/Content/iziToast.min.css" />
  <link rel="stylesheet" href="~/Content/jstree.min.css" />
  <link rel="stylesheet" href="~/Content/mmmicons.css" />
  <link rel="stylesheet" href="~/Content/bootstrap.min.css" />
  <link rel="stylesheet" href="~/Content/bootstrap-grid.min.css" />
  <link rel="stylesheet" href="~/Content/adminlte.css" />
  <link href="~/Content/OverlayScrollbars.css" rel="stylesheet" />
  <link rel="stylesheet" type="text/css" href="http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css" />

  <script src="~/lib/jquery/dist/jquery.min.js"></script>
  <script src="~/js/Site-1.1.0.js?c=c"></script>
  <script src="~/js/jquery-ui.min.js"></script>
  <script src="~/js/bootstrap.js"></script>
  <script src="~/js/bootstrap.js"></script>
  <script src="~/js/bootstrap.bundle.min.js"></script>
  <script src="~/js/bootstrap.js"></script>
  <script src="~/js/select2.min.js"></script>
  <script src="~/js/Select2Controls-1.1.0.js"></script>
  <script src="~/js/iziToast.min.js"></script>
  <script src="~/js/SweetAlert/SweetAlert.js"></script>
  <script src="~/js/jstree.min.js"></script>
  <script src="~/js/jquery.maskMoney.js"></script>
  <script src="~/Scripts/inputmask/inputmask.js"></script>
  <script src="~/Scripts/inputmask/jquery.inputmask.js"></script>
  <script src="~/Scripts/inputmask/inputmask.extensions.js"></script>
  <script src="~/Scripts/inputmask/inputmask.date.extensions.js"></script>
  <script src="~/Scripts/inputmask/inputmask.numeric.extensions.js"></script>
  <script src="~/Views/Shared/Shared.js"></script>
  <script src="~/js/adminlte.js"></script>
  <script src="~/js/globalize.js"></script>
  <script src="~/js/moment.js"></script>

</head>
<body class="hold-transition sidebar-mini layout-fixed layout-navbar-fixed layout-footer-fixed">

  <div class="modal-backdrop overlay" id="fundo-loading">
    <i class="fa fa-refresh fa-spin"></i>
  </div>

  <div class="wrapper">

    <!-- Preloader -->
    @*<div class="preloader flex-column justify-content-center align-items-center">
        <img class="animation__wobble" src="dist/img/AdminLTELogo.png" alt="AdminLTELogo" height="60" width="60">
      </div>*@

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand" style=" background-color: #b5babd; display: flex; justify-content: space-between; max-height: 60px;">
      <!-- Left navbar links -->

      <ul class="navbar-nav">
        <li class="nav-item" style=" display: flex; align-items: center;color:white">
          <a class="nav-link" data-widget="pushmenu" role="button" style="padding-right: .5rem; padding-left: .5rem;color:white">
            <i class="mw-drip-menu" style="font-size: 25px; color: white;"></i>
          </a>
          @if (!(ContextoUsuario.UserLogged.Permissoes.Where(a => a.NomeGrupo.Equals("Unicooper")).Count() > 0))
          {
            @Html.Select2("OrganizacaoUserSelect", "GetOrganizacaoByUserSelect", "Selecione a Organização", valor: ContextoUsuario.UserLogged.IdFilial.ToString(), text: ContextoUsuario.GetOrganizacaoNome(), classLabel: "LookupViewShared")
          }
        </li>
      </ul>


      <ul class="navbar-nav">
        <li class="nav-item" style="color:white;display:flex">
          @*<i class="mw-MeuPerfil" style="color:#486a97;font-size: 35px;"></i><h4 id="Organizacao" data-org="@ContextoUsuario.UserLogged.IdOrganizacao"> @ContextoUsuario.GetOrganizacaoNome() <a class="change-data link link-info" href="@Url.Action("Index", "Configure")">Alterar</a></h4>*@
          <h5 style="border-bottom: none;">@ViewData["Title"]</h5>
        </li>
        <li onclick="Shared.ShowModalResetPassword();" style="cursor:pointer;text-align: start; border-left: 1px solid; margin-left: 10px; padding-left: 10px; height: calc(4vh - 5px); padding-bottom: 5px;">
          <p class="userdata">
            <span class="datavalue">@ContextoUsuario.UserLogged.Nome</span>
          </p>
          <p class="userdata" style="margin-top: -5px !important;display:none;">
            <span class="datavalue" id="Filial" data-filial="@ContextoUsuario.UserLogged.IdFilial">@ContextoUsuario.GetFilialNome()</span>
          </p>
        </li>
      </ul>
    </nav>
    <!-- /.navbar -->
    <!-- Main Sidebar Container -->
    <aside class="main-sidebar sidebar-light-primary elevation-4" style="background-color:#e6e7e9">
      <!-- Brand Logo -->
      <a href="#" class="brand-link" style="background-color:#e6e7e9;border-color: transparent;">
        <img src="~/Content/img/Logo_Unicontabil.jpeg" alt="UniContabil Logo" class="brand-image">
        <span class="brand-text font-weight-light"></span>
      </a>

      <!-- Sidebar -->
      <div class="sidebar" style="min-height: calc(100vh - 60px);">
        <nav class="mt-2">
          <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
            @foreach (SidebarModuloModel Modulo in ContextoUsuario.UserLogged.Sidemenu)
            {
              @*<li class="nav-header sidebar-group-title">
                  <p>@(Modulo.Nome)</p>
                </li>*@
              @foreach (SidebarItemModel Item in Modulo.Itens.Where(a =>!a.Nome.Equals("Categoria Documento")).OrderBy(a => a.Nome))
              {
                <li class="nav-item sidebar-group">
                  <i class="nav-icon sidebar-menu-icon"></i>
                  <a class="nav-link" href="@if (!string.IsNullOrEmpty(Item.ActionName)) { <text> @Url.Action(Item.ActionName, Item.ControllerName) </text>} else { <text>#</text> } "><p><b>@Item.Nome</b></p></a>
                  @if (Item.Itens.Count > 0)
                  {
                    <i class="sidebar-menu-arrow mw-drip-chevron-down collapseDown" href="@string.Format("#collapse{0}", Item.Id)" data-toggle="collapse" aria-expanded="false" aria-controls="@string.Format("collapse{0}", Item.Id)"></i>
                  }
                  <!--submenus-->
                  <div class="sidebar-item collapse" id="@string.Format("collapse{0}", Item.Id)">
                    @foreach (SidebarItemModel Option in Item.Itens)
                    {
                      <ul class="nav nav-treeview">
                        <li class="nav-item">
                          <a class="nav-link" href="@Url.Action(Option.ActionName, Option.ControllerName)"><p><b>@Option.Nome</b></p></a>
                        </li>
                      </ul>
                    }
                  </div>
                </li>
              }
            }
            <li class="nav-item sidebar-group">
              <i class="nav-icon sidebar-menu-icon"></i>
              <a class="nav-link" href="@Url.Action("LogOut", "Account")"><p><b>Sair</b></p></a>
              <!--submenus-->
              <div class="sidebar-item collapse" id="collapse82">
              </div>
            </li>
          </ul>
        </nav>
        <!-- /.sidebar-menu -->
      </div>
      <!-- /.sidebar -->
    </aside>

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper p-1" style="margin-top: calc(3.5rem - 25px);">
      @RenderBody()
    </div>
    <!-- /.content-wrapper -->
    <!-- Control Sidebar -->
    <aside class="control-sidebar control-sidebar-light">
      <!-- Control sidebar content goes here -->
    </aside>
    <!-- /.control-sidebar -->
    <!-- Main Footer -->
    @*<footer class="main-footer">
        <strong>Copyright &copy; 2014-2021 <a href="https://adminlte.io">AdminLTE.io</a>.</strong>
        All rights reserved.
        <div class="float-right d-none d-sm-inline-block">
          <b>Version</b> 3.1.0
        </div>
      </footer>*@
  </div>
  @using UniContabil.Infrastructure
  @using UniContabil.Infrastructure.Controls
  @Html.LibMessageBoxAlert()
  @RenderSection("Scripts", required: false)
  @{
    await Html.RenderPartialAsync("_ModalOrganizacao");
    await Html.RenderPartialAsync("_ModalResetPassword");
  }
</body>
</html>
<style>
  .sidebar {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .nav-sidebar > .nav-item {
    box-shadow: inset 0px -13px 20px 0px #bfc0c2;
  }

  h4 {
    margin: 0 !important;
  }

  .main-header .select2 {
    margin-bottom: 10px;
  }

  .main-header .select2-selection {
    width: 300px;
  }
</style>