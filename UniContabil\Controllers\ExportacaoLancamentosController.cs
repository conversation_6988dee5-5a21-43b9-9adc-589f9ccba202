﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MWHelper.Exceptions;

using System;
using System.Collections.Generic;
using System.Configuration;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using UniContabilDomain.Services;
using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;

namespace UniContabil.Controllers
{
  [Authorize]
  public class ExportacaoLancamentosController : LibController
  {

    private ExportacaoLancamentosService ExportacaoLancamentosService
    {
      get
      {
        if (_ExportacaoLancamentosService == null)
          _ExportacaoLancamentosService = new ExportacaoLancamentosService(ContextoUsuario.UserLogged);

        return _ExportacaoLancamentosService;
      }
    }
    private ExportacaoLancamentosService _ExportacaoLancamentosService;

    [HttpGet]
    //[NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1, string dtIni = null, string dtFim = null)
    {
      try
      {
        //DateTime? dataIni = null, dataFim = null;

        //if (!string.IsNullOrEmpty(dtIni))
        //  dataIni = Convert.ToDateTime(dtIni, new CultureInfo("pt-BR"));

        //if (!string.IsNullOrEmpty(dtFim))
        //  dataFim = Convert.ToDateTime(dtFim, new CultureInfo("pt-BR"));

        //IPagedList<ExportacaoLancamentosIndex> List = new ExportacaoLancamentosService().GetAll(page, dataIni, dataFim);
        //return View(List);
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    public PartialViewResult FiltroEmpresasLancamentos(string dtIni = null, string dtFim = null,string exportado = null)
    {
      try
      {
        DateTime? dataIni = null, dataFim = null;
        bool? export = null;

        if (!string.IsNullOrEmpty(dtIni))
          dataIni = Convert.ToDateTime(dtIni, new CultureInfo("pt-BR"));

        if (!string.IsNullOrEmpty(dtFim))
          dataFim = Convert.ToDateTime(dtFim, new CultureInfo("pt-BR"));

        if (!string.IsNullOrEmpty(exportado))
          export = exportado == "1" ? true : false;

        List<ExportacaoLancamentosIndex> List = ExportacaoLancamentosService.GetAll(dataIni, dataFim, export);
        return PartialView("_GridFiltro", List);
      }
      catch (Exception)
      {
        return PartialView("_GridFiltro", new List<ExportacaoLancamentosIndex>());
      }
    }

    [HttpPost]
    public JsonResult Export(int[] listFil, string dtIni, string dtFim, string exportado = null)
    {
      try
      {
        DateTime? dataIni = null, dataFim = null;
        bool? export = null;

        if (!string.IsNullOrEmpty(dtIni))
          dataIni = Convert.ToDateTime(dtIni, new CultureInfo("pt-BR"));

        if (!string.IsNullOrEmpty(dtFim))
          dataFim = Convert.ToDateTime(dtFim, new CultureInfo("pt-BR"));

        if (!string.IsNullOrEmpty(exportado))
          export = exportado == "1" ? true : false;

        string fileName = ExportacaoLancamentosService.ExportCSV(listFil, dataIni, dataFim, export);
        return new JsonResult(new { error = false, mensage = "Sucesso", fileName = fileName });
      }
      catch (Exception ex)
      {
        return new JsonResult(new { error = true, mensage = ex.Message });
      }
    }

    public FileContentResult DownloadExport(string name)
    {
      try
      {
        byte[] reportData = null;
        string pathTempFiles = ConfigurationManager.AppSettings["TempFiles"].ToString();
        string zipFile = $"{pathTempFiles}{name}";
        reportData = System.IO.File.ReadAllBytes(zipFile);
        return File(reportData, "application/zip", name);
      }
      catch (Exception ex)
      {
        byte[] vazio = new byte[0];
        return File(vazio, "application/zip", "erro.zip");
      }
    }
  }
}
