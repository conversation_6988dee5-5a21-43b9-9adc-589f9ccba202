﻿@using UniContabilEntidades.Models.DataBase
@using UniContabil.Infrastructure

@model List<E_AnexoContabilidade>
<div class="table-group-content">
  <table class="table table-sm table-striped table-bordered table-houver">
    <thead>
      <tr>
        <th>
          Nome
        </th>
        <th>
          Data
        </th>
      </tr>
    </thead>
    <tbody>
      @for (int z = 0; z < Model.Count; z++)
      {
        <tr>
          <td>@Model[z].AC_NomeArquivo</td>
          <td>@Model[z].AC_DataCriacao.Value.ToString("dd/MM/yyyy")</td>
          <td>
            <button class="btn btn-outline-success" onclick="Contabilidade.VerAnexo(@("'" + Model[z].AC_Id + "'"))">Ver Anexo</button>
            @if (ContextoUsuario.UserLogged.Contabilidade != null && ContextoUsuario.UserLogged.Contabilidade.adm)
            {
              <button class="btn btn-outline-danger" onclick="Contabilidade.DeleteAnexo(@("'" + Model[z].AC_Id + "'"))">Excluir</button>
            }
          </td>
        </tr>
      }
    </tbody>
  </table>
</div>
