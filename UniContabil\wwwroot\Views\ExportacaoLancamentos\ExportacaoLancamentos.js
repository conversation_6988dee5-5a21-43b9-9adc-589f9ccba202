﻿var ExportacaoLancamentos = function () { };


ExportacaoLancamentos.init = function () {

};

$(document).ready(function () {
  ExportacaoLancamentos.init();
});

ExportacaoLancamentos.Search = function () {
  const dtIni = $("#dtIni").val();
  const dtFim = $("#dtFim").val();
  const exportado = $("#Exportado").val();

  $("#GridFiltro").html("");
  $(".spinner-table").attr("style", "display: flex !important; margin-top: 5px;margin-bottom: 5px;");
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/ExportacaoLancamentos/FiltroEmpresasLancamentos',
    dataType: 'html',
    data: {
      dtIni: dtIni,
      dtFim: dtFim,
      exportado: exportado,
    },
    success: function (data) {
      $(".spinner-table").attr("style", "display: none !important");
      $("#GridFiltro").html(data);
    }
  });
}

ExportacaoLancamentos.Exportar = function (coduser) {

  var checkboxes = document.querySelectorAll('input[class=selectsEx]:checked')
  if (checkboxes.length < 1) {
    SweetAlert("Atenção", "Selecione um Médico/Clínica para exportação dos lançamentos.", "warning");
  } else {
    const dtIni = $("#dtIni").val();
    const dtFim = $("#dtFim").val();
    const exportado = $("#Exportado").val();
    $(".spinner-table").attr("style", "display: flex !important; margin-top: 5px;margin-bottom: 5px;");

    var arrayIdFil = [];

    for (var i = 0; i < checkboxes.length; i++) {
      arrayIdFil.push(checkboxes[i].getAttribute("idfil"))
    }

    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/ExportacaoLancamentos/Export',
      dataType: 'json',
      data: {
        listFil: arrayIdFil,
        dtIni: dtIni,
        dtFim: dtFim,
        exportado: exportado,
      },
      success: function (data) {
        $(".spinner-table").attr("style", "display: none !important");
        if (!data.error) {
          let url = GetURLBaseComplete() + '/ExportacaoLancamentos/DownloadExport?name=' + data.fileName;
          window.open(url, '_blank')
          //SweetAlert("Sucesso", data.mensage, "success");
        }
        else {
          SweetAlert("Erro", data.mensage, "danger");
        }
      }
    });
  }
}

