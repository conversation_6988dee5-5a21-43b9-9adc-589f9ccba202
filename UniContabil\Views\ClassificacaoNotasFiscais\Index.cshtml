﻿@using UniContabilEntidades.Models
@model List<LancamentosModel>

@{
  ViewData["Title"] = "Classificar Movimentações";
  Layout = "~/Views/Shared/_Layout.cshtml";
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();
}
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="~/Views/ClassificacaoNotasFiscais/ClassificacaoNotasFiscais.js?d4d=dte"></script>
<link href="~/Views/ClassificacaoNotasFiscais/ClaassificadoNotasFiscais.css?14qd=12q" rel="stylesheet" />

<div class="card" style="padding: 0 !important;margin-bottom: 0 !important;">
  <div class="card-header">
    <p><b>Para incluir uma movimentação selecione o Item de Receita ou Despesas, Clique com o Botão Direito e selecione a opção Incluir Movimentação.</b></p>
  </div>
</div>
<div class="card" style="display:flex !important;padding:10px !important; margin-top: 0 !important;">
  <div class="col-md-3" style="overflow:auto;">
    <div class="row">
      <div class="form-group">
        <div class="col-md-12">
          <input type="radio" name="Tipo" class="custom-radio" value="1" id="Tipo_1" />
          <label>Receita</label>
          <input type="radio" name="Tipo" class="custom-radio" value="2" id="Tipo_2" checked="checked" />
          <label>Despesa</label>
        </div>
      </div>
    </div>
    <div id="tree" style=" max-height: calc(100vh - 100px);">
    </div>
  </div>

  <div class="col-md-9" style=" text-align: center; color: #d1ebff;">
    <div class="row" style="margin-bottom: 5px !important; justify-content: space-between;">

      <div class="col-md-2">
        <div>
          <label class="txtAno">ANO</label>
          <select id="ano" class="form-control">
            <option>Selecione o Ano</option>
            <option value="2021">2021</option>
            <option value="2022" selected="selected">2022</option>
            <option value="2023">2023</option>
            <option value="2024">2024</option>
            <option value="2025">2025</option>
          </select>
        </div>
      </div>

      <div class="row col-md-8" style="text-align: center; display: flex; justify-content: space-evenly; align-items: end;">
        <div class="classif docC" id="classificados" data-classificado="1">
          <a class="btn btn-sm btn-outline-info">Documentos Classificados</a>
        </div>
        <div class="classif docN selecionado" id="Naoclass" data-classificado="0">
          <a class="btn btn-sm btn-outline-info">Documentos Não Classificados</a>
        </div>
      </div>
    </div>

    <div id="resizebleDiv">
      <div class="row divTable">
        @Html.Partial("_GridClassificados", Model)
      </div>
    </div>

    <div class="row" style="text-align: center; color: black; display: inline;">
      <div class="col-md-12 subtitle" style="margin-bottom: 0 !important;">
        <label>DADOS DA MOVIMENTAÇÃO</label>
      </div>
      <div class="card" id="InfoUserPartial">
      </div>
    </div>

    <div class="row">
      <div class="col-md-12 subtitle">
        <label>COMPROVANTE</label>
      </div>
      <div class="Docs" id="Docs">

      </div>
    </div>
  </div>
</div>

@Html.Partial("Modal")

@if (ViewBag.HasConvite == 1)
{
  <script>
    $(document).ready(function () {
      $("#modalConvite").modal("show");
    });
  </script>
}