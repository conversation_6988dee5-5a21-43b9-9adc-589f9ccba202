﻿@model UniContabilEntidades.Models.DependentesModel
@using UniContabil.Infrastructure.Controls

@{
  ViewData["Title"] = "Novo " + @Html.DisplayNameFor(model => model);
  Layout = "";
}
<div class="card">
  @using (Html.BeginForm("CreateDependentes", "Perfil", FormMethod.Post, new { id = "formCreateDependentes" }))
  {
    <div class="card-body">
      @Html.HiddenFor(a => a.Id)
      <div class="row">
        <div class="col-md-4">
          <div class="form-group">
            @Html.LabelFor(a => a.CPFdep)
            @Html.TextBoxFor(a => a.CPFdep, new { @class = "form-control CPF" })
            @Html.ValidationMessageFor(a => a.CPFdep)
          </div>
        </div>
        <div class="col-md-4">
          <div class="form-group">
            @Html.LabelFor(a => a.NomeDep)
            @Html.TextBoxFor(a => a.NomeDep, new { @class = "form-control" })
            @Html.ValidationMessageFor(a => a.NomeDep)
          </div>
        </div>
        <div class="col-md-4">
          <div class="form-group">
            @Html.LabelFor(a => a.Nascimento)
            @Html.TextBoxFor(a => a.Nascimento, new { type = "date", @class = "form-control EMAIL" })
            @Html.ValidationMessageFor(a => a.Nascimento)
          </div>
        </div>
      </div>
      <div class="row">
        <div class="form-group" style="width: 740px;">
          <label asp-for="TipoDependente" class="control-label"></label>
          @Html.Select2("DependenteSelect", "GetTipoDependenteSelect", "Selecione " + @Html.DisplayNameFor(model => model.TipoDependente), this.ViewContext.RouteData.Values["controller"].ToString())
        </div>
      </div>
    </div>

  }
</div>
