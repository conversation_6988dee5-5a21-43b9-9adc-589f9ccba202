﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;

namespace UniContabil.Controllers
{
  public class DemonstrativoResultadoExercicioController : LibController
  {
    public DemonstrativoResultadoExercicioController()
    { }
   
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        IPagedList<DemonstrativoResultadoExercicioModel> List = new DemonstrativoResultadoExercicioService().GetPagedList(page);
        ViewBag.PageItems = List;
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(DemonstrativoResultadoExercicioModel demonstrativoResultadoExercicioModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          var model = demonstrativoResultadoExercicioModel.ToDatabase();
          new DemonstrativoResultadoExercicioService().Create(model);

          return RedirectToAction(nameof(Index));
        }
        else
        {
          return View(demonstrativoResultadoExercicioModel);
        }
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(demonstrativoResultadoExercicioModel);
      }
    }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(int id)
    {
      try
      {
        DemonstrativoResultadoExercicioService demonstrativoResultadoExercicioService = new DemonstrativoResultadoExercicioService();

        DemonstrativoResultadoExercicioModel tipoDREModel = new DemonstrativoResultadoExercicioModel().FromDatabase(demonstrativoResultadoExercicioService.GetById(id));

        tipoDREModel.ListaClassificacaoDocumentos = demonstrativoResultadoExercicioService.GetListClassificacaoDocumentos(tipoDREModel.Id);

        //tipoDREModel.ListClassificDoc = new DemonstrativoResultadoExercicioService().GetClassDocList(tipoDREModel.Id);

        if (tipoDREModel == null)
        {
          return NotFound();
        }

        return View(tipoDREModel);
      }
      catch (Exception Ex)
      
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(DemonstrativoResultadoExercicioModel tipoDREModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          var model = tipoDREModel.ToDatabase();
          new DemonstrativoResultadoExercicioService().Edit(model);
          return RedirectToAction(nameof(Index));
        }
        return View(tipoDREModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(tipoDREModel);
      }
    }
  

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string Delete(int id)
    {
      try
      {
        DemonstrativoResultadoExercicioService Service = new DemonstrativoResultadoExercicioService();
        var model = Service.GetByIdDRE(id);
        Service.Delete(model);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }


    [HttpGet]
    public PartialViewResult _GetGridClassificacao(int id)
    {
      List<ListaClassificacao> listaClassificacaos = new List<ListaClassificacao>();
      listaClassificacaos = new DemonstrativoResultadoExercicioService().GetClassDocList(id);

      return PartialView(listaClassificacaos);
    }

    [HttpGet]
    public JsonResult AtualizaClassificacao(int IdDRE, int IdClassificacao)
    {
      new DemonstrativoResultadoExercicioService().AtualizarClassificacaoDRE(IdDRE, IdClassificacao);
 
      return Json("");
    }
    [HttpPost]
    public JsonResult DeleteClassificacao(int IdClassificacao)
    {
      new DemonstrativoResultadoExercicioService().DeletarClassificacaoDRE(IdClassificacao);
      return Json("");

    }
  }
}
