﻿var FilialIndex = function () { };


FilialIndex.init = function () {

  $(document).on("click", "#enviaEmailMed", function () {
    $("#NomeMed").val("");
    $("#EmailMed").val("");
    $("#CPFCNPJMed").val("");

    $("#ModalEmailMed").modal('show');
  });

  $(document).on("click", "#sendEmailMed", function () {

    let cpfcnpj = $("#CPFCNPJMed").val();

    if (cpfcnpj) {
      cpfcnpj = cpfcnpj.replaceAll(".", "");
      cpfcnpj = cpfcnpj.replaceAll("-", "");
      cpfcnpj = cpfcnpj.replaceAll("/", "");
    }

    var Model = {
      Nome: $("#NomeMed").val(),
      Email: $("#EmailMed").val(),
      CPFCNPJ: cpfcnpj,
    }

    $.ajax({
      url: GetURLBaseComplete() + "/Filial/SendEmailMed/",
      type: 'POST',
      data: Model,
      dataType: 'json',
      success: function (data) {
        if (!data.erro) {
          swal({
            title: "Sucesso",
            text: data.message,
            icon: "success",
          });
          $("#ModalEmailMed").modal('hide');
          FilialIndex.GetMedConvidado();
        }
        else {
          swal({
            title: "Erro",
            text: data.message,
            icon: "error",
          });
        }
      }
    })

  });
};

FilialIndex.GetMedConvidado = function () {
  $.ajax({
    url: GetURLBaseComplete() + "/Filial/GetMedConvidado",
    type: 'GET',
    dataType: 'html',
    success: function (data) {
      $("#medConvite").html("");
      $("#medConvite").html(data);
    }
  });
}
FilialIndex.GetContabConvidado = function () {
  $.ajax({
    url: GetURLBaseComplete() + "/Filial/GetContabConvidado",
    type: 'GET',
    dataType: 'html',
    success: function (data) {
      $("#ContabConvite").html("");
      $("#ContabConvite").html(data);
    }
  });
}

$(document).ready(function () {

  FilialIndex.init();
  FilialIndex.GetMedConvidado();
  FilialIndex.GetContabConvidado();
  
});
