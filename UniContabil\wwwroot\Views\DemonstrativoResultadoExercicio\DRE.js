﻿
var DRE = {};

DRE.Init = function () {
  $(document).on('click', '#ModalGridClassificacao', function () {
    $.ajax({
      url: GetURLBaseComplete() + '/DemonstrativoResultadoExercicio/_GetGridClassificacao/' + $("#Id").val(),
      cache: false,
      dataType: "html",
      type: "GET",
      success: function (resposta) {
        $('#gridClassificacao').html("");
        $('#gridClassificacao').html(resposta);
        $('#GridModalClassDoc').modal('show');
      },
      error: function (error) {
        console.log(error);
      }
    });
  });

  $(document).on('click', '.incluiClassificacao', function () {
    var idclassificacao = $(this).attr("data-id");

    $.ajax({
      url: GetURLBaseComplete() + '/DemonstrativoResultadoExercicio/AtualizaClassificacao?IdDRE=' + $("#Id").val() + '&IdClassificacao=' + idclassificacao,
      cache: false,
      dataType: "json",
      type: "GET",
      success: function (resposta) {
        $('#GridModalClassDoc').modal('hide');
        location.reload();
      },
      error: function (error) {
        console.log(error);
      }
    });

  });
  $(document).on('click', '.deleteClassificacao', function () {
    var idclassificacao = $(this).attr("data-id");

    $.ajax({
      url: GetURLBaseComplete() + '/DemonstrativoResultadoExercicio/DeleteClassificacao?IdDRE=' + $("#Id").val() + '&IdClassificacao=' + idclassificacao,
      cache: false,
      dataType: "json",
      type: "POST",
      success: function (resposta) {
        $('#GridModalClassDoc').modal('hide');
        location.reload();
      },
      error: function (error) {
        console.log(error);
      }
    });

  });
};


$(document).ready(function () {
  DRE.Init();
});