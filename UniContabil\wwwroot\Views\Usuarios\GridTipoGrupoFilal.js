﻿
function remakeChecks(line) {
  for (var i = 0; i < $('#option-grupos-table tbody tr').length; i++) {
    if (i != line)
      $("#selecttipogrupo_" + i).prop("checked", false);
  }
}

$(document).ready(function () {

  $(".select-all").change(function () {
    //alert('checked');
    if (this.checked) {
      for (var i = 0; i < $('#filiais-table tbody tr').length; i++) {
        //$("#selectfilial_" + i).iCheck("check");
        $("#selectfilial_" + i).prop("checked", true);
      }
    }
    else {
      for (var i = 0; i < $('#filiais-table tbody tr').length; i++) {
        $("#selectfilial_" + i).prop("checked", false);
        //$("#selectfilial_" + i).iCheck("uncheck");
      }
    }
  });

  $(".selectstiposgrupos").change(function () {
    $(".spinner-menu").attr("style", "display: flex !important; margin-top: 80px;");
    const linha = $(this).data("linha");
    remakeChecks(linha);
    const id = $(this).data("codigo");
    const codfilial = $(this).data("codfilial");
    const codorg = $(this).data("codorg");
    const createall = $(this).data("createall");
    let tipogrupo = $(this).data("idtipogrupo");
    
    //z0__TipoGrupoFilalPartial_Create
    if (!tipogrupo)
      tipogrupo = 0;

    const coduser = $("#Codigo").val();

    let model = {
      Id: id,
      IdOrganizacao: codorg,
      IdUsuario: coduser,
      IdFilial: codfilial,
      IdTipoGrupoFilal: tipogrupo,
      Create: this.checked,
      CreateAll: createall,
    }
    SendAssociacao(model);
  });

});

function SendAssociacao(model) {
  $.ajax({
    url: GetURLBaseComplete() + '/Usuarios/ProcessaAssociacao',
    dataType: "json",
    type: "POST",
    data: model,
    success: function (retorno) {
      if (!retorno.Erro) {
        Alerta(retorno.titulo, retorno.mensagem, "green");
      }
      else {
        Alerta(retorno.titulo, retorno.mensagem, "red");
      }
      GridFilial.init();
      if (model.CreateAll) 
        loadAbaOrganizacoes();
       else 
        loadGridTipoGrupo(model.IdOrganizacao, model.IdFilial)
      
    }
  });
}