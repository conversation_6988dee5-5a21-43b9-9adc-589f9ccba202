{"format": 1, "restore": {"C:\\WorkSpace\\Fontes\\Unicontabil\\UniContabil\\Unicontabil\\UniContabil.csproj": {}}, "projects": {"C:\\WorkSpace\\Fontes\\Unicontabil\\UniContabil\\Unicontabil\\UniContabil.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\WorkSpace\\Fontes\\Unicontabil\\UniContabil\\Unicontabil\\UniContabil.csproj", "projectName": "UniContabil", "projectPath": "C:\\WorkSpace\\Fontes\\Unicontabil\\UniContabil\\Unicontabil\\UniContabil.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\WorkSpace\\Fontes\\Unicontabil\\UniContabil\\Unicontabil\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"FastReport.Compat": {"target": "Package", "version": "[2020.3.6, )"}, "FastReport.OpenSource": {"target": "Package", "version": "[2020.3.4, )"}, "FastReport.OpenSource.Export.PdfSimple": {"target": "Package", "version": "[2020.3.4, )"}, "FastReport.OpenSource.Web": {"target": "Package", "version": "[2020.3.4, )"}, "Font.Awesome": {"target": "Package", "version": "[5.15.3, )"}, "HtmlAgilityPack": {"target": "Package", "version": "[1.11.36, )"}, "Magick.NET-Q16-x64": {"target": "Package", "version": "[8.3.2, )"}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": {"target": "Package", "version": "[3.1.15, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.1.0, )"}, "Microsoft.CodeAnalysis.CSharp": {"target": "Package", "version": "[3.8.0, )"}, "Microsoft.CodeAnalysis.Common": {"target": "Package", "version": "[3.8.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[3.1.7, )"}, "Microsoft.EntityFrameworkCore.Proxies": {"target": "Package", "version": "[3.1.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[3.1.7, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[3.1.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.1.7, )"}, "Microsoft.VisualStudio.Web.CodeGeneration": {"target": "Package", "version": "[3.1.4, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[3.1.4, )"}, "Nancy": {"target": "Package", "version": "[2.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[12.0.3, )"}, "QRCoder": {"target": "Package", "version": "[1.3.9, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[5.0.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.1, )"}, "System.Drawing.Common": {"target": "Package", "version": "[5.0.0, )"}, "System.IO": {"target": "Package", "version": "[4.3.0, )"}, "System.Private.ServiceModel": {"target": "Package", "version": "[4.4.4, )"}, "System.Security.Cryptography.Xml": {"target": "Package", "version": "[5.0.0, )"}, "System.ServiceModel.Duplex": {"target": "Package", "version": "[4.4.4, )"}, "System.ServiceModel.Http": {"target": "Package", "version": "[4.4.4, )"}, "System.ServiceModel.NetTcp": {"target": "Package", "version": "[4.4.4, )"}, "System.ServiceModel.Primitives": {"target": "Package", "version": "[4.4.4, )"}, "System.ServiceModel.Security": {"target": "Package", "version": "[4.4.4, )"}, "X.PagedList.Mvc.Core": {"target": "Package", "version": "[8.0.7, )"}, "bootstrap": {"target": "Package", "version": "[4.6.0, )"}, "iTextSharp": {"target": "Package", "version": "[5.5.13.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.416\\RuntimeIdentifierGraph.json"}}}}}