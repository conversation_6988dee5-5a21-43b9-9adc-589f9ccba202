﻿var Filial = function () { };
var abaselect = "";

Filial.init = function () {
  $('#campoCEP').on('keyup', function () {
    console.log('Digitado')
    completaCampos();
  })

  $("#aba-1").click(function () {
    $(".body-aba-2").html("");
    $("#body-tipo-grupo-filial").html("");
    abaselect = "aba-1";

    reloadAba("aba-1");
  });

  $("#aba-2").click(function () {
    $(".spinner-table").attr("style", "display: flex !important; margin-top: 5px;margin-bottom: 5px;");
    abaselect = "aba-2";
    reloadAba("aba-2");
    loadAbaSocios();
  });

  $("#aba-3").click(function () {
    $(".spinner-table").attr("style", "display: flex !important; margin-top: 5px;margin-bottom: 5px;");
    abaselect = "aba-3";
    reloadAba("aba-3");
    loadAbaSecretaria();
  });

  $(document).on('click', '#editContabilidade', function () {
    var cod = $("#Id").val();
    $.ajax({
      url: GetURLBaseComplete() + "/Filial/_GetPartialEditcontab/" + cod,
      type: 'GET',
      dataType: 'html',
      success: function (data) {
        $("#editContab").html("");
        $("#editContab").html(data);

        $("#modalContabilidade").modal('show');
      }
    })
  });

  $(document).on('click', '#save', function () {
    var idcontab = $("#ContabilidadeSelect_id").val();
    var contabtext = $("#ContabilidadeSelect_text").val();
    var cod = $("#Id").val();

    var continput = $("#Contabilidade_text").val();
    if (continput) {
      swal({
        title: "Alterar Contabilidade",
        text: "Ao alterar a contabilidade, você removerá todas as permissões da contabilidade atual. \n Autorizo o meu contador a acessar os dados financeiros da minha conta no Unicontábil",
        icon: "warning",
        buttons: ["Cancelar", "Confirmar"],
      })
        .then((continuar) => {
          if (continuar) {
            $.ajax({
              url: GetURLBaseComplete() + "/Filial/EditContabilidade?id=" + cod + "&idcontab=" + idcontab,
              type: 'GET',
              dataType: 'html',
              success: function (data) {
                if (!data.error) {
                  $("#Contabilidade_text").val(contabtext);
                  $("#IdContabilidade").val(idcontab);
                  swal({
                    title: "Sucesso",
                    text: data.message,
                    icon: "success",
                  });
                  $("#modalContabilidade").modal('hide');
                }
                else {
                  swal({
                    title: "Erro",
                    text: data.message,
                    icon: "error",
                  });
                }
              }
            })
          }
        });
    }
    else {
      $.ajax({
        url: GetURLBaseComplete() + "/Filial/EditContabilidade?id=" + cod + "&idcontab=" + idcontab,
        type: 'GET',
        dataType: 'html',
        success: function (data) {
          if (!data.error) {
            $("#Contabilidade_text").val(contabtext);
            $("#IdContabilidade").val(idcontab);
            swal({
              title: "Sucesso",
              text: data.message,
              icon: "success",
            });
            $("#modalContabilidade").modal('hide');
          }
          else {
            swal({
              title: "Erro",
              text: data.message,
              icon: "error",
            });
          }
        }
      })
    }
  });

  $(document).on("keyup", "#CPFUser", function (e) {
    if ($(this).inputmask('unmaskedvalue').length == 11) {
      $.ajax({
        type: 'GET',
        url: GetURLBaseComplete() + "/Contabilidade/VerificaCpf?cpf=" + $(this).inputmask('unmaskedvalue'),
        success: function (data) {
          if (!data.error) {
            if (data.hasCpf) {
              $(".PassUser").attr('hidden', true);
              $(".ConfirmEmail").attr('hidden', true);
              $("#TelefoneUser").val(data.telefone);
              $("#EmailUser").val(data.email);
              $("#ConfirmEmail").val(data.email);
              $("#NomeUser").val(data.nome);
              $("#IdUser").val(data.idUser);
              $("#BtnCadastrar").text('Vincular');


              $("#EmailUser").attr('readonly', true);
              $("#TelefoneUser").attr('readonly', true);
              $("#ConfirmEmail").attr('readonly', true);
              $("#NomeUser").attr('readonly', true);

              Alerta("Atenção", "CPF encontrado no sistema", "green")
            } else {
              //Alerta("Atenção", "CPF não encontrado no sistema", "yellow")
              $(".PassUser").attr('hidden', false);
              $("#EmailUser").attr('readonly', false);
              $("#TelefoneUser").attr('readonly', false);
              $("#ConfirmEmail").attr('readonly', false);
              $("#NomeUser").attr('readonly', false);
              $("#IdUser").val('')
              $(".ConfirmEmail").attr('hidden', false);
              $("#BtnCadastrar").text('Cadastrar e vincular')
            }
          }
        }
      })
    } else {
      $(".PassUser").attr('hidden', false);
      $(".ConfirmEmail").attr('hidden', false);
      $("#BtnCadastrar").val('Cadastrar e vincular')
      $("#EmailUser").attr('readonly', false);
      $("#TelefoneUser").attr('readonly', false);
      $("#ConfirmEmail").attr('readonly', false);
      $("#NomeUser").attr('readonly', false);
      $("#IdUser").val('')
      $(".ConfirmEmail").attr('hidden', false);
      $("#BtnCadastrar").text('Cadastrar e vincular')
    }
  })

  $(document).on("click", "#enviaEmailContab", function () {
    $("#NomeContab").val("");
    $("#EmailContab").val("");
    $("#CPFCNPJContab").val("");

    $("#ModalEmailContab").modal('show');
  });

  $(document).on("click", "#sendEmail", function () {

    let cpfcnpj = $("#CPFCNPJContab").val();

    if (cpfcnpj) {
      cpfcnpj = cpfcnpj.replaceAll(".", "");
      cpfcnpj = cpfcnpj.replaceAll("-", "");
      cpfcnpj = cpfcnpj.replaceAll("/", "");
    }
    var Model = {
      Nome: $("#NomeContab").val(),
      Email: $("#EmailContab").val(),
      CPFCNPJ: cpfcnpj,
    }

    $.ajax({
      url: GetURLBaseComplete() + "/Filial/SendEmailContab/",
      type: 'POST',
      data: Model,
      dataType: 'json',
      success: function (data) {
        if (!data.error) {
          swal({
            title: "Sucesso",
            text: data.message,
            icon: "success",
          });
          $("#ModalEmailContab").modal('hide');
        }
        else {
          swal({
            title: "Erro",
            text: data.message,
            icon: "error",
          });
        }
      }
    })

  });
};

$(document).ready(function () {
  Filial.init();
});

Filial.AdicionarUsuario = function () {
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + "/Filial/_ModalCreateUserSemCpf?IdFilial=" + $("#Id").val(),
    success: function (data) {
      $("#ModalCriarUsuario .modal-body").html(data);

      $('#divCadastro .telefone').inputmask({
        mask: '(99)99999-999[9]',
        removeMaskOnSubmit: true
      });

      $('#divCadastro .CPF').inputmask({
        mask: '999.999.999-99',
        removeMaskOnSubmit: true
      });

      $('#divCadastro .EMAIL').inputmask({
        mask: "*{1,100}@*{1,20}[.*{2,6}][.*{1,2}]",
        greedy: false,
        onBeforePaste: function (pastedValue, opts) {
          pastedValue = pastedValue.toLowerCase();
          return pastedValue.replace("mailto:", "");
        },
        definitions: {
          '*': {
            validator: "[.0-9A-Za-z!#$%&'*+/=?^_`{|}~\-]",
            cardinality: 1,
            casing: "lower"
          }
        }
      });
      $("#ModalCriarUsuario").modal('show');
    }
  })
}

function loadFiltroSocios() {
  const cod = $("#Codigo").val();
  const search = $("#Search").val();
  //if (search) {
  $(".body-filtro-socios-content").html("");
  $(".spinner-table").attr("style", "display: flex !important; margin-top: 5px;margin-bottom: 5px;");
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Filial/GetSocios',
    dataType: 'html',
    data: {
      id: cod,
      filtro: search,
    },
    success: function (data) {
      $(".spinner-table").attr("style", "display: none !important");
      $(".body-filtro-socios-content").html(data);
      //$(".spinner-menu").attr("style", "display: none !important");
    }
  });
  //}
  //else
  //  Alerta("Atenção", "Gentileza preencher o campo pesquisa", "yellow", 6000);

}

function loadFiltroSecretaria() {
  const search = $("#SearchSecretaria").val();
  //if (search) {
  $(".body-filtro-secretaria-content").html("");
  $(".spinner-table").attr("style", "display: flex !important; margin-top: 5px;margin-bottom: 5px;");
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Filial/GetSecretaria',
    dataType: 'html',
    data: {
      filtro: search,
    },
    success: function (data) {
      $(".spinner-table").attr("style", "display: none !important");
      $(".body-filtro-secretaria-content").html(data);
      //$(".spinner-menu").attr("style", "display: none !important");
    }
  });
  //}
  //else
  //  Alerta("Atenção", "Gentileza preencher o campo pesquisa", "yellow", 6000);

}

function reloadAba(aba) {

  let name = "";

  if (!aba)
    aba = "aba-1"

  name = aba;
  if (aba == "aba-1") {
    $("#aba-2").removeClass("active");
    $("#body-aba-2").addClass("hide-body");
    $("#aba-3").removeClass("active");
    $("#body-aba-3").addClass("hide-body");

    $("#aba-1").addClass("active");
    $("#body-aba-1").removeClass("hide-body");

  } else if (aba == "aba-2") {
    $("#aba-1").removeClass("active");
    $("#body-aba-1").addClass("hide-body");

    $("#aba-3").removeClass("active");
    $("#body-aba-3").addClass("hide-body");

    $("#aba-2").addClass("active");
    $("#body-aba-2").removeClass("hide-body");
  }
  else {
    $("#aba-1").removeClass("active");
    $("#body-aba-1").addClass("hide-body");

    $("#aba-2").removeClass("active");
    $("#body-aba-2").addClass("hide-body");

    $("#aba-3").addClass("active");
    $("#body-aba-3").removeClass("hide-body");
  }
}

function loadAbaSocios() {
  const cod = $("#Id").val();
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Filial/GetControlePermissao',
    dataType: 'html',
    data: {
      id: cod
    },
    success: function (data) {
      $("#body-aba-2").html(data);
      $(".spinner-table").attr("style", "display: none !important");
    }
  });
}

function loadAbaSecretaria() {
  const cod = $("#Id").val();
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Filial/GetControlePermissaoSecretaria',
    dataType: 'html',
    data: {
      id: cod
    },
    success: function (data) {
      $("#body-aba-3").html(data);
      $(".spinner-table").attr("style", "display: none !important");
    }
  });
}

function completaCampos() {
  var val = $('#campoCEP').inputmask('unmaskedvalue');


  if (val.length == 8) {
    $.ajax({
      url: 'https://viacep.com.br/ws/' + val + '/json',
      type: 'GET',
      dataType: 'json',
      success: function (data) {
        if (data.erro) {
        } else {
          $('#Logradouro').val(data['logradouro']);
          $('#Complemento').val(data['complemento']);
          $('#Bairro').val(data['bairro']);
          $.ajax({
            type: 'GET',
            url: GetURLBaseComplete() + '/select2/GetUFSelect?term=' + data['uf'],
            success: function (sucessData) {
              console.log(sucessData)
              $("#select2-UFSelectLookup-container").attr('title', sucessData['items'][0]['text'])
              $("#UFSelect_id").val(sucessData['items'][0]['id'])
              $("#UFSelect_text").val(sucessData['items'][0]['text'])
              $('#select2-UFSelectLookup-container').html('')
              $('#select2-UFSelectLookup-container').append(sucessData['items'][0]['text'])
              $.ajax({
                type: 'GET',
                url: GetURLBaseComplete() + '/select2/GetMunicipioSelect?term=' + data['localidade'] + "&id=" + sucessData['items'][0]['id'],
                success: function (sucessDataCidade) {
                  console.log(sucessDataCidade)
                  $("#select2-MunicipioSelect-container").attr('title', sucessDataCidade['items'][0]['text'])
                  $("#MunicipioSelect_id").val(sucessDataCidade['items'][0]['id'])
                  $("#MunicipioSelect_text").val(sucessDataCidade['items'][0]['text'])
                  $('#select2-MunicipioSelectLookup-container').html('')
                  $('#select2-MunicipioSelectLookup-container').append(sucessDataCidade['items'][0]['text'])
                }
              })
            }
          })
        }
      }
    })
  }
}

Filial.SaveVinculo = function (coduser) {
  const codfilial = $("#Id").val();
  const codorg = $("#IdOrganizacao").val();

  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/Filial/SaveVinculo',
    dataType: 'json',
    data: {
      id: coduser,
      codfilial: codfilial,
      codorg: codorg,
    },
    success: function (data) {
      $(".spinner-menu").attr("style", "display: none !important");
      if (!data.Error) {
        loadAbaSocios();
        SweetAlert("Sucesso", data.mensage, "success");
      }
      else {
        SweetAlert("Erro", data.mensage, "danger");
      }
    }
  });
}

Filial.SaveVinculoSecretaria = function (coduser) {
  const codfilial = $("#Id").val();
  const codorg = $("#IdOrganizacao").val();

  if (!coduser)
    coduser = $("#IdUser").val();
  if (coduser) {

    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/Filial/SaveVinculoSecretaria',
      dataType: 'json',
      data: {
        id: coduser,
        codfilial: codfilial,
        codorg: codorg,
      },
      success: function (data) {

        $(".spinner-menu").attr("style", "display: none !important");
        if (!data.Error) {
          $("#ModalCriarUsuario").modal('hide');
          loadAbaSecretaria();
          SweetAlert("Sucesso", data.mensage, "success");

          $(".swal-overlay").attr("style", "display: none !important");
        }
        else {
          SweetAlert("Erro", data.mensage, "danger");
        }
      }
    });
  }
  else {
    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/Filial/CrateUserAndVinciular',
      dataType: 'json',
      data: $("#formCadastroSecretaria").serialize(),
      success: function (data) {

        $(".spinner-menu").attr("style", "display: none !important");
        if (!data.Error) {
          $("#ModalCriarUsuario").modal('hide');
          loadAbaSecretaria();
          SweetAlert("Sucesso", data.mensage, "success");

          $(".swal-overlay").attr("style", "display: none !important");
        }
        else {
          SweetAlert("Erro", data.mensage, "danger");
        }
      }
    });
  }
}

Filial.DeleteAssociado = function (coduser, aba) {
  const codfilial = $("#Id").val();
  const codorg = $("#IdOrganizacao").val();

  $.ajax({
    url: GetURLBaseComplete() + '/Usuarios/DeleteAssociacao',
    dataType: "json",
    type: "POST",
    data: {
      id: coduser,
      codfilial: codfilial,
      codorg: codorg,
    },
    success: function (retorno) {
      if (!retorno.erro) {

        if (abaselect == "aba-2")
          loadAbaSocios();
        else
          loadAbaSecretaria();

        SweetAlert("Sucesso", retorno.mensagem, "success");
      }
      else {
        SweetAlert("Erro", retorno.mensagem, "danger");
      }
    }
  });
}