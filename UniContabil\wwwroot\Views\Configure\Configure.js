﻿var Configure = function () {
};

Configure.Init = function () {
 
};

Configure.AlterarOrgAtiva = function (idFilial) {

  if (idFilial && (idFilial != "0")) {
    idFilial = parseInt(idFilial);

    if (idFilial < 1)
      idFilial = null;

    const obj = {
      idFilial: idFilial
    };

    $.post(GetURLBaseComplete() + "/Account/ChangeOrganizacao", obj, function (data) {
      data = JSON.parse(data);
      if (data.Sucesso)
        location.href = GetURLBaseComplete() +"/ClassificacaoNotasFiscais/Index";
      else if (!data.Sucesso && data.Reload)
        location.reload();
      else
        Alerta("Erro", data.Mensagem, "red", 5000);
    });
  }
  else {
    Alerta("Aviso", "Por favor selecione os campos de organização e filial.", "yellow", 5000);
  }
};


$(document).ready(function () {
  Configure.Init();
});