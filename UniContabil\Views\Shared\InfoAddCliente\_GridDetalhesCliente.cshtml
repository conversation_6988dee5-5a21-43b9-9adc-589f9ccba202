﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure
@model GridDetalhesCliente

<div class="container">
  <div class="row">
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.Nome)
        @Html.TextBoxFor(a => a.Nome, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.Nome)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.CPFCNPJ)
        @Html.TextBoxFor(a => a.CPFCNPJ, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.CPFCNPJ)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.Email)
        @Html.TextBoxFor(a => a.Email, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.Email)
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.CEP)
        @Html.TextBoxFor(a => a.CEP, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.CEP)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.TipoLogradouro)
        @Html.TextBoxFor(a => a.TipoLogradouro, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.TipoLogradouro)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.Logradouro)
        @Html.TextBoxFor(a => a.Logradouro, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.Logradouro)
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.NumeroLogradouro)
        @Html.TextBoxFor(a => a.NumeroLogradouro, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.NumeroLogradouro)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.Complemento)
        @Html.TextBoxFor(a => a.Complemento, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.Complemento)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.Cidade)
        @Html.TextBoxFor(a => a.Cidade, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.Cidade)
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.Estado)
        @Html.TextBoxFor(a => a.Estado, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.Estado)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.TelefoneFixo)
        @Html.TextBoxFor(a => a.TelefoneFixo, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.TelefoneFixo)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.TelefoneCelular)
        @Html.TextBoxFor(a => a.TelefoneCelular, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.TelefoneCelular)
      </div>
    </div>
  </div>
</div>

