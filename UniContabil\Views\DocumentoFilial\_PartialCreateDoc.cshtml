﻿@model UniContabilEntidades.Models.DocumentoFilialCreateModal
@using UniContabil.Infrastructure.Controls

<form asp-action="CreateModal" asp-controller="DocumentoFilial" id="formdoc">

  @Html.HiddenFor(a => a.IdTipoDocumentoFilial)
  @Html.HiddenFor(a => a.ArquivoBase64)
  @Html.HiddenFor(a => a.<PERSON>)

  <div class="row">
    <div class="col-md-8">
      <div class="form-group">
        <label>Nome Documento</label>
        @Html.TextBoxFor(m => m.Nome, new { type = "text", @class = "form-control" })
        @Html.ValidationMessageFor(m => m.Nome)
      </div>
    </div> 
    <div class="col-md-4">
      <div class="form-group">
        <label>Data</label>
        @Html.TextBoxFor(m => m.DataDocumento, new { type = "date", @class = "form-control" })
        @Html.ValidationMessageFor(m => m.DataDocumento)
      </div>
    </div>
  </div>
  <div class="row" >
    <input hidden="hidden" type="file" id="Anexodocumento" accept="image/*,.pdf" class="form-control" />
    <div class="col-md-12" style=" margin: 20px 0;">
      <a id="AddAnexo" class="btn btn-outline-info"><i class="mw-drip-paperclip" style="font-size: 20pt;"></i></a>
    </div>
    <h5 id="titledoc"></h5>
  </div>
  <div class="modal-footer">
    <button class="btn btn-primary col-5" type="button" id="saveDoc">Salvar</button>
  </div>
</form>
