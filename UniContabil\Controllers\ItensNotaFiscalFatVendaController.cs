﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using UniContabil.Infrastructure.Exceptions;

using UniContabilEntidades.Models;
using UniContabilDomain.Services;
using UniContabilEntidades.Models.DataBase;
using Estoque.Infrastructure;

namespace UniContabil.Controllers
{
  [AllowAnonymous]
  public class ItensNotaFiscalFatVendaController : LibController
  {
    private ItensNotaFiscalFatVendaServices ItensNotaFiscalFatVendaServices
    {
      get
      {
        if (_ItensNotaFiscalFatVendaServices == null)
          _ItensNotaFiscalFatVendaServices = new ItensNotaFiscalFatVendaServices(ContextoUsuario.UserLogged);

        return _ItensNotaFiscalFatVendaServices;
      }
    }
    private ItensNotaFiscalFatVendaServices _ItensNotaFiscalFatVendaServices;

    [HttpPost]
    public JsonResult Create(ItensNotaFiscalFatVendaModel item)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        if (ModelState.IsValid)
        {
          ItensNotaFiscalFatVendaServices.Create(item);
          retornoAjax.Titulo = "Sucesso";
          retornoAjax.Mensagem = "Item adicionado com sucesso.";
          retornoAjax.Erro = false;
          return Json(retornoAjax);
        }
        List<string> ListaErro = ModelStateHelper.GetErroList(ViewData.ModelState.Values);
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = String.Join(" <br> ", ListaErro.ToArray());
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public JsonResult Edit(ItensNotaFiscalFatVendaModel item)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        if (ModelState.IsValid)
        {
          ItensNotaFiscalFatVendaServices.Edit(item);
          retornoAjax.Titulo = "Sucesso";
          retornoAjax.Mensagem = "Item Editado com sucesso.";
          retornoAjax.Erro = false;
          return Json(retornoAjax);
        }
        List<string> ListaErro = ModelStateHelper.GetErroList(ViewData.ModelState.Values);
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = String.Join(" <br> ", ListaErro.ToArray());
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public JsonResult Delete(int Codigo)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        ItensNotaFiscalFatVendaServices.Delete(Codigo);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Item deletado com sucesso.";
        retornoAjax.Erro = false;
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax);
      }


    }

 
  }
}