﻿@{
  ViewData["Title"] = "Grupos";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

<script src="~/Views/GrupoUsuario/GrupoUsuario.js"></script>

<div style="display: flex">
  <div class="card">
    <div class="card-header">
      <div class="card-title">Lista de Grupos</div>
      <div class="card-options">
      </div>
    </div>
    <div class="card-body">
      <div class="group-list" style="margin-top: 7.5px;">
      </div>
    </div>
    <div class="card-footer">
    </div>
  </div>
  <div class="col-9">
    <div class="table-group card">
      <div class="card-header">
        <div class="card-title">Gerenciar Grupo</div>
        <div class="card-options">
          <button class="btn btn-outline-primary btn-sm" style="height: 31px" data-toggle="modal" data-target="#modalNovoGrupo">+ Novo Grupo</button>
        </div>
      </div>
      <div style="padding: 15px">
        <ul class="nav nav-tabs">
          <li class="nav-item">
            <a class="nav-link active" id="group-users">Usuários</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" id="group-permissions">Permissões</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" id="group-delete" style="color: red !important">Excluir Grupo</a>
          </li>
        </ul>
        <div class="group-detail">
        </div>
        <div class="group-permissions" style="max-height: calc(100vh - 290px); overflow-y: auto;">
        </div>
        <div class="justify-content-center spinner-menu" style="display:none !important;">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Carregando...</span>
          </div>
        </div>
      </div>
      <div class="card-footer">
      </div>
    </div>

  </div>
</div>

@{
  await Html.RenderPartialAsync("_ModalNovoGrupo");
}
