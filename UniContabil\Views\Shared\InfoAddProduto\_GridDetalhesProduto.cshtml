﻿@using UniContabilEntidades.Models
@using UniContabil.Infrastructure
@model GridDetalhesProduto

<div class="container">
  <div class="row">
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.<PERSON>digo)
        @Html.TextBoxFor(a => a.<PERSON>digo, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.Codigo)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.Descricao)
        @Html.TextBoxFor(a => a.<PERSON>c<PERSON>, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.Descricao)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.CodigoBarras)
        @Html.TextBoxFor(a => a.<PERSON>, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.CodigoBarras)
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.UnidadeMedida)
        @Html.TextBoxFor(a => a.UnidadeMedida, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.UnidadeMedida)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.DescricaoGrupo)
        @Html.TextBoxFor(a => a.DescricaoGrupo, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.DescricaoGrupo)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.TipoProduto)
        @Html.TextBoxFor(a => a.TipoProduto, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.TipoProduto)
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.ContraLoteFormatado)
        @Html.TextBoxFor(a => a.ContraLoteFormatado, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.ContraLoteFormatado)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.ConsumoMedio)
        @Html.TextBoxFor(a => a.ConsumoMedio, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.ConsumoMedio)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.CustoPadrao)
        @Html.TextBoxFor(a => a.CustoPadrao, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.CustoPadrao)
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.ContraLote)
        @Html.TextBoxFor(a => a.ContraLote, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.ContraLote)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.EstoqueMinimo)
        @Html.TextBoxFor(a => a.EstoqueMinimo, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.EstoqueMinimo)
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.DiasReposicao)
        @Html.TextBoxFor(a => a.DiasReposicao, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.DiasReposicao)
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="form-group">
        @Html.LabelFor(a => a.DataCadastroFormatada)
        @Html.TextBoxFor(a => a.DataCadastroFormatada, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.DataCadastroFormatada)
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12">
      <div class="form-group">
        @Html.LabelFor(a => a.DescricaoEstendida)
        @Html.TextAreaFor(a => a.DescricaoEstendida, new { @class = "form-control" })
        @Html.ValidationMessageFor(a => a.DescricaoEstendida)
      </div>
    </div>
  </div>
</div>

