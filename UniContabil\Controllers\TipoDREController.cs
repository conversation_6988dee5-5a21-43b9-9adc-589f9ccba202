﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using UniContabilDomain.Services;
using UniContabil.Infrastructure;
using UniContabil.Infrastructure.Controls;
using X.PagedList;
using Newtonsoft.Json;

using UniContabilEntidades.Models;
using UniContabilEntidades.Models.DataBase;

namespace UniContabil.Controllers
{
  public class TipoDREController : LibController
  {
    public TipoDREController()
    { }

    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Index)]
    public ActionResult Index(int page = 1)
    {
      try
      {
        IPagedList<TipoDREModel> List = new TipoDREService().GetPagedList(page);
        ViewBag.PageItems = List;
        return View();
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }
    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create()
    {
      return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Create)]
    public ActionResult Create(TipoDREModel tipoDREModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          var model = tipoDREModel.ToDatabase();
          new TipoDREService().Create(model);

          return RedirectToAction(nameof(Index));
        }
        else
        {
          return View(tipoDREModel);
        }
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(tipoDREModel);
      }
    }
    [HttpGet]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(int id)
    {
      try
      {
        TipoDREModel tipoDREModel = new TipoDREModel().FromDatabase(new TipoDREService().GetById(id));

        if (tipoDREModel == null)
        {
          return NotFound();
        }

        return View(tipoDREModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Edit)]
    public ActionResult Edit(TipoDREModel tipoDREModel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          var model = tipoDREModel.ToDatabase();
          new TipoDREService().Edit(model);
          return RedirectToAction(nameof(Index));
        }
        return View(tipoDREModel);
      }
      catch (Exception Ex)
      {
        MessageAdd(new Message(MessageType.Error, Ex.Message));
        return View(tipoDREModel);
      }
    }

    [HttpPost]
    [NumeroFuncaoAttr(numeroFuncao: (int)TipoFuncao.Delete)]
    public string Delete(int id)
    {
      try
      {
        TipoDREService Service = new TipoDREService();
        var model = Service.GetById(id);
        Service.Delete(model);
        return JsonConvert.SerializeObject(new { Sucesso = true });
      }
      catch (Exception Ex)
      {
        return JsonConvert.SerializeObject(new { Sucesso = false, Mensagem = string.Format("Não foi possível deletar. Erro {0}.", Ex.HResult) });
      }
    }


  }
}
