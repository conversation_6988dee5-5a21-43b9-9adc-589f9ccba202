/*!
* inputmask.date.extensions.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2017 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 3.3.11
*/

!function(e){"function"==typeof define&&define.amd?define(["./dependencyLibs/inputmask.dependencyLib","./inputmask"],e):"object"==typeof exports?module.exports=e(require("./dependencyLibs/inputmask.dependencyLib"),require("./inputmask")):e(window.dependencyLib||jQuery,window.Inputmask)}(function(e,a){function r(e){return isNaN(e)||29===new Date(e,2,0).getDate()}return a.extendAliases({"dd/mm/yyyy":{mask:"1/2/y",placeholder:"dd/mm/yyyy",regex:{val1pre:new RegExp("[0-3]"),val1:new RegExp("0[1-9]|[12][0-9]|3[01]"),val2pre:function(e){var r=a.escapeRegex.call(this,e);return new RegExp("((0[1-9]|[12][0-9]|3[01])"+r+"[01])")},val2:function(e){var r=a.escapeRegex.call(this,e);return new RegExp("((0[1-9]|[12][0-9])"+r+"(0[1-9]|1[012]))|(30"+r+"(0[13-9]|1[012]))|(31"+r+"(0[13578]|1[02]))")}},leapday:"29/02/",separator:"/",yearrange:{minyear:1900,maxyear:2099},isInYearRange:function(e,a,r){if(isNaN(e))return!1;var t=parseInt(e.concat(a.toString().slice(e.length))),n=parseInt(e.concat(r.toString().slice(e.length)));return!isNaN(t)&&(a<=t&&t<=r)||!isNaN(n)&&(a<=n&&n<=r)},determinebaseyear:function(e,a,r){var t=(new Date).getFullYear();if(e>t)return e;if(a<t){for(var n=a.toString().slice(0,2),y=a.toString().slice(2,4);a<n+r;)n--;var i=n+y;return e>i?e:i}if(e<=t&&t<=a){for(var s=t.toString().slice(0,2);a<s+r;)s--;var o=s+r;return o<e?e:o}return t},onKeyDown:function(r,t,n,y){var i=e(this);if(r.ctrlKey&&r.keyCode===a.keyCode.RIGHT){var s=new Date;i.val(s.getDate().toString()+(s.getMonth()+1).toString()+s.getFullYear().toString()),i.trigger("setvalue")}},getFrontValue:function(e,a,r){for(var t=0,n=0,y=0;y<e.length&&"2"!==e.charAt(y);y++){var i=r.definitions[e.charAt(y)];i?(t+=n,n=i.cardinality):n++}return a.join("").substr(t,n)},postValidation:function(e,a,t){var n,y,i=e.join("");return 0===t.mask.indexOf("y")?(y=i.substr(0,4),n=i.substring(4,10)):(y=i.substring(6,10),n=i.substr(0,6)),a&&(n!==t.leapday||r(y))},definitions:{1:{validator:function(e,a,r,t,n){var y=n.regex.val1.test(e);return t||y||e.charAt(1)!==n.separator&&-1==="-./".indexOf(e.charAt(1))||!(y=n.regex.val1.test("0"+e.charAt(0)))?y:(a.buffer[r-1]="0",{refreshFromBuffer:{start:r-1,end:r},pos:r,c:e.charAt(0)})},cardinality:2,prevalidator:[{validator:function(e,a,r,t,n){var y=e;isNaN(a.buffer[r+1])||(y+=a.buffer[r+1]);var i=1===y.length?n.regex.val1pre.test(y):n.regex.val1.test(y);if(i&&a.validPositions[r]&&(n.regex.val2(n.separator).test(e+a.validPositions[r].input)||(a.validPositions[r].input="0"===e?"1":"0")),!t&&!i){if(i=n.regex.val1.test(e+"0"))return a.buffer[r]=e,a.buffer[++r]="0",{pos:r,c:"0"};if(i=n.regex.val1.test("0"+e))return a.buffer[r]="0",r++,{pos:r}}return i},cardinality:1}]},2:{validator:function(e,a,r,t,n){var y=n.getFrontValue(a.mask,a.buffer,n);-1!==y.indexOf(n.placeholder[0])&&(y="01"+n.separator);var i=n.regex.val2(n.separator).test(y+e);return t||i||e.charAt(1)!==n.separator&&-1==="-./".indexOf(e.charAt(1))||!(i=n.regex.val2(n.separator).test(y+"0"+e.charAt(0)))?i:(a.buffer[r-1]="0",{refreshFromBuffer:{start:r-1,end:r},pos:r,c:e.charAt(0)})},cardinality:2,prevalidator:[{validator:function(e,a,r,t,n){isNaN(a.buffer[r+1])||(e+=a.buffer[r+1]);var y=n.getFrontValue(a.mask,a.buffer,n);-1!==y.indexOf(n.placeholder[0])&&(y="01"+n.separator);var i=1===e.length?n.regex.val2pre(n.separator).test(y+e):n.regex.val2(n.separator).test(y+e);return i&&a.validPositions[r]&&(n.regex.val2(n.separator).test(e+a.validPositions[r].input)||(a.validPositions[r].input="0"===e?"1":"0")),t||i||!(i=n.regex.val2(n.separator).test(y+"0"+e))?i:(a.buffer[r]="0",r++,{pos:r})},cardinality:1}]},y:{validator:function(e,a,r,t,n){return n.isInYearRange(e,n.yearrange.minyear,n.yearrange.maxyear)},cardinality:4,prevalidator:[{validator:function(e,a,r,t,n){var y=n.isInYearRange(e,n.yearrange.minyear,n.yearrange.maxyear);if(!t&&!y){var i=n.determinebaseyear(n.yearrange.minyear,n.yearrange.maxyear,e+"0").toString().slice(0,1);if(y=n.isInYearRange(i+e,n.yearrange.minyear,n.yearrange.maxyear))return a.buffer[r++]=i.charAt(0),{pos:r};if(i=n.determinebaseyear(n.yearrange.minyear,n.yearrange.maxyear,e+"0").toString().slice(0,2),y=n.isInYearRange(i+e,n.yearrange.minyear,n.yearrange.maxyear))return a.buffer[r++]=i.charAt(0),a.buffer[r++]=i.charAt(1),{pos:r}}return y},cardinality:1},{validator:function(e,a,r,t,n){var y=n.isInYearRange(e,n.yearrange.minyear,n.yearrange.maxyear);if(!t&&!y){var i=n.determinebaseyear(n.yearrange.minyear,n.yearrange.maxyear,e).toString().slice(0,2);if(y=n.isInYearRange(e[0]+i[1]+e[1],n.yearrange.minyear,n.yearrange.maxyear))return a.buffer[r++]=i.charAt(1),{pos:r};if(i=n.determinebaseyear(n.yearrange.minyear,n.yearrange.maxyear,e).toString().slice(0,2),y=n.isInYearRange(i+e,n.yearrange.minyear,n.yearrange.maxyear))return a.buffer[r-1]=i.charAt(0),a.buffer[r++]=i.charAt(1),a.buffer[r++]=e.charAt(0),{refreshFromBuffer:{start:r-3,end:r},pos:r}}return y},cardinality:2},{validator:function(e,a,r,t,n){return n.isInYearRange(e,n.yearrange.minyear,n.yearrange.maxyear)},cardinality:3}]}},insertMode:!1,autoUnmask:!1},"mm/dd/yyyy":{placeholder:"mm/dd/yyyy",alias:"dd/mm/yyyy",regex:{val2pre:function(e){var r=a.escapeRegex.call(this,e);return new RegExp("((0[13-9]|1[012])"+r+"[0-3])|(02"+r+"[0-2])")},val2:function(e){var r=a.escapeRegex.call(this,e);return new RegExp("((0[1-9]|1[012])"+r+"(0[1-9]|[12][0-9]))|((0[13-9]|1[012])"+r+"30)|((0[13578]|1[02])"+r+"31)")},val1pre:new RegExp("[01]"),val1:new RegExp("0[1-9]|1[012]")},leapday:"02/29/",onKeyDown:function(r,t,n,y){var i=e(this);if(r.ctrlKey&&r.keyCode===a.keyCode.RIGHT){var s=new Date;i.val((s.getMonth()+1).toString()+s.getDate().toString()+s.getFullYear().toString()),i.trigger("setvalue")}}},"yyyy/mm/dd":{mask:"y/1/2",placeholder:"yyyy/mm/dd",alias:"mm/dd/yyyy",leapday:"/02/29",onKeyDown:function(r,t,n,y){var i=e(this);if(r.ctrlKey&&r.keyCode===a.keyCode.RIGHT){var s=new Date;i.val(s.getFullYear().toString()+(s.getMonth()+1).toString()+s.getDate().toString()),i.trigger("setvalue")}}},"dd.mm.yyyy":{mask:"1.2.y",placeholder:"dd.mm.yyyy",leapday:"29.02.",separator:".",alias:"dd/mm/yyyy"},"dd-mm-yyyy":{mask:"1-2-y",placeholder:"dd-mm-yyyy",leapday:"29-02-",separator:"-",alias:"dd/mm/yyyy"},"mm.dd.yyyy":{mask:"1.2.y",placeholder:"mm.dd.yyyy",leapday:"02.29.",separator:".",alias:"mm/dd/yyyy"},"mm-dd-yyyy":{mask:"1-2-y",placeholder:"mm-dd-yyyy",leapday:"02-29-",separator:"-",alias:"mm/dd/yyyy"},"yyyy.mm.dd":{mask:"y.1.2",placeholder:"yyyy.mm.dd",leapday:".02.29",separator:".",alias:"yyyy/mm/dd"},"yyyy-mm-dd":{mask:"y-1-2",placeholder:"yyyy-mm-dd",leapday:"-02-29",separator:"-",alias:"yyyy/mm/dd"},datetime:{mask:"1/2/y h:s",placeholder:"dd/mm/yyyy hh:mm",alias:"dd/mm/yyyy",regex:{hrspre:new RegExp("[012]"),hrs24:new RegExp("2[0-4]|1[3-9]"),hrs:new RegExp("[01][0-9]|2[0-4]"),ampm:new RegExp("^[a|p|A|P][m|M]"),mspre:new RegExp("[0-5]"),ms:new RegExp("[0-5][0-9]")},timeseparator:":",hourFormat:"24",definitions:{h:{validator:function(e,a,r,t,n){if("24"===n.hourFormat&&24===parseInt(e,10))return a.buffer[r-1]="0",a.buffer[r]="0",{refreshFromBuffer:{start:r-1,end:r},c:"0"};var y=n.regex.hrs.test(e);if(!t&&!y&&(e.charAt(1)===n.timeseparator||-1!=="-.:".indexOf(e.charAt(1)))&&(y=n.regex.hrs.test("0"+e.charAt(0))))return a.buffer[r-1]="0",a.buffer[r]=e.charAt(0),r++,{refreshFromBuffer:{start:r-2,end:r},pos:r,c:n.timeseparator};if(y&&"24"!==n.hourFormat&&n.regex.hrs24.test(e)){var i=parseInt(e,10);return 24===i?(a.buffer[r+5]="a",a.buffer[r+6]="m"):(a.buffer[r+5]="p",a.buffer[r+6]="m"),(i-=12)<10?(a.buffer[r]=i.toString(),a.buffer[r-1]="0"):(a.buffer[r]=i.toString().charAt(1),a.buffer[r-1]=i.toString().charAt(0)),{refreshFromBuffer:{start:r-1,end:r+6},c:a.buffer[r]}}return y},cardinality:2,prevalidator:[{validator:function(e,a,r,t,n){var y=n.regex.hrspre.test(e);return t||y||!(y=n.regex.hrs.test("0"+e))?y:(a.buffer[r]="0",r++,{pos:r})},cardinality:1}]},s:{validator:"[0-5][0-9]",cardinality:2,prevalidator:[{validator:function(e,a,r,t,n){var y=n.regex.mspre.test(e);return t||y||!(y=n.regex.ms.test("0"+e))?y:(a.buffer[r]="0",r++,{pos:r})},cardinality:1}]},t:{validator:function(e,a,r,t,n){return n.regex.ampm.test(e+"m")},casing:"lower",cardinality:1}},insertMode:!1,autoUnmask:!1},datetime12:{mask:"1/2/y h:s t\\m",placeholder:"dd/mm/yyyy hh:mm xm",alias:"datetime",hourFormat:"12"},"mm/dd/yyyy hh:mm xm":{mask:"1/2/y h:s t\\m",placeholder:"mm/dd/yyyy hh:mm xm",alias:"datetime12",regex:{val2pre:function(e){var r=a.escapeRegex.call(this,e);return new RegExp("((0[13-9]|1[012])"+r+"[0-3])|(02"+r+"[0-2])")},val2:function(e){var r=a.escapeRegex.call(this,e);return new RegExp("((0[1-9]|1[012])"+r+"(0[1-9]|[12][0-9]))|((0[13-9]|1[012])"+r+"30)|((0[13578]|1[02])"+r+"31)")},val1pre:new RegExp("[01]"),val1:new RegExp("0[1-9]|1[012]")},leapday:"02/29/",onKeyDown:function(r,t,n,y){var i=e(this);if(r.ctrlKey&&r.keyCode===a.keyCode.RIGHT){var s=new Date;i.val((s.getMonth()+1).toString()+s.getDate().toString()+s.getFullYear().toString()),i.trigger("setvalue")}}},"hh:mm t":{mask:"h:s t\\m",placeholder:"hh:mm xm",alias:"datetime",hourFormat:"12"},"h:s t":{mask:"h:s t\\m",placeholder:"hh:mm xm",alias:"datetime",hourFormat:"12"},"hh:mm:ss":{mask:"h:s:s",placeholder:"hh:mm:ss",alias:"datetime",autoUnmask:!1},"hh:mm":{mask:"h:s",placeholder:"hh:mm",alias:"datetime",autoUnmask:!1},date:{alias:"dd/mm/yyyy"},"mm/yyyy":{mask:"1/y",placeholder:"mm/yyyy",leapday:"donotuse",separator:"/",alias:"mm/dd/yyyy"},shamsi:{regex:{val2pre:function(e){var r=a.escapeRegex.call(this,e);return new RegExp("((0[1-9]|1[012])"+r+"[0-3])")},val2:function(e){var r=a.escapeRegex.call(this,e);return new RegExp("((0[1-9]|1[012])"+r+"(0[1-9]|[12][0-9]))|((0[1-9]|1[012])"+r+"30)|((0[1-6])"+r+"31)")},val1pre:new RegExp("[01]"),val1:new RegExp("0[1-9]|1[012]")},yearrange:{minyear:1300,maxyear:1499},mask:"y/1/2",leapday:"/12/30",placeholder:"yyyy/mm/dd",alias:"mm/dd/yyyy",clearIncomplete:!0},"yyyy-mm-dd hh:mm:ss":{mask:"y-1-2 h:s:s",placeholder:"yyyy-mm-dd hh:mm:ss",alias:"datetime",separator:"-",leapday:"-02-29",regex:{val2pre:function(e){var r=a.escapeRegex.call(this,e);return new RegExp("((0[13-9]|1[012])"+r+"[0-3])|(02"+r+"[0-2])")},val2:function(e){var r=a.escapeRegex.call(this,e);return new RegExp("((0[1-9]|1[012])"+r+"(0[1-9]|[12][0-9]))|((0[13-9]|1[012])"+r+"30)|((0[13578]|1[02])"+r+"31)")},val1pre:new RegExp("[01]"),val1:new RegExp("0[1-9]|1[012]")},onKeyDown:function(e,a,r,t){}}}),a});