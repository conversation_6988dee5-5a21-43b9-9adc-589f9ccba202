﻿@{
  ViewData["Title"] = "Classificação Documento";
  Layout = "~/Views/Shared/_Layout.cshtml";
  string controllerName = this.ViewContext.RouteData.Values["controller"].ToString();
}

<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="~/Views/ClassificacaoDocumentos/ClassificacaoDocumentos.js?wre=rer"></script>

<div class="card">
  <div class="card-body" style="max-height: 100vh !important;">
    <div class="row">
      <div class="col-md-4" style="overflow:auto;">
        <div class="row">
          <div class="form-group">
            <div class="col-md-12">
              <input type="radio" name="Tipo" class="custom-radio" value="1" id="Tipo_1" />
              <label>Receita</label>
              <input type="radio" name="Tipo" class="custom-radio" value="2" id="Tipo_2" checked="checked" />
              <label>Despesa</label>
            </div>
          </div>
        </div>
        <div id="tree" style=" max-height: calc(100vh - 100px);">
        </div>
      </div>
      <div class="col-md-8">
        <div id="editpartial">

        </div>
      </div>
    </div>
  </div>
</div>


